package com.nymbl.config.service;

import com.nymbl.Application;
import com.nymbl.config.model.ShortUrl;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.io.IOException;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;

@RunWith(SpringJUnit4ClassRunner.class)
class ShortUrlServiceTest {

    @Mock
    private RedisTemplate<String, ShortUrl> redisTemplate;

    @Mock
    private ValueOperations<String, ShortUrl> valueOperations;

    private ShortUrlService shortUrlService;



    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
        Mockito.when(redisTemplate.opsForValue()).thenReturn(valueOperations);
        Mockito.doNothing().when(valueOperations).set(anyString(), any(ShortUrl.class));
        shortUrlService = new ShortUrlService(redisTemplate);
        shortUrlService.setBaseUrl("http://localhost:8080/");
    }

    @AfterEach
    void tearDown() {
    }

    @Test
    void createShortUrl() {

        String testUrl = "http://google.com/blabla";
        try {
            ShortUrl newUrl = shortUrlService.createShortUrl(testUrl);
            assertEquals(testUrl, newUrl.getUrl());
        } catch (IOException e) {
            e.printStackTrace();
        }
    }


    @Test
    void getShortUrl() {
    }
}

