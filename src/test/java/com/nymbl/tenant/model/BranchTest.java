package com.nymbl.tenant.model;

import com.nymbl.master.model.ClearingHouseMain;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertEquals;

class BranchTest {

    private Branch branchUnderTest;

    @BeforeEach
    void setUp() {
        branchUnderTest = new Branch();
        ClearingHouse clearingHouse = new ClearingHouse();
        ClearingHouseMain clearingHouseMain = new ClearingHouseMain();
        clearingHouse.setClearingHouse(clearingHouseMain);
        branchUnderTest.setInClearingHouse(clearingHouse);
        branchUnderTest.setOutClearingHouse(clearingHouse);
    }

    @Test
    void testSetId() {
        // Setup
        final Long id = 0L;

        // Run the test
        branchUnderTest.setId(id);

        // Verify the results
        assertEquals(id,branchUnderTest.getId());
    }

    @Test
    void testSetActive() {
        // Setup
        final Boolean active = false;

        // Run the test
        branchUnderTest.setActive(active);

        // Verify the results
        assertEquals(active,branchUnderTest.getActive());
    }

    @Test
    void testSetName() {
        // Setup
        final String name = "name";

        // Run the test
        branchUnderTest.setName(name);

        // Verify the results
        assertEquals(name, branchUnderTest.getName());
    }

    @Test
    void testSetCode() {
        // Setup
        final String code = "code";

        // Run the test
        branchUnderTest.setCode(code);

        // Verify the results
        assertEquals(code,branchUnderTest.getCode());
    }

    @Test
    void testSetTagLine() {
        // Setup
        final String tagLine = "tagLine";

        // Run the test
        branchUnderTest.setTagLine(tagLine);

        // Verify the results
        assertEquals(tagLine,branchUnderTest.getTagLine());
    }

    @Test
    void testSetNpi() {
        // Setup
        final String npi = "npi";

        // Run the test
        branchUnderTest.setNpi(npi);

        // Verify the results
        assertEquals(npi,branchUnderTest.getNpi());
    }

    @Test
    void testSetOtherId1() {
        // Setup
        final String otherId1 = "otherId1";

        // Run the test
        branchUnderTest.setOtherId1(otherId1);

        // Verify the results
        assertEquals(otherId1,branchUnderTest.getOtherId1());
    }

    @Test
    void testSetOtherId2() {
        // Setup
        final String otherId2 = "otherId2";

        // Run the test
        branchUnderTest.setOtherId2(otherId2);

        // Verify the results
        assertEquals(otherId2,branchUnderTest.getOtherId2());
    }

    @Test
    void testSetStreetAddress() {
        // Setup
        final String streetAddress = "streetAddress";

        // Run the test
        branchUnderTest.setStreetAddress(streetAddress);

        // Verify the results
        assertEquals(streetAddress,branchUnderTest.getStreetAddress());
    }

    @Test
    void testSetPoBox() {
        // Setup
        final String poBox = "poBox";

        // Run the test
        branchUnderTest.setPoBox(poBox);

        // Verify the results
        assertEquals(poBox,branchUnderTest.getPoBox());
    }

    @Test
    void testSetCity() {
        // Setup
        final String city = "city";

        // Run the test
        branchUnderTest.setCity(city);

        // Verify the results
        assertEquals(city,branchUnderTest.getCity());
    }

    @Test
    void testSetState() {
        // Setup
        final String state = "state";

        // Run the test
        branchUnderTest.setState(state);

        // Verify the results
        assertEquals(state,branchUnderTest.getState());
    }

    @Test
    void testSetZipcode() {
        // Setup
        final String zipcode = "zipcode";

        // Run the test
        branchUnderTest.setZipcode(zipcode);

        // Verify the results
        assertEquals(zipcode,branchUnderTest.getZipcode());
    }

    @Test
    void testSetPhoneNumber() {
        // Setup
        final String phoneNumber = "phoneNumber";

        // Run the test
        branchUnderTest.setPhoneNumber(phoneNumber);

        // Verify the results
        assertEquals(phoneNumber, branchUnderTest.getPhoneNumber());
    }

    @Test
    void testSetFaxNumber() {
        // Setup
        final String faxNumber = "faxNumber";

        // Run the test
        branchUnderTest.setFaxNumber(faxNumber);

        // Verify the results
    }

    @Test
    void testSetBillingCompanyName() {
        // Setup
        final String billingCompanyName = "billingCompanyName";

        // Run the test
        branchUnderTest.setBillingCompanyName(billingCompanyName);

        // Verify the results
    }

    @Test
    void testSetBillingStreetAddress() {
        // Setup
        final String billingStreetAddress = "billingStreetAddress";

        // Run the test
        branchUnderTest.setBillingStreetAddress(billingStreetAddress);

        // Verify the results
    }

    @Test
    void testSetBillingCity() {
        // Setup
        final String billingCity = "billingCity";

        // Run the test
        branchUnderTest.setBillingCity(billingCity);

        // Verify the results
    }

    @Test
    void testSetBillingState() {
        // Setup
        final String billingState = "billingState";

        // Run the test
        branchUnderTest.setBillingState(billingState);

        // Verify the results
    }

    @Test
    void testSetBillingZipcode() {
        // Setup
        final String billingZipcode = "billingZipcode";

        // Run the test
        branchUnderTest.setBillingZipcode(billingZipcode);

        // Verify the results
    }

    @Test
    void testSetBillingPhoneNumber() {
        // Setup
        final String billingPhoneNumber = "billingPhoneNumber";

        // Run the test
        branchUnderTest.setBillingPhoneNumber(billingPhoneNumber);

        // Verify the results
    }

    @Test
    void testSetBillingFaxNumber() {
        // Setup
        final String billingFaxNumber = "billingFaxNumber";

        // Run the test
        branchUnderTest.setBillingFaxNumber(billingFaxNumber);

        // Verify the results
    }

    @Test
    void testSetHostUrl() {
        // Setup
        final String hostUrl = "hostUrl";

        // Run the test
        branchUnderTest.getOutClearingHouse().getClearingHouse().setHostUrl(hostUrl);

        // Verify the results
    }

    @Test
    void testSetAccountNumber() {
        // Setup
        final String accountNumber = "accountNumber";

        // Run the test
        branchUnderTest.getOutClearingHouse().setAccountNumber(accountNumber);

        // Verify the results
    }

    @Test
    void testSetPassword() {
        // Setup
        final String password = "password";

        // Run the test
        branchUnderTest.getOutClearingHouse().setPassword(password);

        // Verify the results
    }

    @Test
    void testSetHmacKey() {
        // Setup
        final String hmacKey = "hmacKey";

        // Run the test
        branchUnderTest.getOutClearingHouse().setHmacKey(hmacKey);

        // Verify the results
    }

    @Test
    void testSetClearingHouseActive() {
        // Setup
        final Boolean active = false;

        // Run the test
        branchUnderTest.getOutClearingHouse().setActive(active);

        // Verify the results
    }

    @Test
    void testSetRestUser() {
        // Setup
        final String restUser = "restUser";

        // Run the test
        branchUnderTest.getOutClearingHouse().setRestUser(restUser);

        // Verify the results
    }

    @Test
    void testSetRestPassword() {
        // Setup
        final String restPassword = "restPassword";

        // Run the test
        branchUnderTest.getOutClearingHouse().setRestPassword(restPassword);

        // Verify the results
    }

    @Test
    void testSetSendNotifications() {
        // Setup
        final Boolean sendNotifications = false;

        // Run the test
        branchUnderTest.setSendNotifications(sendNotifications);

        // Verify the results
    }

    @Test
    void testSetAdditionalText() {
        // Setup
        final String additionalText = "additionalText";

        // Run the test
        branchUnderTest.setAdditionalText(additionalText);

        // Verify the results
    }

    @Test
    void testSetTaxId() {
        // Setup
        final String taxId = "taxId";

        // Run the test
        branchUnderTest.setTaxId(taxId);

        // Verify the results
    }

    @Test
    void testSetTaxIdType() {
        // Setup
        final String taxIdType = "taxIdType";

        // Run the test
        branchUnderTest.setTaxIdType(taxIdType);

        // Verify the results
    }

    @Test
    void testSetHideCompanyName() {
        // Setup
        final Boolean hideCompanyName = false;

        // Run the test
        branchUnderTest.setHideCompanyName(hideCompanyName);

        // Verify the results
    }

    @Test
    void testSetHideServiceFacilityLocation() {
        // Setup
        final Boolean hideServiceFacilityLocation = false;

        // Run the test
        branchUnderTest.setHideServiceFacilityLocation(hideServiceFacilityLocation);

        // Verify the results
    }
}
