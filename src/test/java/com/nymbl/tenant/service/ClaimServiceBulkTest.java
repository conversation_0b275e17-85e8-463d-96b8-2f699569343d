package com.nymbl.tenant.service;

import com.nymbl.config.exception.X12Exception;
import com.nymbl.config.x12.x837.Factory837;
import com.nymbl.config.x12.x837.Factory837Parameters;
import com.nymbl.config.x12.x837.X12Claim;
import com.nymbl.tenant.model.*;
import com.nymbl.tenant.repository.*;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.mockito.MockitoAnnotations.openMocks;

class ClaimServiceBulkTest {

    @Mock
    private ClaimRepository claimRepository;

    @Mock
    private BulkClaimJobService bulkClaimJobService;

    @Mock
    private Factory837 factory837;

    @Mock
    private SystemSettingService systemSettingService;

    @Mock
    private BranchService branchService;

    @Mock
    private ClaimFileService claimFileService;

    @Mock
    private ClaimSubmissionService claimSubmissionService;

    @Mock
    private NymblStatusService nymblStatusService;

    @InjectMocks
    private ClaimService claimService;

    @BeforeEach
    void setUp() {
        openMocks(this);
        // Manually set the bulkClaimJobService since it's injected via setter injection
        claimService.setBulkClaimJobService(bulkClaimJobService);
    }

    @Test
    void testValidateBulkSubmission_ValidClaims() {
        // Setup
        List<Long> claimIds = Arrays.asList(1L, 2L);

        Claim claim1 = new Claim();
        claim1.setId(1L);
        claim1.setBillingBranchId(1L);

        PatientInsurance patientInsurance1 = new PatientInsurance();
        InsuranceCompany insuranceCompany1 = new InsuranceCompany();
        insuranceCompany1.setId(1L);
        patientInsurance1.setInsuranceCompany(insuranceCompany1);

        claim1.setResponsiblePatientInsurance(patientInsurance1);

        Claim claim2 = new Claim();
        claim2.setId(2L);
        claim2.setBillingBranchId(1L);

        PatientInsurance patientInsurance2 = new PatientInsurance();
        InsuranceCompany insuranceCompany2 = new InsuranceCompany();
        insuranceCompany2.setId(1L);
        patientInsurance2.setInsuranceCompany(insuranceCompany2);

        claim2.setResponsiblePatientInsurance(patientInsurance2);

        when(claimRepository.findAllById(claimIds)).thenReturn(Arrays.asList(claim1, claim2));

        // Execute
        List<String> errors = claimService.validateBulkSubmission(claimIds);

        // Verify
        assertTrue(errors.isEmpty());
        verify(claimRepository, times(1)).findAllById(claimIds);
    }

    @Test
    void testValidateBulkSubmission_EmptyClaimIds() {
        // Setup
        List<Long> claimIds = Collections.emptyList();

        // Execute
        List<String> errors = claimService.validateBulkSubmission(claimIds);

        // Verify
        assertEquals(1, errors.size());
        assertEquals("No claims selected for bulk submission", errors.get(0));
    }

    @Test
    void testValidateBulkSubmission_SingleClaim() {
        // Setup
        List<Long> claimIds = Collections.singletonList(1L);

        // Execute
        List<String> errors = claimService.validateBulkSubmission(claimIds);

        // Verify
        assertEquals(1, errors.size());
        assertEquals("Bulk submission requires at least 2 claims", errors.get(0));
    }

    @Test
    void testValidateBulkSubmission_ClaimNotFound() {
        // Setup
        List<Long> claimIds = Arrays.asList(1L, 2L);

        when(claimRepository.findById(1L)).thenReturn(Optional.empty());
        when(claimRepository.findById(2L)).thenReturn(Optional.empty());

        // Execute
        List<String> errors = claimService.validateBulkSubmission(claimIds);

        // Verify
        assertEquals(3, errors.size()); // Updated to match actual count
        assertTrue(errors.contains("Claim with ID 1 not found"));
        assertTrue(errors.contains("Claim with ID 2 not found"));
        assertTrue(errors.contains("No valid claims found")); // This is the third error
    }

    @Test
    void testValidateBulkSubmission_DifferentBillingBranch() {
        // Setup
        List<Long> claimIds = Arrays.asList(1L, 2L);

        Claim claim1 = new Claim();
        claim1.setId(1L);
        claim1.setBillingBranchId(1L);

        PatientInsurance patientInsurance1 = new PatientInsurance();
        InsuranceCompany insuranceCompany1 = new InsuranceCompany();
        insuranceCompany1.setId(1L);
        patientInsurance1.setInsuranceCompany(insuranceCompany1);

        claim1.setResponsiblePatientInsurance(patientInsurance1);

        Claim claim2 = new Claim();
        claim2.setId(2L);
        claim2.setBillingBranchId(2L); // Different billing branch

        PatientInsurance patientInsurance2 = new PatientInsurance();
        InsuranceCompany insuranceCompany2 = new InsuranceCompany();
        insuranceCompany2.setId(1L);
        patientInsurance2.setInsuranceCompany(insuranceCompany2);

        claim2.setResponsiblePatientInsurance(patientInsurance2);

        when(claimRepository.findAllById(claimIds)).thenReturn(Arrays.asList(claim1, claim2));

        // Execute
        List<String> errors = claimService.validateBulkSubmission(claimIds);

        // Verify
        assertEquals(1, errors.size());
        assertEquals("All claims must have the same billing branch", errors.get(0));
    }

    @Test
    void testValidateBulkSubmission_DifferentPayer() {
        // Setup
        List<Long> claimIds = Arrays.asList(1L, 2L);

        Claim claim1 = new Claim();
        claim1.setId(1L);
        claim1.setBillingBranchId(1L);

        PatientInsurance patientInsurance1 = new PatientInsurance();
        InsuranceCompany insuranceCompany1 = new InsuranceCompany();
        insuranceCompany1.setId(1L);
        patientInsurance1.setInsuranceCompany(insuranceCompany1);

        claim1.setResponsiblePatientInsurance(patientInsurance1);

        Claim claim2 = new Claim();
        claim2.setId(2L);
        claim2.setBillingBranchId(1L);

        PatientInsurance patientInsurance2 = new PatientInsurance();
        InsuranceCompany insuranceCompany2 = new InsuranceCompany();
        insuranceCompany2.setId(2L); // Different payer
        patientInsurance2.setInsuranceCompany(insuranceCompany2);

        claim2.setResponsiblePatientInsurance(patientInsurance2);

        when(claimRepository.findAllById(claimIds)).thenReturn(Arrays.asList(claim1, claim2));

        // Execute
        List<String> errors = claimService.validateBulkSubmission(claimIds);

        // Verify
        assertEquals(1, errors.size());
        assertEquals("All claims must have the same payer", errors.get(0));
    }

    @Test
    void testSendBulkClaimFiles_ValidationFails() throws X12Exception {
        // Setup
        List<Long> claimIds = Arrays.asList(1L, 2L);
        Long billingBranchId = 1L;

        List<String> validationErrors = Collections.singletonList("Validation error");

        // Create a spy of the service to mock the validateBulkSubmission method
        ClaimService spyClaimService = spy(claimService);
        doReturn(validationErrors).when(spyClaimService).validateBulkSubmission(claimIds);

        // Execute
        Map<String, Object> result = spyClaimService.sendBulkClaimFiles(claimIds, billingBranchId);

        // Verify
        assertFalse((Boolean) result.get("success"));
        assertEquals(validationErrors, result.get("errors"));
        verify(spyClaimService).validateBulkSubmission(claimIds);
        verifyNoInteractions(bulkClaimJobService);
    }

    @Test
    void testSendBulkClaimFiles_Success() throws X12Exception {
        // Setup
        List<Long> claimIds = Arrays.asList(1L, 2L);
        Long billingBranchId = 1L;

        // Create a spy of the service to mock the validateBulkSubmission method
        ClaimService spyClaimService = spy(claimService);
        doReturn(Collections.emptyList()).when(spyClaimService).validateBulkSubmission(claimIds);

        // Mock the bulkClaimJobService.createJob method
        BulkClaimJob job = new BulkClaimJob();
        job.setJobId("test-job-id");
        job.setStatus("PENDING");
        when(bulkClaimJobService.createJob(claimIds)).thenReturn(job);

        // Execute
        Map<String, Object> result = spyClaimService.sendBulkClaimFiles(claimIds, billingBranchId);

        // Verify
        assertTrue((Boolean) result.get("success"));
        assertEquals("test-job-id", result.get("jobId"));
        assertEquals("PENDING", result.get("status"));
        verify(spyClaimService).validateBulkSubmission(claimIds);
        verify(bulkClaimJobService).createJob(claimIds);
    }
}
