package com.nymbl.tenant.service;

import com.nymbl.Application;
import com.nymbl.config.TenantDatabaseConfig;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = {Application.class, TenantDatabaseConfig.class})
@TestPropertySource(value = "classpath:test.properties")
@ActiveProfiles("test")
class ExportLLPRServiceTest {

//    @Autowired
//    AWSS3Service awss3Service;
//    @Autowired
//    BatchUtil batchUtil;
//    @Autowired
//    PatientRepository patientRepository;
//    @Autowired
//    ExportLLPRService exportLLPRService;
//
//    @Value("${run.integration.test}")
//    private Boolean runIntegrationTest;
//    private String tenant = "austin_pro";
//
//    @PersistenceContext(unitName = "tenant")
//    private EntityManager entityManager;
//
//
//    @BeforeEach
//    void setUp() {
//        TableObjectContainer.setApplicationRunning(true);
//        TenantContext.setCurrentTenant(tenant);
//    }
//
//    @AfterEach
//    void tearDown() {
//    }
//
//
//    @Test
//    public void writeLLPRExportFilesTest(){
//        TenantContext.setCurrentTenant(tenant);
//        System.out.println("Start");
//        String filePath =  "/Users/<USER>/";
//        exportLLPRService.writeLLPRExportFiles(filePath);
//        System.out.println("End");
//    }
}