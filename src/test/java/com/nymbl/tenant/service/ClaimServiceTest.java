package com.nymbl.tenant.service;

import com.nymbl.config.Constants;
import com.nymbl.config.clearingHouse.Waystar837P;
import com.nymbl.config.clearingHouse.WaystarAPI;
import com.nymbl.config.dto.*;
import com.nymbl.config.dto.arReports.ArIvlcPeriodTotals;
import com.nymbl.config.enums.InsuranceType;
import com.nymbl.config.exception.X12Exception;
import com.nymbl.config.service.ClaimProfileService;
import com.nymbl.config.service.TableObjectContainer;
import com.nymbl.config.utils.DateUtil;
import com.nymbl.config.utils.FileUtil;
import com.nymbl.config.utils.OptimisticLockingUtil;
import com.nymbl.config.x12.x835.Factory835;
import com.nymbl.config.x12.x837.Factory837;
import com.nymbl.master.model.ClearingHousePayer;
import com.nymbl.master.model.Company;
import com.nymbl.master.model.NotificationType;
import com.nymbl.master.model.User;
import com.nymbl.master.service.ClearingHousePayerService;
import com.nymbl.master.service.CompanyService;
import com.nymbl.master.service.UserService;
import com.nymbl.tenant.ProjectionUtil;
import com.nymbl.tenant.dashboard.dto.PrescriptionDto;
import com.nymbl.tenant.dashboard.repository.BranchDtoRepository;
import com.nymbl.tenant.dashboard.repository.InsuranceVerificationDtoRepository;
import com.nymbl.tenant.dashboard.service.PrescriptionDtoService;
import com.nymbl.tenant.interfaces.repository.InsuranceVerification_L_CodeIRepository;
import com.nymbl.tenant.model.*;
import com.nymbl.tenant.repository.*;
import jakarta.persistence.EntityManager;
import jakarta.persistence.Query;
import org.joda.time.DateTime;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.springframework.data.domain.*;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import java.math.BigDecimal;
import java.util.*;

import static com.nymbl.config.Constants.DF_YYYY_MM_DD_HH_MM;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import static org.mockito.MockitoAnnotations.openMocks;

@SuppressWarnings("unchecked")
class ClaimServiceTest {

    @Mock
    private ClaimRepository mockClaimRepository;
    @Mock
    private UserService mockUserService;
    @Mock
    private PrescriptionDiagnosisCodeService mockPrescriptionDiagnosisCodeService;
    @Mock
    private InsuranceVerificationRepository mockInsuranceVerificationRepository;
    @Mock
    private InsuranceVerificationService mockInsuranceVerificationService;
    @Mock
    private InsuranceVerification_L_CodeRepository mockInsuranceVerification_L_CodeRepository;
    @Mock
    private InsuranceVerificationLCodeService mockInsuranceVerificationLCodeService;
    @Mock
    private Prescription_L_CodeService mockPrescriptionLCodeService;
    @InjectMocks
    private ClaimSubmissionService mockClaimSubmissionService;
    @Mock
    private ClaimSubmissionRepository mockClaimSubmissionRepository;
    @Mock
    private SystemSettingService mockSystemSettingService;
    @Mock
    private FinancialResponsibilityService mockFinancialResponsibilityService;
    @Mock
    private PrescriptionService mockPrescriptionService;
    @Mock
    private NotificationService mockNotificationService;
    @InjectMocks
    private ClaimFileService mockClaimFileService;
    @Mock
    private ClaimFileRepository mockClaimFileRepository;
    @Mock
    private FileUtil mockFileUtil;
    @Mock
    private InsuranceCompanyService mockInsuranceCompanyService;
    @Mock
    private TaskService mockTaskService;
    @Mock
    private PatientInsuranceService mockPatientInsuranceService;
    @Mock
    private TaskRepository mockTaskRepository;
    @Mock
    private AppliedPaymentRepository mockAppliedPaymentRepository;
    @Mock
    private AppliedPayment_L_CodeRepository mockAppliedPaymentLCodeRepository;
    @Mock
    private DeliveryLocationRepository mockDeliveryLocationRepository;
    @Mock
    private PrescriptionSectionService mockPrescriptionSectionService;
    @Mock
    private BranchService mockBranchService;
    @Mock
    private ClaimProfileService mockClaimProfileService;
    @Mock
    private Factory837 mockFactory837;
    @Mock
    private AutoPostClaimResponseService mockAutoPostClaimResponseService;
    @Mock
    private UserNotificationService mockUserNotificationService;
    @Mock
    private L_CodeFeeService mockLCodeFeeService;
    @Mock
    private EntityManager mockEntityManager;
    @Mock
    private PaymentService mockPaymentService;
    @Mock
    private AppliedPaymentService mockAppliedPaymentService;
    @Mock
    private AutoPostPatientService mockAutoPostPatientService;
    @Mock
    private PatientStatementService mockPatientStatementService;
    @Mock
    private NymblStatusHistoryService mockNymblStatusHistoryService;

    private ClaimService claimServiceUnderTest;
    @Mock
    private PrescriptionDtoService prescriptionDtoService;
    @Mock
    private BranchDtoRepository branchDtoRepository;
    @Mock
    private InsuranceVerificationDtoRepository insuranceVerificationDtoRepository;
    @Mock
    private InsuranceVerification_L_CodeIRepository insuranceVerification_l_codeIRepository;
    @Mock
    private WaystarAPI mockWaystarAPI;
    @Mock
    private ClearingHousePayerService mockClearingHousePayerService;
    @Mock
    private OptimisticLockingUtil mockOptimisticLockingUtil;
    @Mock
    private FeatureFlagService mockFeatureFlagService;
    @Mock
    private CompanyService mockCompanyService;
    @Mock
    private GeneralLedgerLiveService mockGeneralLedgerLiveService;
    @Mock
    private PhysicianService mockPhysicianService;
    @Mock
    private ItemPhysicalService mockItemPhysicalService;
    @Mock
    private GeneralLedgerService2 mockGeneralLedgerService2;

    @BeforeEach
    void setUp() {
        TableObjectContainer.getTableMap().clear();
        mockClaimFileService = new ClaimFileService(mock(ClaimFileRepository.class));
        mockClaimSubmissionService = new ClaimSubmissionService(mock(ClaimSubmissionRepository.class), mock(UserService.class), mock(InsuranceCompanyService.class));
        mockUserNotificationService = new UserNotificationService(mock(UserNotificationRepository.class), mockUserService, mockNotificationService);
        mockInsuranceVerificationLCodeService = new InsuranceVerificationLCodeService(mockInsuranceVerification_L_CodeRepository, mockInsuranceVerificationRepository, mockLCodeFeeService, mockPatientInsuranceService, insuranceVerification_l_codeIRepository, mockSystemSettingService);
        //mockEntityManager = mock(EntityManager.class);
        openMocks(this);
        claimServiceUnderTest = new ClaimService(mockClaimRepository, mockUserService, mockPrescriptionDiagnosisCodeService,
                mockInsuranceVerificationService, mockInsuranceVerificationLCodeService, mockClaimSubmissionService, mockSystemSettingService, mockFinancialResponsibilityService, mockPrescriptionService,
                mockNotificationService, mockClaimFileService, mockInsuranceCompanyService, mockPatientInsuranceService,
                mockTaskService, mockAppliedPaymentRepository, mockAppliedPaymentLCodeRepository, mockDeliveryLocationRepository, mockPrescriptionSectionService, mockBranchService,
                mockFactory837, mockAutoPostClaimResponseService, mockPaymentService, mockAppliedPaymentService,
                mockAutoPostPatientService, mockPatientStatementService, mockNymblStatusHistoryService, prescriptionDtoService, branchDtoRepository, insuranceVerificationDtoRepository,
                mockWaystarAPI, mockClearingHousePayerService, mockOptimisticLockingUtil, mockGeneralLedgerLiveService, mockFeatureFlagService, mockCompanyService, mockPhysicianService, mockGeneralLedgerService2, mockItemPhysicalService,
                mock(Factory835.class), mock(X12FileService.class), mock(NymblStatusService.class));
    }

    @Test
    void testGetProjectedVsBilled() {
        List<Long> branchIdList = Arrays.asList(Fake.getBranch().getId());
        when(branchDtoRepository.findBranchDtoByIdDto(any())).thenReturn(ProjectionUtil.makeBranchDto());
        when(mockInsuranceVerificationLCodeService.getReportBillablesAndAllowablesUsingIvlcs(any(), any(), any(), any())).thenReturn(new ArIvlcPeriodTotals());
        List<PrescriptionDto> prescriptionDtoList = new ArrayList<>();
        prescriptionDtoList.add(ProjectionUtil.makePrescriptionDto(1l));
        when(prescriptionDtoService.findPrescriptionByPatientBranchAndSubmissionDate(any(), any(), any())).thenReturn(prescriptionDtoList);
        claimServiceUnderTest.getProjectedVsBilled(branchIdList, true, false);
        assertTrue(claimServiceUnderTest.getCheckForError().contains("Device Type is null for prescription id"));
    }


    @Deprecated
//    @Test
    void testCreate837Requests() {
        SystemSetting systemSetting = new SystemSetting();
        systemSetting.setId(Fake.ID);
        systemSetting.setSection("claim");
        systemSetting.setField("format_837");
        systemSetting.setValue("zirmed");
        when(mockSystemSettingService.findBySectionAndField("claim", "format_837")).thenReturn(systemSetting);

        when(mockSystemSettingService.findBySection("claim")).thenReturn(Fake.getClaimSettings());

        User user = Fake.getUserFakeID();
        when(mockUserService.getCurrentCompany()).thenReturn(user.getCompany());

        Claim claim = Fake.getClaim();
        Branch branch = Fake.getBranch();
        Long patientInsuranceId = 116L;
        Long otherPatientInsuranceId = 100L;

        PatientInsurance patientInsurance = Fake.getPatientInsurance();
        when(mockPatientInsuranceService.findOne(anyLong())).thenReturn(patientInsurance);

        InsuranceVerification insuranceVerification = Fake.getInsuranceVerification();
        when(mockInsuranceVerificationService.findByPatientInsuranceIdAndPrescriptionId(any(), any())).thenReturn(insuranceVerification);

        InsuranceVerification_L_Code insuranceVerification_l_code = new InsuranceVerification_L_Code();
        insuranceVerification_l_code.setInsuranceVerificationId(insuranceVerification.getId());
        insuranceVerification_l_code.setId(Fake.ID);
        insuranceVerification_l_code.setBillingFee(BigDecimal.TEN);
        insuranceVerification_l_code.setAllowableFee(BigDecimal.TEN);
        insuranceVerification_l_code.setBill(true);
        Prescription_L_Code prescriptionLCode = new Prescription_L_Code();
        prescriptionLCode.setId(Fake.ID);
        prescriptionLCode.setModifier1("HelpMe");
        prescriptionLCode.setEmergency(true);
        L_Code lCode = new L_Code();
        lCode.setName("A0000");
        prescriptionLCode.setLCode(lCode);
        prescriptionLCode.setQuantity(3L);
        prescriptionLCode.setEspdt("Help");
        prescriptionLCode.setLCodeJustification("Works For Me");
        insuranceVerification_l_code.setPrescriptionLCodeId(prescriptionLCode.getId());
        insuranceVerification_l_code.setPrescriptionLCode(prescriptionLCode);
//        when(mockInsuranceVerificationLCodeService.findByInsuranceVerificationIdAndCoveredTrueAndOrderByPlcOrderNum(anyLong())).thenReturn(Collections.singletonList(insuranceVerification_l_code));
        when(mockInsuranceVerificationLCodeService.findByInsuranceVerificationIdAndBillTrueAndOrderByPlcOrderNum(anyLong())).thenReturn(Collections.singletonList(insuranceVerification_l_code));

        when(mockSystemSettingService.findBySection(any())).thenReturn(Fake.getClaimSettings());
        List<String> requests837 = claimServiceUnderTest.create837Requests(claim, branch, patientInsuranceId, otherPatientInsuranceId);
        assertEquals("|102398001|Benner|Patricia|A||11/30/1957|F|B|P|||1259 Thompson Pl|San Antonio|TX|78226||18|1259 Thompson Pl|San Antonio|TX|78226|||B|P|||102398001|||WellMed-United Healthcare|N|N||N||NONE|11/30/1957|F|||Main|SOF|10/29/2019|SOF|||||||||||||||||||||||||||||||0||||**********|E|P4C1|Y|successful||||" + DateUtil.getStringDate(DateUtil.getCurrentDate(), Constants.WAYSTAR_DATE_FORMAT) + "|Neu Limbs, LLC.|**********||5282 Medical Drive, Suite 105||San Antonio|TX|78229|**********|Neu Limbs, LLC.|5282 Medical Drive, Suite 105||San Antonio|TX|78229||||WellMed-United Healthcare|||San Antonio|TX||||||Y|A0000|HelpMe||30|3|Help||||**********|Works For Me|||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||", requests837.get(0));

//        SystemSetting systemSetting3 = new SystemSetting();
//        systemSetting3.setId(Fake.ID);
//        systemSetting3.setSection("claim");
//        systemSetting3.setField("use_icb_name_for_hcfa");
//        systemSetting3.setValue("Y");
//        when(mockSystemSettingService.findBySectionAndField("claim", "use_icb_name_for_hcfa")).thenReturn(systemSetting3);

        List<SystemSetting> l = Fake.getClaimSettings();
        for (SystemSetting ss : l) {
            if (ss.getField().equals("use_icb_name_for_hcfa")) {
                ss.setValue("Y");
                break;
            }
        }
        when(mockSystemSettingService.findBySection(any())).thenReturn(l);

        List<String> requests837use_icb_name_for_hcfa = claimServiceUnderTest.create837Requests(claim, branch, patientInsuranceId, otherPatientInsuranceId);
        assertEquals("|102398001|Benner|Patricia|A||11/30/1957|F|B|P|||1259 Thompson Pl|San Antonio|TX|78226||18|1259 Thompson Pl|San Antonio|TX|78226|||B|P|||102398001|||WellMed-United Healthcare|N|N||N||NONE|11/30/1957|F|||WellMed-United Healthcare|SOF|10/29/2019|SOF|||||||||||||||||||||||||||||||0||||**********|E|P4C1|Y|successful||||" + DateUtil.getStringDate(DateUtil.getCurrentDate(), Constants.WAYSTAR_DATE_FORMAT) + "|Neu Limbs, LLC.|**********||5282 Medical Drive, Suite 105||San Antonio|TX|78229|**********|Neu Limbs, LLC.|5282 Medical Drive, Suite 105||San Antonio|TX|78229||||Main|||San Antonio|TX||||||Y|A0000|HelpMe||30|3|Help||||**********|Works For Me|||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||", requests837use_icb_name_for_hcfa.get(0));
    }

    //    @Test
    void testCreate837RequestsBadPatientInsuranceCompanyBranch() {
        SystemSetting systemSetting = new SystemSetting();
        systemSetting.setId(Fake.ID);
        systemSetting.setSection("claim");
        systemSetting.setField("format_837");
        systemSetting.setValue("zirmed");
        when(mockSystemSettingService.findBySectionAndField("claim", "format_837")).thenReturn(systemSetting);

        when(mockSystemSettingService.findBySection(any())).thenReturn(Fake.getClaimSettings());

        User user = Fake.getUserFakeID();
        when(mockUserService.getCurrentCompany()).thenReturn(user.getCompany());

        Claim claim = Fake.getClaim();
        Branch branch = Fake.getBranch();
        Long patientInsuranceId = 116L;
        Long otherPatientInsuranceId = 100L;

        PatientInsurance patientInsurance = Fake.getPatientInsurance();
        patientInsurance.setInsuranceCompanyBranch(null);
        patientInsurance.setInsuranceCompanyBranchId(null);
        when(mockPatientInsuranceService.findOne(anyLong())).thenReturn(patientInsurance);

        InsuranceVerification insuranceVerification = Fake.getInsuranceVerification();
        when(mockInsuranceVerificationService.findByPatientInsuranceIdAndPrescriptionId(any(), any())).thenReturn(insuranceVerification);

        InsuranceVerification_L_Code insuranceVerification_l_code = new InsuranceVerification_L_Code();
        insuranceVerification_l_code.setInsuranceVerificationId(insuranceVerification.getId());
        insuranceVerification_l_code.setId(Fake.ID);
        insuranceVerification_l_code.setBillingFee(BigDecimal.TEN);
        insuranceVerification_l_code.setAllowableFee(BigDecimal.TEN);
        insuranceVerification_l_code.setBill(true);
        Prescription_L_Code prescriptionLCode = new Prescription_L_Code();
        prescriptionLCode.setId(Fake.ID);
        prescriptionLCode.setModifier1("HelpMe");
        prescriptionLCode.setEmergency(true);
        L_Code lCode = new L_Code();
        lCode.setName("A0000");
        prescriptionLCode.setLCode(lCode);
        prescriptionLCode.setQuantity(3L);
        prescriptionLCode.setEspdt("Help");
        prescriptionLCode.setLCodeJustification("Works For Me");
        insuranceVerification_l_code.setPrescriptionLCodeId(prescriptionLCode.getId());
        insuranceVerification_l_code.setPrescriptionLCode(prescriptionLCode);
//        when(mockInsuranceVerificationLCodeService.findByInsuranceVerificationIdAndCoveredTrueAndOrderByPlcOrderNum(anyLong())).thenReturn(Collections.singletonList(insuranceVerification_l_code));
        when(mockInsuranceVerificationLCodeService.findByInsuranceVerificationIdAndBillTrueAndOrderByPlcOrderNum(anyLong())).thenReturn(Collections.singletonList(insuranceVerification_l_code));

        SystemSetting systemSetting2 = new SystemSetting();
        systemSetting2.setId(Fake.ID2);
        systemSetting2.setSection("claim");
        systemSetting2.setField("rendering_provider_other_id");
        systemSetting2.setValue("branch_npi");
        when(mockSystemSettingService.findBySectionAndField("claim", "rendering_provider_other_id")).thenReturn(systemSetting2);

//        List<String> requests837 = claimServiceUnderTest.create837Requests(claim, branch, patientInsuranceId, otherPatientInsuranceId);
//        assertEquals("|102398001|Benner|Patricia|A||11/30/1957|F|B|P|||1259 Thompson Pl|San Antonio|TX|78226||18|1259 Thompson Pl|San Antonio|TX|78226|||B|P|||102398001|||WellMed-United Healthcare|N|N||N||NONE|11/30/1957|F|||WellMed-United Healthcare|SOF|10/29/2019|SOF|||||||||||||||||||||||||||||||0||||**********|E|P4C1|Y|successful||||" + DateUtil.getStringDate(DateUtil.getCurrentDate(), Constants.WAYSTAR_DATE_FORMAT) + "|Neu Limbs, LLC.|**********||5282 Medical Drive, Suite 105||San Antonio|TX|78229|**********|Neu Limbs, LLC.|5282 Medical Drive, Suite 105||San Antonio|TX|78229||||WellMed-United Healthcare||||||||||Y|A0000|HelpMe||30|3|Help||||**********|Works For Me|||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||", requests837.get(0));

        SystemSetting systemSetting3 = new SystemSetting();
        systemSetting3.setId(Fake.ID);
        systemSetting3.setSection("claim");
        systemSetting3.setField("use_icb_name_for_hcfa");
        systemSetting3.setValue("Y");
        when(mockSystemSettingService.findBySectionAndField("claim", "use_icb_name_for_hcfa")).thenReturn(systemSetting3);

//        List<String> requests837use_icb_name_for_hcfa = claimServiceUnderTest.create837Requests(claim, branch, patientInsuranceId, otherPatientInsuranceId);
//        assertEquals("|102398001|Benner|Patricia|A||11/30/1957|F|B|P|||1259 Thompson Pl|San Antonio|TX|78226||18|1259 Thompson Pl|San Antonio|TX|78226|||B|P|||102398001|||WellMed-United Healthcare|N|N||N||NONE|11/30/1957|F|||WellMed-United Healthcare|SOF|10/29/2019|SOF|||||||||||||||||||||||||||||||0||||**********|E|P4C1|Y|successful||||" + DateUtil.getStringDate(DateUtil.getCurrentDate(), Constants.WAYSTAR_DATE_FORMAT) + "|Neu Limbs, LLC.|**********||5282 Medical Drive, Suite 105||San Antonio|TX|78229|**********|Neu Limbs, LLC.|5282 Medical Drive, Suite 105||San Antonio|TX|78229||||WellMed-United Healthcare||||||||||Y|A0000|HelpMe||30|3|Help||||**********|Works For Me|||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||", requests837use_icb_name_for_hcfa.get(0));
    }

    @Test
    void testSave() {
        // Setup
        Claim claim = Fake.getClaim();
        when(mockUserService.getCurrentUser()).thenReturn(Fake.getUserFakeID());

        // Run the test
        Claim result = claimServiceUnderTest.save(claim);

        // Verify the results
        assertEquals(Fake.ID, result.getCreatedById());
        assertNotNull(result.getCreatedAt());
        assertEquals(Fake.ID, result.getUpdatedById());
        assertNotNull(result.getUpdatedAt());

    }

    @Test
    void testUpdateClaim() {
        // Setup
        final java.sql.Date dateOfService = new java.sql.Date(Calendar.getInstance().getTime().getTime());
        final List<Claim> claimList = Collections.singletonList(Fake.getClaim());
        when(mockClaimRepository.findByPrescriptionId(anyLong())).thenReturn(claimList);

        when(mockUserService.getUserById(anyLong())).thenReturn(Fake.getUserFakeID());

        // Configure FinancialResponsibilityService.findByPrescriptionId(...).
        final FinancialResponsibility financialResponsibility = new FinancialResponsibility();
        when(mockFinancialResponsibilityService.findByPrescriptionId(anyLong())).thenReturn(financialResponsibility);

        // Configure PrescriptionService.findOne(...).
        final Prescription prescription = Fake.getPrescription();
        when(mockPrescriptionService.findOne(anyLong())).thenReturn(prescription);

        when(mockInsuranceVerificationService.findFirstByPrescriptionIdAndCarrierTypeOrderByIdDesc(anyLong(), anyString())).thenReturn(Fake.getInsuranceVerification());

        when(mockInsuranceVerificationLCodeService.findByInsuranceVerificationId(anyLong())).thenReturn(Fake.getInsuranceVerification_l_codeList());

        when(mockPrescriptionService.save(any(Prescription.class))).thenReturn(Fake.getPrescription());

        when(mockInsuranceVerificationLCodeService.save(any(InsuranceVerification_L_Code.class))).thenReturn(Fake.getInsuranceVerification_l_code());

        when(mockUserService.getCurrentUser()).thenReturn(Fake.getUserFakeID());

        // Run the test
        final Claim result = claimServiceUnderTest.updateClaim(Fake.ID, Fake.ID, Fake.ID, dateOfService, false);

        // Verify the results
        assertEquals(Fake.ID, result.getUserId());
        assertEquals(Fake.ID, result.getCreatedById());
        assertEquals(Fake.ID, result.getUpdatedById());
        assertEquals(Fake.ID, result.getUser().getId());
        assertEquals(Fake.ID, result.getCreatedBy().getId());
        assertEquals(Fake.ID, result.getUpdatedBy().getId());
    }

    @Test
    void testSendClaimFiles() {
        // Setup
        final List<Long> claimIds = Collections.singletonList(Fake.ID);
        final Long billingBranchId = Fake.ID;
        SystemSetting systemSetting = new SystemSetting();
        systemSetting.setId(Fake.ID);
        systemSetting.setSection("billing");
        systemSetting.setField("auto_claim_follow_up");
        systemSetting.setValue("Y");
        SystemSetting systemSetting2 = new SystemSetting();
        systemSetting2.setId(Fake.ID);
        systemSetting2.setSection("claim");
        systemSetting2.setField("format_837");
        systemSetting2.setValue("zirmed");
        SystemSetting systemSetting3 = new SystemSetting();
        systemSetting3.setId(Fake.ID);
        systemSetting3.setSection("claim");
        systemSetting3.setField("use_single_claim");
        systemSetting3.setValue("N");
        SystemSetting systemSetting4 = new SystemSetting();
        systemSetting4.setId(Fake.ID2);
        systemSetting4.setSection("claim");
        systemSetting4.setField("rendering_provider_other_id");
        systemSetting4.setValue("branch_npi");

        when(mockSystemSettingService.findBySectionAndField("billing", "auto_claim_follow_up")).thenReturn(systemSetting);
        when(mockSystemSettingService.findBySectionAndField("claim", "format_837")).thenReturn(systemSetting2);
        when(mockSystemSettingService.findBySectionAndField("claim", "use_single_claim")).thenReturn(systemSetting3);
        when(mockSystemSettingService.findBySectionAndField("claim", "rendering_provider_other_id")).thenReturn(systemSetting4);
        Branch branch = new Branch();
        branch.setId(Fake.ID);
        branch.setName("Local");
        branch.setTaxIdType("ssn");
        branch.setTaxId("***********");
        branch.setUseBranchName(true);
        when(mockUserService.getCurrentBranchUnsafeDoNotUse()).thenReturn(branch);
        Claim claim = new Claim();
        claim.setId(Fake.ID);
        claim.setResubmissionCode("6");
        claim.setOriginalRefNum("4567");
        claim.setAdditionalInfo("Help is on the way!");
        Prescription prescription = new Prescription();
        prescription.setId(Fake.ID);
        Patient patient = new Patient();
        patient.setId(Fake.ID);
        Branch patientPrimaryBranch = new Branch();
        patientPrimaryBranch.setId(Fake.ID2);
        patientPrimaryBranch.setName("PatientPrimaryBranch");
        patientPrimaryBranch.setTaxIdType("ssn");
        patientPrimaryBranch.setTaxId("***********");
        patientPrimaryBranch.setUseBranchName(true);
        patient.setPrimaryBranch(patientPrimaryBranch);
        patient.setPrimaryBranchId(Fake.ID2);
        patient.setLastName("L");
        patient.setFirstName("F");
        patient.setMiddleName("M");
        patient.setDob(java.sql.Date.valueOf("2000-01-01"));
        patient.setGender("Male");
        patient.setStreetAddress("555 Here");
        patient.setCity("Local");
        patient.setState("KY");
        patient.setZipcode("55555");
        patient.setHomePhone("**********");
        InsuranceCompany insuranceCompany = new InsuranceCompany();
        insuranceCompany.setId(Fake.ID);
        insuranceCompany.setFollowUpDays(7L);
        insuranceCompany.setFollowUpMessage("Follow up on this");
        insuranceCompany.setName("GoodInsurance");
        insuranceCompany.setForm1500TemplateId(Fake.ID);
        insuranceCompany.setForm1500Template(Fake.getForm1500Template());
        insuranceCompany.setPhysicianQualifier("DN");
        InsuranceCompanyBranch insuranceCompanyBranch = new InsuranceCompanyBranch();
        insuranceCompanyBranch.setId(Fake.ID);
        insuranceCompanyBranch.setStreetAddress("555 Here Again");
        insuranceCompanyBranch.setCity("LocalHere");
        insuranceCompanyBranch.setState("KY");
        insuranceCompanyBranch.setZipcode("55555");
        PatientInsurance patientInsurance = new PatientInsurance();
        patientInsurance.setId(Fake.ID);
        patientInsurance.setPatient(patient);
        patientInsurance.setInsuranceCompanyId(Fake.ID);
        patientInsurance.setInsuranceCompany(insuranceCompany);
        patientInsurance.setInsuranceCompanyId(Fake.ID);
        patientInsurance.setInsuranceCompanyBranch(insuranceCompanyBranch);
        patientInsurance.setInsuranceCompanyBranchId(Fake.ID);
        patientInsurance.setInsuranceNumber("777");
        patientInsurance.setLastName("L");
        patientInsurance.setFirstName("F");
        patientInsurance.setMiddleName("M");
        patientInsurance.setStreetAddress("555 Here");
        patientInsurance.setCity("BestPlace");
        patientInsurance.setState("KY");
        patientInsurance.setZipcode("55555");
        patientInsurance.setRelationToSubscriber("physician");
        patientInsurance.setGender("Male");
        PhoneNumber phoneNumber = new PhoneNumber();
        phoneNumber.setId(Fake.ID);
        phoneNumber.setPhoneNumber("**********");
        prescription.setPatientInsurance(patientInsurance);
        prescription.setPatientInsuranceId(Fake.ID);
        prescription.setPatient(patient);
        claim.setPrescription(prescription);
        claim.setPrescriptionId(Fake.ID);
        claim.setPatientInsuranceId(Fake.ID);
        claim.setPatientInsurance(patientInsurance);
        claim.setResponsiblePatientInsuranceId(Fake.ID);
        claim.setResponsiblePatientInsurance(patientInsurance);
        claim.setAcceptAssignment(true);
        when(mockClaimRepository.findById(Fake.ID)).thenReturn(Optional.of(claim));
        Company company = new Company();
        company.setId(Fake.ID);
        when(mockUserService.getCurrentCompany()).thenReturn(company);
        when(mockPatientInsuranceService.findOne(anyLong())).thenReturn(patientInsurance);
        PrescriptionDiagnosisCode prescriptionDiagnosisCode = new PrescriptionDiagnosisCode();
        prescriptionDiagnosisCode.setId(Fake.ID);
        DiagnosisCode diagnosisCode = new DiagnosisCode();
        diagnosisCode.setId(Fake.ID);
        diagnosisCode.setCode("A0000");
        prescriptionDiagnosisCode.setDiagnosisCode(diagnosisCode);
        when(mockPrescriptionDiagnosisCodeService.findByPrescriptionId(Fake.ID)).thenReturn(Collections.singletonList(prescriptionDiagnosisCode));
        InsuranceVerification insuranceVerification = new InsuranceVerification();
        insuranceVerification.setId(Fake.ID);
        insuranceVerification.setPatientInsurance(patientInsurance);
        insuranceVerification.setPatientInsuranceId(patientInsurance.getId());
        when(mockInsuranceVerificationService.findByPatientInsuranceIdAndPrescriptionId(Fake.ID, Fake.ID)).thenReturn(insuranceVerification);
        InsuranceVerification_L_Code insuranceVerification_l_code = new InsuranceVerification_L_Code();
        insuranceVerification_l_code.setInsuranceVerificationId(insuranceVerification.getId());
        insuranceVerification_l_code.setId(Fake.ID);
        insuranceVerification_l_code.setBillingFee(BigDecimal.TEN);
        insuranceVerification_l_code.setAllowableFee(BigDecimal.TEN);
        insuranceVerification_l_code.setBill(true);
        Prescription_L_Code prescriptionLCode = new Prescription_L_Code();
        prescriptionLCode.setId(Fake.ID);
        prescriptionLCode.setModifier1("HelpMe");
        prescriptionLCode.setEmergency(true);
        L_Code lCode = new L_Code();
        lCode.setName("A0000");
        prescriptionLCode.setLCode(lCode);
        prescriptionLCode.setQuantity(3L);
        prescriptionLCode.setEspdt("Help");
        prescriptionLCode.setLCodeJustification("Works For Me");
        insuranceVerification_l_code.setPrescriptionLCodeId(prescriptionLCode.getId());
        insuranceVerification_l_code.setPrescriptionLCode(prescriptionLCode);
//        when(mockInsuranceVerificationLCodeService.findByInsuranceVerificationIdAndCoveredTrueAndOrderByPlcOrderNum(anyLong())).thenReturn(Collections.singletonList(insuranceVerification_l_code));
        when(mockInsuranceVerificationLCodeService.findByInsuranceVerificationIdAndBillTrueAndOrderByPlcOrderNum(anyLong())).thenReturn(Collections.singletonList(insuranceVerification_l_code));

        when(mockBranchService.findOne(Fake.ID)).thenReturn(branch);
        User user = new User();
        user.setId(Fake.ID);
        when(mockUserService.getUserById(Fake.ID)).thenReturn(user);
        DeliveryLocation deliveryLocation = new DeliveryLocation();
        deliveryLocation.setId(Fake.ID);
        when(mockDeliveryLocationRepository.findById(Fake.ID)).thenReturn(Optional.of(deliveryLocation));
        when(mockInsuranceVerificationService.findByPrescriptionId(Fake.ID)).thenReturn(Collections.singletonList(insuranceVerification));
        when(mockInsuranceVerificationService.findFirstByPrescriptionIdAndCarrierTypeOrderByIdDesc(Fake.ID, "carrierType")).thenReturn(insuranceVerification);
        ClaimFile claimFile = new ClaimFile();
        claimFile.setId(Fake.ID);
        when(mockClaimFileRepository.save(any(ClaimFile.class))).thenReturn(claimFile);
//        when(mockFactory837.build(Fake.ID, "controlNumber")).thenReturn(null);
        when(mockUserService.getCurrentUser()).thenReturn(user);
        ClaimSubmission claimSubmission = new ClaimSubmission();
        claimSubmission.setId(Fake.ID);
        when(mockClaimSubmissionRepository.save(any(ClaimSubmission.class))).thenReturn(claimSubmission);
        Task followUpTask = new Task();
        followUpTask.setId(Fake.ID);
        when(mockTaskRepository.save(any(Task.class))).thenReturn(followUpTask);

        when(mockPatientInsuranceService.findOne(any())).thenReturn(patientInsurance);

        when(mockSystemSettingService.findBySection(any())).thenReturn(Fake.getClaimSettings());

        // Run the test
        try {
            claimServiceUnderTest.sendClaimFiles(claimIds, billingBranchId);
        } catch (X12Exception e) {
            System.out.println(e.getMessage());
        }
        // Verify the results
        HashMap<String, Object> tM = TableObjectContainer.getTableMap();
        assertEquals(Fake.ID, ((ClaimFile) TableObjectContainer.getObjectFromList(ClaimFile.class.getName(), 0)).getClaimId());
        assertEquals(Fake.ID, ((ClaimFile) TableObjectContainer.getObjectFromList(ClaimFile.class.getName(), 0)).getBranchId());
        assertFalse(((ClaimFile) TableObjectContainer.getObjectFromList(ClaimFile.class.getName(), 0)).getSubmitted());
        assertTrue(((ClaimFile) TableObjectContainer.getObjectFromList(ClaimFile.class.getName(), 0)).getFilename().endsWith(".CLP"));

        assertEquals(Fake.ID, ((ClaimSubmission) TableObjectContainer.getObjectFromList(ClaimSubmission.class.getName(), 0)).getClaimId());
        assertEquals(Fake.ID, ((ClaimSubmission) TableObjectContainer.getObjectFromList(ClaimSubmission.class.getName(), 0)).getSubmittedById());
        assertEquals("", ((ClaimSubmission) TableObjectContainer.getObjectFromList(ClaimSubmission.class.getName(), 0)).getStatus());

        assertEquals(Fake.ID, ((Claim) TableObjectContainer.getObjectFromList(Claim.class.getName(), 0)).getId());
        assertEquals(Fake.ID, ((Claim) TableObjectContainer.getObjectFromList(Claim.class.getName(), 0)).getPrescriptionId());
//        assertEquals("queued", ((Claim) TableObjectContainer.getObjectFromList(Claim.class.getName(), 0)).getStatus());
        assertEquals(Fake.QUEUED_ID, ((Claim) TableObjectContainer.getObjectFromList(Claim.class.getName(), 0)).getNymblStatusId());
        assertEquals("Help is on the way!", ((Claim) TableObjectContainer.getObjectFromList(Claim.class.getName(), 0)).getAdditionalInfo());

        assertEquals(Fake.ID, ((Task) TableObjectContainer.getObjectFromList(Task.class.getName(), 0)).getClaimId());
        assertEquals(false, ((Task) TableObjectContainer.getObjectFromList(Task.class.getName(), 0)).getCompleted());
        assertEquals("Follow up on this", ((Task) TableObjectContainer.getObjectFromList(Task.class.getName(), 0)).getDescription());
        assertNotEquals(null, ((Task) TableObjectContainer.getObjectFromList(Task.class.getName(), 0)).getDueDate());

        systemSetting2.setValue("zirmed");
        when(mockSystemSettingService.findBySectionAndField("claim", "format_837")).thenReturn(systemSetting2);
        tM.clear();

        try {
            claimServiceUnderTest.sendClaimFiles(claimIds, billingBranchId);
        } catch (X12Exception e) {
            System.out.println(e.getMessage());
        }

        assertEquals(Fake.ID, ((ClaimFile) TableObjectContainer.getObjectFromList(ClaimFile.class.getName(), 0)).getClaimId());
        assertEquals(Fake.ID, ((ClaimFile) TableObjectContainer.getObjectFromList(ClaimFile.class.getName(), 0)).getBranchId());
        assertFalse(((ClaimFile) TableObjectContainer.getObjectFromList(ClaimFile.class.getName(), 0)).getSubmitted());
        assertTrue(((ClaimFile) TableObjectContainer.getObjectFromList(ClaimFile.class.getName(), 0)).getFilename().endsWith(".CLP"));
        String contentExpected = "|777|L|F|M||1/1/2000|M|L|F|M||555 Here|Local|KY|55555|**********|G8|555 Here|BestPlace|KY|55555|||||||||||N|N||N||NONE||M|||GoodInsurance|SOF||SOF|||||||||||DN||||||Help is on the way!||A0000||||||||||||0|6|4567||**********|E|P1C1|Y|Local||||"
                + DateUtil.getStringDate(DateUtil.getCurrentDate(), Constants.WAYSTAR_DATE_FORMAT)
                + "|Local||||||||||||||||||GoodInsurance|555 Here Again||LocalHere|KY|55555|||||Y|A0000|HelpMe||30|3|Help|||||Works For Me|||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||";
        assertEquals(contentExpected, ((ClaimFile) TableObjectContainer.getObjectFromList(ClaimFile.class.getName(), 0)).getContents());

        assertEquals(Fake.ID, ((ClaimSubmission) TableObjectContainer.getObjectFromList(ClaimSubmission.class.getName(), 0)).getClaimId());
        assertEquals(Fake.ID, ((ClaimSubmission) TableObjectContainer.getObjectFromList(ClaimSubmission.class.getName(), 0)).getSubmittedById());
        assertEquals("", ((ClaimSubmission) TableObjectContainer.getObjectFromList(ClaimSubmission.class.getName(), 0)).getStatus());

        assertEquals(Fake.ID, ((Claim) TableObjectContainer.getObjectFromList(Claim.class.getName(), 0)).getId());
        assertEquals(Fake.ID, ((Claim) TableObjectContainer.getObjectFromList(Claim.class.getName(), 0)).getPrescriptionId());
//        assertEquals("queued", ((Claim) TableObjectContainer.getObjectFromList(Claim.class.getName(), 0)).getStatus());
        assertEquals(Fake.QUEUED_ID, ((Claim) TableObjectContainer.getObjectFromList(Claim.class.getName(), 0)).getNymblStatusId());

        assertEquals(Fake.ID, ((Task) TableObjectContainer.getObjectFromList(Task.class.getName(), 0)).getClaimId());
        assertEquals(false, ((Task) TableObjectContainer.getObjectFromList(Task.class.getName(), 0)).getCompleted());
        assertEquals("Follow up on this", ((Task) TableObjectContainer.getObjectFromList(Task.class.getName(), 0)).getDescription());
        assertNotEquals(null, ((Task) TableObjectContainer.getObjectFromList(Task.class.getName(), 0)).getDueDate());

        tM.clear();
        try {
            claimServiceUnderTest.sendClaimFiles(claimIds, null);
        } catch (X12Exception e) {
            System.out.println(e.getMessage());
        }
        //System.out.println(((ClaimFile)TableObjectContainer.getObjectFromList(ClaimFile.class.getName(), 0)).getContents());
        contentExpected = "|777|L|F|M||1/1/2000|M|L|F|M||555 Here|Local|KY|55555|**********|G8|555 Here|BestPlace|KY|55555|||||||||||N|N||N||NONE||M|||GoodInsurance|SOF||SOF|||||||||||DN||||||Help is on the way!||A0000||||||||||||0|6|4567||**********|E|P1C1|Y|PatientPrimaryBranch||||"
                + DateUtil.getStringDate(DateUtil.getCurrentDate(), Constants.WAYSTAR_DATE_FORMAT)
                + "|PatientPrimaryBranch||||||||||||||||||GoodInsurance|555 Here Again||LocalHere|KY|55555|||||Y|A0000|HelpMe||30|3|Help|||||Works For Me|||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||";
        assertEquals(contentExpected, ((ClaimFile) TableObjectContainer.getObjectFromList(ClaimFile.class.getName(), 0)).getContents());

        claim.setPatientInsurance(null);
        when(mockClaimRepository.findById(Fake.ID)).thenReturn(Optional.of(claim));
        tM.clear();
        try {
            claimServiceUnderTest.sendClaimFiles(claimIds, billingBranchId);
        } catch (X12Exception e) {
            System.out.println(e.getMessage());
        }

//        assertNull(tM.get(ClaimFile.class.getName()));

        assertEquals(Fake.ID, ((ClaimSubmission) TableObjectContainer.getObjectFromList(ClaimSubmission.class.getName(), 0)).getClaimId());
        assertEquals(Fake.ID, ((ClaimSubmission) TableObjectContainer.getObjectFromList(ClaimSubmission.class.getName(), 0)).getSubmittedById());
        assertEquals("", ((ClaimSubmission) TableObjectContainer.getObjectFromList(ClaimSubmission.class.getName(), 0)).getStatus());

        assertEquals(Fake.ID, ((Claim) TableObjectContainer.getObjectFromList(Claim.class.getName(), 0)).getId());
        assertEquals(Fake.ID, ((Claim) TableObjectContainer.getObjectFromList(Claim.class.getName(), 0)).getPrescriptionId());
//        assertEquals("post_to_pt_responsibility", ((Claim) TableObjectContainer.getObjectFromList(Claim.class.getName(), 0)).getStatus());
        assertEquals(Fake.QUEUED_ID, ((Claim) TableObjectContainer.getObjectFromList(Claim.class.getName(), 0)).getNymblStatusId());

    }

    @Test
    void testLoadForeignKeys() {
        // Setup
        final Claim o = new Claim();
        o.setUserId(Fake.ID);
        User user = new User();
        user.setId(Fake.ID);
        when(mockUserService.getUserById(Fake.ID)).thenReturn(user);

        final ClearingHousePayer p = new ClearingHousePayer();
        p.setId(Fake.ID);
        p.setPayerId("<PAYERID>");
        when(mockClearingHousePayerService.findByPayerId("")).thenReturn(Collections.singletonList(p));

        // Run the test
        claimServiceUnderTest.loadForeignKeys(o);

        // Verify the results
        assertEquals(Fake.ID, o.getUser().getId());
    }

    @Test
    void testFindByPrescriptionPatientId() {
        // Setup
        final Long patientId = Fake.ID;
        when(mockClaimRepository.findPatientClaim1(patientId)).thenReturn(Fake.getClaimList());
        when(mockClaimRepository.findPatientClaim0(patientId)).thenReturn(Fake.getClaimList());
        when(mockUserService.getUserById(patientId)).thenReturn(Fake.getUserFakeID());

        // Run the test
        List<Claim> result = claimServiceUnderTest.findByPrescriptionPatientId(patientId, 0);

        // Verify the results
        assertEquals(Fake.ID, result.get(0).getId());
        assertEquals(Fake.ID, result.get(0).getUser().getId());

        result = claimServiceUnderTest.findByPrescriptionPatientId(patientId, 1);

        // Verify the results
        assertEquals(Fake.ID, result.get(0).getId());
        assertEquals(Fake.ID, result.get(0).getUser().getId());
    }

    @Test
    void testFindByPrescriptionId() {
        // Setup
        final Long prescriptionId = Fake.ID;
        when(mockClaimRepository.findByPrescriptionId(Fake.ID)).thenReturn(Fake.getClaimList());
        when(mockUserService.getUserById(Fake.ID)).thenReturn(Fake.getUserFakeID());

        // Run the test
        final List<Claim> result = claimServiceUnderTest.findByPrescriptionId(prescriptionId);

        // Verify the results
        assertEquals(Fake.ID, result.get(0).getId());
        assertEquals(Fake.ID, result.get(0).getUser().getId());
    }

    @Test
    void testPopulateFindClaimsQuery() {
        // Setup
        String querySegment = " FROM claim c LEFT JOIN (SELECT claim_id, MAX(submission_date) AS submission_date FROM claim_submission GROUP BY claim_id) AS cs ON cs.claim_id = c.id LEFT JOIN (SELECT claim_id, MAX(applied_date) AS applied_date FROM applied_payment GROUP BY claim_id) AS ap ON ap.claim_id = c.id LEFT JOIN prescription rx ON c.prescription_id =  rx.id LEFT JOIN patient p ON rx.patient_id = p.id LEFT JOIN patient_insurance pi ON rx.patient_insurance_id = pi.id LEFT JOIN insurance_company ic ON pi.insurance_company_id = ic.id LEFT JOIN branch b ON p.primary_branch_id = b.id INNER JOIN task t ON c.id = t.claim_id WHERE c.status = 'status' AND c.user_id = 1 AND pi.insurance_company_id = 1 AND p.id = 1 AND p.primary_branch_id =1 AND c.id =1 AND t.due_date BETWEEN '2019-01-26' AND '2019-01-26' AND c.date_resolved IS NULL";
        String outcome = "SELECT c.* FROM claim c LEFT JOIN (SELECT claim_id, MAX(submission_date) AS submission_date FROM claim_submission GROUP BY claim_id) AS cs ON cs.claim_id = c.id LEFT JOIN (SELECT claim_id, MAX(applied_date) AS applied_date FROM applied_payment GROUP BY claim_id) AS ap ON ap.claim_id = c.id LEFT JOIN prescription rx ON c.prescription_id =  rx.id LEFT JOIN patient p ON rx.patient_id = p.id LEFT JOIN patient_insurance pi ON rx.patient_insurance_id = pi.id LEFT JOIN insurance_company ic ON pi.insurance_company_id = ic.id LEFT JOIN branch b ON p.primary_branch_id = b.id INNER JOIN task t ON c.id = t.claim_id WHERE c.status = 'status' AND c.user_id = 1 AND pi.insurance_company_id = 1 AND p.id = 1 AND p.primary_branch_id =1 AND c.id =1 AND t.due_date BETWEEN '2019-01-26' AND '2019-01-26' AND c.date_resolved IS NULL ORDER BY c.id DESC LIMIT 0, 30";

        // Run the test
        String result = claimServiceUnderTest.populateFindClaimsQuery(querySegment, "c.id", "DESC", "0", false);
        // Verify the results
        assertEquals(outcome, result);

        String exportOutcome = "SELECT c.* FROM claim c LEFT JOIN (SELECT claim_id, MAX(submission_date) AS submission_date FROM claim_submission GROUP BY claim_id) AS cs ON cs.claim_id = c.id LEFT JOIN (SELECT claim_id, MAX(applied_date) AS applied_date FROM applied_payment GROUP BY claim_id) AS ap ON ap.claim_id = c.id LEFT JOIN prescription rx ON c.prescription_id =  rx.id LEFT JOIN patient p ON rx.patient_id = p.id LEFT JOIN patient_insurance pi ON rx.patient_insurance_id = pi.id LEFT JOIN insurance_company ic ON pi.insurance_company_id = ic.id LEFT JOIN branch b ON p.primary_branch_id = b.id INNER JOIN task t ON c.id = t.claim_id WHERE c.status = 'status' AND c.user_id = 1 AND pi.insurance_company_id = 1 AND p.id = 1 AND p.primary_branch_id =1 AND c.id =1 AND t.due_date BETWEEN '2019-01-26' AND '2019-01-26' AND c.date_resolved IS NULL ORDER BY c.id DESC LIMIT 0, 999999";
        result = claimServiceUnderTest.populateFindClaimsQuery(querySegment, "c.id", "DESC", "0", true);
        // Verify the results
        assertEquals(exportOutcome, result);
    }

    @Test
    void testGetClaimsByEntityManager() {
        // Setup
        String query = "SELECT c.* FROM claim c LEFT JOIN (SELECT claim_id, MAX(submission_date) AS submission_date FROM claim_submission GROUP BY claim_id) AS cs ON cs.claim_id = c.id LEFT JOIN (SELECT claim_id, MAX(applied_date) AS applied_date FROM applied_payment GROUP BY claim_id) AS ap ON ap.claim_id = c.id LEFT JOIN prescription rx ON c.prescription_id =  rx.id LEFT JOIN patient p ON rx.patient_id = p.id LEFT JOIN patient_insurance pi ON rx.patient_insurance_id = pi.id LEFT JOIN insurance_company ic ON pi.insurance_company_id = ic.id LEFT JOIN branch b ON p.primary_branch_id = b.id INNER JOIN task t ON c.id = t.claim_id WHERE c.status = 'status' AND c.user_id = 1 AND pi.insurance_company_id = 1 AND p.id = 1 AND p.primary_branch_id =1 AND c.id =1 AND t.due_date BETWEEN '2019-01-26' AND '2019-01-26' AND c.date_resolved IS NULL ORDER BY c.id DESC LIMIT 0, 30";
        Query mockedQuery = mock(Query.class);
        when(mockedQuery.getResultList()).thenReturn(Fake.getClaimList());
        when(mockedQuery.setParameter(anyString(), any())).thenReturn(mockedQuery);
        when(mockEntityManager.createNativeQuery(query, Claim.class)).thenReturn(mockedQuery);

        // Run the test
        final List<Claim> result = claimServiceUnderTest.getClaimsByEntityManager(query, mockEntityManager);

        // Verify the results
        assertEquals(Fake.ID, result.get(0).getId());
    }

    @Test
    void testFindClaims() {
        // Setup
        when(mockUserService.getUserById(anyLong())).thenReturn(Fake.getUserFakeID());
        String querySegment = " FROM claim c LEFT JOIN (SELECT claim_id, MAX(submission_date) AS submission_date FROM claim_submission GROUP BY claim_id) AS cs ON cs.claim_id = c.id LEFT JOIN (SELECT claim_id, MAX(applied_date) AS applied_date FROM applied_payment GROUP BY claim_id) AS ap ON ap.claim_id = c.id LEFT JOIN prescription rx ON c.prescription_id =  rx.id LEFT JOIN patient p ON rx.patient_id = p.id LEFT JOIN patient_insurance pi ON rx.patient_insurance_id = pi.id LEFT JOIN insurance_company ic ON pi.insurance_company_id = ic.id LEFT JOIN branch b ON p.primary_branch_id = b.id INNER JOIN task t ON c.id = t.claim_id WHERE c.status = 'status' AND c.user_id = 1 AND pi.insurance_company_id = 1 AND p.id = 1 AND p.primary_branch_id =1 AND c.id =1 AND t.due_date BETWEEN '2019-01-26' AND '2019-01-26' AND c.date_of_service BETWEEN '2019-01-26 00:00:00' AND '2019-01-26 23:59:59' AND c.date_resolved IS NULL";
        String query = "SELECT c.* FROM claim c LEFT JOIN (SELECT claim_id, MAX(submission_date) AS submission_date FROM claim_submission GROUP BY claim_id) AS cs ON cs.claim_id = c.id LEFT JOIN (SELECT claim_id, MAX(applied_date) AS applied_date FROM applied_payment GROUP BY claim_id) AS ap ON ap.claim_id = c.id LEFT JOIN prescription rx ON c.prescription_id =  rx.id LEFT JOIN patient p ON rx.patient_id = p.id LEFT JOIN patient_insurance pi ON rx.patient_insurance_id = pi.id LEFT JOIN insurance_company ic ON pi.insurance_company_id = ic.id LEFT JOIN branch b ON p.primary_branch_id = b.id INNER JOIN task t ON c.id = t.claim_id WHERE c.status = 'status' AND c.user_id = 1 AND pi.insurance_company_id = 1 AND p.id = 1 AND p.primary_branch_id =1 AND c.id =1 AND t.due_date BETWEEN '2019-01-26' AND '2019-01-26' AND c.date_of_service BETWEEN '2019-01-26 00:00:00' AND '2019-01-26 23:59:59' AND c.date_resolved IS NULL ORDER BY c.id DESC LIMIT 0, 30";
        Query mockedQuery = mock(Query.class);
        when(mockedQuery.getResultList()).thenReturn(Fake.getClaimList());
        when(mockedQuery.setParameter(anyString(), any())).thenReturn(mockedQuery);
        when(mockEntityManager.createNativeQuery(query, Claim.class)).thenReturn(mockedQuery);

        // Run the test
        final List<Claim> result = claimServiceUnderTest.findClaims(querySegment, "c.id", "DESC", "0", false, mockEntityManager);

        // Verify the results
        assertEquals(Fake.ID, result.get(0).getId());
        assertEquals(Fake.ID, result.get(0).getUser().getId());
    }

    @Test
    void testExportClaims() {
        // Setup
        final java.sql.Date startDate = Fake.getSqlDate("2019-01-26");
        final java.sql.Date endDate = Fake.getSqlDate("2019-01-26");
        final java.sql.Date dosStartDate = Fake.getSqlDate("2019-01-26");
        final java.sql.Date dosEndDate = Fake.getSqlDate("2019-01-26");
        Sort sort =  Sort.by(Sort.DEFAULT_DIRECTION, "id");
        final Pageable pageable = PageRequest.of(0, 1000, sort);

        SystemSetting ss = new SystemSetting();
        ss.setId(Fake.ID);
        ss.setSection("billing");
        ss.setField("display_biller_code_field");
        ss.setValue("N");
        when(mockSystemSettingService.findBySectionAndField("billing", "display_biller_code_field")).thenReturn(ss);

        when(mockUserService.getUserById(any())).thenReturn(Fake.getUserFakeID());

        // Configure AppliedPaymentRepository.findByClaimId(...).
        final AppliedPayment appliedPayment = new AppliedPayment();
        appliedPayment.setId(Fake.ID);
        appliedPayment.setAppliedDate(java.sql.Date.valueOf("2019-01-26"));

        // Configure taskService.findByClaimIdAndType(...).
        final Task followUpTask = new Task();
        followUpTask.setId(Fake.ID);
        followUpTask.setClaimId(Fake.ID);
        followUpTask.setType("follow_up");

        List<ClaimSubmission> claimSubmissionList = new ArrayList<>();
        ClaimSubmission claimSubmission = new ClaimSubmission();
        claimSubmission.setId(Fake.ID);
        claimSubmission.setSubmissionDate(java.sql.Date.valueOf("2019-01-26"));
        claimSubmissionList.add(claimSubmission);

        String query = "SELECT c.* FROM claim c LEFT JOIN (SELECT claim_id, MAX(submission_date) AS submission_date FROM claim_submission GROUP BY claim_id) AS cs ON cs.claim_id = c.id LEFT JOIN (SELECT claim_id, MAX(applied_date) AS applied_date FROM applied_payment GROUP BY claim_id) AS ap ON ap.claim_id = c.id LEFT JOIN prescription rx ON c.prescription_id =  rx.id LEFT JOIN patient p ON rx.patient_id = p.id LEFT JOIN patient_insurance pi ON c.responsible_patient_insurance_id = pi.id LEFT JOIN insurance_company ic ON pi.insurance_company_id = ic.id LEFT JOIN branch b ON c.billing_branch_id = b.id LEFT JOIN nymbl_master.user n_user ON c.user_id = n_user.id INNER JOIN task t ON c.id = t.claim_id AND t.type = 'follow_up' WHERE c.nymbl_status_id = 1 AND c.user_id = 1 AND pi.insurance_company_id = 1 AND p.id = 1 AND c.billing_branch_id =1 AND c.id =1 AND t.due_date BETWEEN '2019-01-26 00:00:00' AND '2019-01-26 23:59:59' ORDER BY c.id DESC LIMIT 0, 30";
        Query mockedQuery = mock(Query.class);
        List<Claim> claimList = Fake.getClaimList();
        claimList.get(0).setBillingBranch(Fake.getBranch());
        claimList.get(0).setAppliedPayments(Collections.singletonList(appliedPayment));
        claimList.get(0).setTasks(Collections.singletonList(followUpTask));
        claimList.get(0).setClaimSubmissions(claimSubmissionList);

        when(mockedQuery.getResultList()).thenReturn(claimList);
        //when(mockedQuery.setParameter(anyString(), anyObject())).thenReturn(mockedQuery);
        when(mockEntityManager.createNativeQuery(query, Claim.class)).thenReturn(mockedQuery);

        String query2 = "SELECT COUNT(c.id) FROM claim c LEFT JOIN (SELECT claim_id, MAX(submission_date) AS submission_date FROM claim_submission GROUP BY claim_id) AS cs ON cs.claim_id = c.id LEFT JOIN (SELECT claim_id, MAX(applied_date) AS applied_date FROM applied_payment GROUP BY claim_id) AS ap ON ap.claim_id = c.id LEFT JOIN prescription rx ON c.prescription_id =  rx.id LEFT JOIN patient p ON rx.patient_id = p.id LEFT JOIN patient_insurance pi ON c.responsible_patient_insurance_id = pi.id LEFT JOIN insurance_company ic ON pi.insurance_company_id = ic.id LEFT JOIN branch b ON c.billing_branch_id = b.id LEFT JOIN nymbl_master.user n_user ON c.user_id = n_user.id INNER JOIN task t ON c.id = t.claim_id AND t.type = 'follow_up' WHERE c.nymbl_status_id = 1 AND c.user_id = 1 AND pi.insurance_company_id = 1 AND p.id = 1 AND c.billing_branch_id =1 AND c.id =1 AND t.due_date BETWEEN '2019-01-26 00:00:00' AND '2019-01-26 23:59:59'";
        Query mockedQuery2 = mock(Query.class);
        //when(mockedQuery2.setParameter(anyString(), anyObject())).thenReturn(mockedQuery2);
        when(mockedQuery2.getSingleResult()).thenReturn(Long.valueOf(Fake.getClaimList().size()));
        when(mockEntityManager.createNativeQuery(query2)).thenReturn(mockedQuery2);

        // Run the test
        final Page<ClaimSearchDTO> result = claimServiceUnderTest.populateClaimSearchDTOS(Fake.ID, Fake.ID, "status", Fake.ID, Fake.ID, Fake.ID, Fake.ID, startDate, endDate, null, null, false, pageable, false, mockEntityManager, null);

        assertEquals(1000, result.getSize());
        assertEquals(Fake.ID, result.getContent().get(0).getFollowUpTasks().get(0).getId());
        assertEquals(Fake.ID, result.getContent().get(0).getClaim().getId());
        assertEquals("2019-01-26", result.getContent().get(0).getClaimSubmissionDate().toString());
        // assertEquals("2019-01-26", result.getContent().get(0).getAppliedPaymentDate().toString());

        final String result2 = claimServiceUnderTest.exportClaimsFromClaimDTOs(result);


        int age = DateUtil.getAgeInDaysFromSQLDate(java.sql.Date.valueOf("2019-01-26"));
        // Verify the results
        assertEquals("Patient Id,Claim Id,Prescription Id,Patient Id,Patient First Name,Patient Last Name,Date of Service,Status,Patient Branch,Billing Branch,RX Branch,Total Billable,Total Allowable,Total Insurance Amount,Total Insurance Balance,Total Insurance Payments,Total Patient Responsibility Amount,Total Patient Responsibility Balance,Total Patient Responsibility Paid,User Id,Patient Insurance,Claim Submission Date,Submission Aging (days-type),Applied Payment Date\n" +
                "4,1,99,4,\"Patricia\",\"Benner\",,New Claim,San Antonio,San Antonio,San Antonio,0,0,0,10,0,0,0,0,1,\"WellMed-United Healthcare - 123456789\",2019-01-26," + age + "-P,\n", result2);
    }

    @Test
    void testConstructQuerySegment() {
        // Setup
        Date date = new GregorianCalendar(2019, Calendar.JANUARY, 1).getTime();
        // Run the test
        final String result = claimServiceUnderTest.constructQuerySegment(0L, "status", 0L, 0L, 0L, 0L, 0L, date, date, null, null, false, null);

        // Verify the results
        String expected = " FROM claim c LEFT JOIN (SELECT claim_id, MAX(submission_date) AS submission_date FROM claim_submission GROUP BY claim_id) AS cs ON cs.claim_id = c.id LEFT JOIN (SELECT claim_id, MAX(applied_date) AS applied_date FROM applied_payment GROUP BY claim_id) AS ap ON ap.claim_id = c.id LEFT JOIN prescription rx ON c.prescription_id =  rx.id LEFT JOIN patient p ON rx.patient_id = p.id LEFT JOIN patient_insurance pi ON c.responsible_patient_insurance_id = pi.id LEFT JOIN insurance_company ic ON pi.insurance_company_id = ic.id LEFT JOIN branch b ON c.billing_branch_id = b.id LEFT JOIN nymbl_master.user n_user ON c.user_id = n_user.id INNER JOIN task t ON c.id = t.claim_id AND t.type = 'follow_up' WHERE c.nymbl_status_id = 0 AND c.user_id = 0 AND pi.insurance_company_id = 0 AND p.id = 0 AND c.billing_branch_id =0 AND c.id =0 AND t.due_date BETWEEN '2019-01-01 00:00:00' AND '2019-01-01 23:59:59'";
//        String expected = " FROM claim c LEFT JOIN (SELECT claim_id, MAX(submission_date) AS submission_date FROM claim_submission GROUP BY claim_id) AS cs ON cs.claim_id = c.id LEFT JOIN (SELECT claim_id, MAX(applied_date) AS applied_date FROM applied_payment GROUP BY claim_id) AS ap ON ap.claim_id = c.id LEFT JOIN prescription rx ON c.prescription_id =  rx.id LEFT JOIN patient p ON rx.patient_id = p.id LEFT JOIN patient_insurance pi ON c.responsible_patient_insurance_id = pi.id LEFT JOIN insurance_company ic ON pi.insurance_company_id = ic.id LEFT JOIN branch b ON c.billing_branch_id = b.id INNER JOIN task t ON c.id = t.claim_id WHERE c.status = 'status' AND c.nymbl_status_id = 0 AND c.user_id = 0 AND pi.insurance_company_id = 0 AND p.id = 0 AND c.billing_branch_id =0 AND c.id =0 AND t.due_date BETWEEN '2019-01-01 00:00:00' AND '2019-01-01 23:59:59'";
        assertEquals(expected, result);
    }

    @Test
    void testGetClaimsByClaimSubmissionDate() {
        // Setup
        final Date startDate = new GregorianCalendar(2017, Calendar.JANUARY, 1, 0, 0, 0).getTime();
        final Date endDate = new GregorianCalendar(2017, Calendar.JANUARY, 1, 0, 0, 0).getTime();
        when(mockClaimRepository.getClaimsByClaimSubmissionDate(new GregorianCalendar(2017, Calendar.JANUARY, 1, 0, 0, 0).getTime(), new GregorianCalendar(2017, Calendar.JANUARY, 1, 0, 0, 0).getTime())).thenReturn(Fake.getClaimList());
        when(mockUserService.getUserById(Fake.ID)).thenReturn(Fake.getUserFakeID());

        // Run the test
        final List<Claim> result = claimServiceUnderTest.getClaimsByClaimSubmissionDate(startDate, endDate);

        // Verify the results
        assertEquals(Fake.ID, result.get(0).getId());
        assertEquals(Fake.ID, result.get(0).getUser().getId());
    }

    @Test
    void testGetActiveClaimsByFirstClaimSubmissionDate() {
        // Setup
        final Date startDate = new GregorianCalendar(2017, Calendar.JANUARY, 1, 0, 0, 0).getTime();
        final Date endDate = new GregorianCalendar(2017, Calendar.JANUARY, 1, 0, 0, 0).getTime();
        when(mockClaimRepository.findByActivePrescriptionPatientPrimaryBranchIdAndDateOfServiceBetween(null, new GregorianCalendar(2017, Calendar.JANUARY, 1, 0, 0, 0).getTime(), new GregorianCalendar(2017, Calendar.JANUARY, 1, 0, 0, 0).getTime())).thenReturn(Fake.getClaimList());
        when(mockUserService.getUserById(Fake.ID)).thenReturn(Fake.getUserFakeID());

        // Run the test
        final List<Claim> result = claimServiceUnderTest.getActiveClaimsByFirstClaimSubmissionDate(startDate, endDate);

        // Verify the results
        assertEquals(Fake.ID, result.get(0).getId());
        assertEquals(Fake.ID, result.get(0).getUser().getId());
    }

    @Test
    void testFindByAdditionalCommentIsNotNull() {
        // Setup
        when(mockClaimRepository.findByAdditionalCommentIsNotNull()).thenReturn(Fake.getClaimList());
        when(mockUserService.getUserById(Fake.ID)).thenReturn(Fake.getUserFakeID());

        // Run the test
        final List<Claim> result = claimServiceUnderTest.findByAdditionalCommentIsNotNull();

        // Verify the results
        assertEquals(Fake.ID, result.get(0).getId());
        assertEquals(Fake.ID, result.get(0).getUser().getId());
    }

    @Test
    void testBuildClaimForSearch() {

        final Claim result = claimServiceUnderTest.buildClaimForSearch();
        // Verify the results
        assertNull(result.getTotalClaimAmount());
        assertNull(result.getTotalClaimPaid());
        assertNull(result.getTotalClaimBalance());
        assertNull(result.getTotalPtResponsibilityAmount());
        assertNull(result.getTotalPtResponsibilityPaid());
        assertNull(result.getTotalPtResponsibilityBalance());
    }

    @Test
    void testGetTotalAmountPaid() {
        // Setup
        final Claim claim = Fake.getClaim();
        claim.setPatientInsuranceId(Fake.ID2);
        claim.getPatientInsurance().setId(Fake.ID2);
        InsuranceVerification insuranceVerification = Fake.getInsuranceVerification();
        insuranceVerification.setCarrierType(InsuranceType.secondary.name());
        when(mockInsuranceVerificationService.findByPatientInsuranceIdAndPrescriptionId(anyLong(), anyLong())).thenReturn(insuranceVerification);

        List<Claim> claimList = Fake.getClaimList();
        claimList.get(0).setTotalClaimPaid(BigDecimal.TEN);
        when(mockClaimRepository.findByPrescriptionId(anyLong())).thenReturn(claimList);

        when(mockUserService.getUserById(anyLong())).thenReturn(Fake.getUserFakeID());

        // Run the test
        String result = claimServiceUnderTest.getTotalAmountPaid(claim);

        // Verify the results
        assertEquals("10.00", result);

        claim.setTotalClaimPaid(BigDecimal.TEN);
        insuranceVerification = Fake.getInsuranceVerification();
        insuranceVerification.setCarrierType(InsuranceType.primary.name());
        when(mockInsuranceVerificationService.findByPatientInsuranceIdAndPrescriptionId(anyLong(), anyLong())).thenReturn(insuranceVerification);

        result = claimServiceUnderTest.getTotalAmountPaid(claim);

        // Verify the results
        assertEquals("10.00", result);
    }

    @Test
    void testGetAllClaimsWithActivePrescriptionAndOutstandingBalanceByBranch() {
        // Setup
        when(mockClaimRepository.getAllClaimsWithActivePrescriptionAndOutstandingBalanceByBranch(Fake.ID)).thenReturn(Fake.getClaimList());

        when(mockUserService.getUserById(Fake.ID)).thenReturn(Fake.getUserFakeID());

        // Run the test
        final List<Claim> result = claimServiceUnderTest.getAllClaimsWithActivePrescriptionAndOutstandingBalanceByBranch(Fake.ID);

        // Verify the results
        assertEquals(Fake.ID, result.get(0).getId());
        assertEquals(Fake.ID, result.get(0).getUser().getId());
    }

    @Test
    void testAddClaim() {
        // Setup
        final java.sql.Date dateOfService = new java.sql.Date(Fake.ID);

        // Configure InsuranceVerificationService.findByPrescriptionId(...).
        List<InsuranceVerification> insuranceVerificationList = Fake.getInsuranceVerificationList();
        when(mockInsuranceVerificationService.findByPrescriptionId(Fake.ID)).thenReturn(insuranceVerificationList);

        // Configure ClaimRepository.findByPrescriptionId(...).
        List<Claim> claimList = Fake.getClaimList();
        claimList.get(0).setId(null);
        NotificationType notificationType = new NotificationType();
        notificationType.setName("Claim Message");
        claimList.get(0).setUser(Fake.getUserFakeID());
        claimList.get(0).getUser().setNotificationTypes(List.of(notificationType));
        when(mockClaimRepository.findByPrescriptionId(Fake.ID)).thenReturn(claimList);

        when(mockUserService.getUserById(Fake.ID)).thenReturn(Fake.getUserFakeID());

        // Configure FinancialResponsibilityService.findByPrescriptionId(...).
        final FinancialResponsibility financialResponsibility = new FinancialResponsibility();
        financialResponsibility.setId(Fake.ID);
        when(mockFinancialResponsibilityService.findByPrescriptionId(Fake.ID)).thenReturn(financialResponsibility);

        // Configure PrescriptionService.findOne(...).
        when(mockPrescriptionService.findOne(Fake.ID)).thenReturn(Fake.getPrescription());

        // Configure InsuranceVerificationService.findByPrescriptionIdAndCarrierType(...).
        when(mockInsuranceVerificationService.findFirstByPrescriptionIdAndCarrierTypeOrderByIdDesc(anyLong(), anyString())).thenReturn(Fake.getInsuranceVerification());

        // Configure InsuranceVerificationLCodeService.findByInsuranceVerificationId(...).
        when(mockInsuranceVerificationLCodeService.findByInsuranceVerificationId(anyLong())).thenReturn(Fake.getInsuranceVerification_l_codeList());

//        List<ItemPhysical> physicalItems
        when(mockItemPhysicalService.findByPrescription(1L, null)).thenReturn(Collections.singletonList(Fake.getItemPhysical(Fake.ID, 0)));

        // Configure PrescriptionService.save(...).
        when(mockPrescriptionService.save(any(Prescription.class))).thenCallRealMethod();

        // Configure InsuranceVerificationLCodeService.save(...).
        when(mockInsuranceVerificationLCodeService.save(any(InsuranceVerification_L_Code.class))).thenCallRealMethod();

        when(mockUserService.getCurrentUser()).thenReturn(Fake.getUserFakeID());

        SystemSetting ss = new SystemSetting();
        ss.setId(Fake.ID);
        ss.setSection("billing");
        ss.setField("auto_lock_wip");
        ss.setValue("Y");
        when(mockSystemSettingService.findBySectionAndField("billing", "auto_lock_wip")).thenReturn(ss);
        // Configure PrescriptionSectionService.findByPrescriptionIdAndLockedIsFalse(...).
        final PrescriptionSection prescriptionSection = new PrescriptionSection();
        when(mockPrescriptionSectionService.findByPrescriptionIdAndLockedIsFalse(Fake.ID)).thenReturn(Collections.singletonList(prescriptionSection));

        // Configure PrescriptionSectionService.save(...).
        when(mockPrescriptionSectionService.save(any(PrescriptionSection.class))).thenCallRealMethod();
        when(mockUserNotificationService.save(any(UserNotification.class))).thenCallRealMethod();
        when(mockNotificationService.createClaimMessage(anyLong(), anyString())).thenCallRealMethod();

        // Run the test
        final Claim result = claimServiceUnderTest.addClaim(Fake.ID, Fake.ID, Fake.ID, dateOfService, false);

        assertEquals(Fake.ID, result.getId());
        assertEquals(Fake.ID, ((Claim) TableObjectContainer.getObjectFromList(Claim.class.getName(), 0)).getId());
        assertEquals(Fake.ID, ((Claim) TableObjectContainer.getObjectFromList(Claim.class.getName(), 0)).getPrescriptionId());
        assertEquals(Fake.NEW_CLAIM_ID, ((Claim) TableObjectContainer.getObjectFromList(Claim.class.getName(), 0)).getNymblStatusId());

        assertEquals(BigDecimal.valueOf(Double.parseDouble("109.82")), ((Claim) TableObjectContainer.getObjectFromList(Claim.class.getName(), 0)).getTotalClaimAmount());
        assertEquals(BigDecimal.ZERO, ((Claim) TableObjectContainer.getObjectFromList(Claim.class.getName(), 0)).getTotalClaimPaid());
        assertEquals(BigDecimal.ZERO, ((Claim) TableObjectContainer.getObjectFromList(Claim.class.getName(), 0)).getTotalPtResponsibilityPaid());
        assertEquals(BigDecimal.valueOf(Double.parseDouble("109.82")), ((Claim) TableObjectContainer.getObjectFromList(Claim.class.getName(), 0)).getTotalClaimBalance());
        assertEquals(BigDecimal.ZERO, ((Claim) TableObjectContainer.getObjectFromList(Claim.class.getName(), 0)).getTotalPtResponsibilityAmount());
        assertEquals(BigDecimal.ZERO, ((Claim) TableObjectContainer.getObjectFromList(Claim.class.getName(), 0)).getTotalPtResponsibilityBalance());
        assertEquals("116", ((Claim) TableObjectContainer.getObjectFromList(Claim.class.getName(), 0)).getPatientInsuranceId().toString());
        assertEquals(BigDecimal.valueOf(Double.parseDouble("109.82")), ((InsuranceVerification_L_Code) TableObjectContainer.getObjectFromList(InsuranceVerification_L_Code.class.getName(), 0)).getTotalCharge());
        assertEquals(BigDecimal.valueOf(Double.parseDouble("109.82")), ((InsuranceVerification_L_Code) TableObjectContainer.getObjectFromList(InsuranceVerification_L_Code.class.getName(), 0)).getTotalAllowable());

        //assertNull(((Prescription) TableObjectContainer.getObjectFromList(Prescription.class.getName(), 0)).getClericalUserId());

        assertEquals(true, ((PrescriptionSection) TableObjectContainer.getObjectFromList(PrescriptionSection.class.getName(), 0)).getLocked());
        assertEquals(Fake.ID, ((PrescriptionSection) TableObjectContainer.getObjectFromList(PrescriptionSection.class.getName(), 0)).getUserId());

    }

    @Test
    void testCreateClaimFile() {
        // Setup
//        final String filename = "filename";
//        final Long claimId = Fake.ID;
//        final Long branchId = Fake.ID;
//        final String contents = "contents";
//        when(mockClaimFileService.save(new ClaimFile())).thenReturn(new ClaimFile());
//
//        // Run the test
//        claimServiceUnderTest.createClaimFile(filename, claimId, branchId, contents);

        // Verify the results
    }

    @Test
    void testCreateClaimSubmission() {
        // Setup
//        final Long claimId = Fake.ID;
//        final Long patientInsuranceId = Fake.ID;
//        when(mockUserService.getCurrentUser()).thenReturn(new User());
//        when(mockClaimSubmissionService.save(new ClaimSubmission())).thenReturn(new ClaimSubmission());
//
//        // Run the test
//        claimServiceUnderTest.createClaimSubmission(claimId, patientInsuranceId);

        // Verify the results
    }

    @Test
    void testPopulateGreaterThanZeroTotalClaimBalanceInsuranceClaims() {
        // Setup
        final Map<Long, List<Claim>> greaterThanZeroTotalClaimBalanceInsuranceClaimsMap = new HashMap<>();

        // Run the test
        final List<Claim> result = claimServiceUnderTest.populateGreaterThanZeroTotalClaimBalanceInsuranceClaims(greaterThanZeroTotalClaimBalanceInsuranceClaimsMap);

        // Verify the results
        assertEquals(Collections.EMPTY_LIST, result);
    }

    @Test
    void testPopulateClaimIdClaimSubmissionMap() {
        // Setup
        when(mockClaimSubmissionService.findByClaimIdIn(anyList())).thenReturn(Fake.getClaimSubmissionList());

        // Run the test
        final Map<Long, List<ClaimSubmission>> result = claimServiceUnderTest.populateClaimIdClaimSubmissionMap(Fake.getClaimList());

        // Verify the results
        assertEquals(1, result.size());
        assertEquals(Fake.ID, result.get(Fake.ID).get(0).getClaimId());
        assertEquals(Fake.ID, result.get(Fake.ID).get(0).getClaim().getId());
    }

    @Test
    void testPopulateGreaterThanZeroTotalClaimBalanceInsuranceClaimsByInsuranceIdMap() {
        // Setup
        List<Claim> claimList = Fake.getClaimList();
        claimList.add(Fake.getClaim());
        // Run the test
        final Map<Long, List<Claim>> result = claimServiceUnderTest.populateGreaterThanZeroTotalClaimBalanceInsuranceClaimsByInsuranceIdMap(claimList, Fake.getInsuranceCompanyList());

        // Verify the results
        assertEquals(1, result.size());
        assertEquals(2, result.get(Fake.ID).size());
    }

    @Test
    void testReassignClaims() {
        // Setup
        when(mockClaimRepository.findById(Fake.ID)).thenReturn(Optional.of(Fake.getClaim()));
        when(mockUserService.getCurrentUser()).thenReturn(Fake.getUserFakeID());
        // Run the test
        claimServiceUnderTest.reassignClaims(Collections.singletonList(Fake.ID), Fake.ID);

        // Verify the results
        assertEquals(Fake.ID, ((Claim) TableObjectContainer.getObjectFromList(Claim.class.getName(), 0)).getUserId());
    }

    @Test
    void testFindByPrescriptionIdAndPatientInsuranceId() {
        // Setup
        final Long prescriptionId = Fake.ID;
        final Long patientInsuranceId = Fake.ID;
        final Claim expectedResult = new Claim();
        when(mockClaimRepository.findTop1ByPrescriptionIdAndPatientInsuranceIdOrderByIdDesc(Fake.ID, Fake.ID)).thenReturn(new Claim());
        when(mockUserService.getUserById(Fake.ID)).thenReturn(new User());

        // Run the test
        final Claim result = claimServiceUnderTest.findByPrescriptionIdAndPatientInsuranceId(prescriptionId, patientInsuranceId);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    void testGetOutstandingPatientResponsibilityBalanceClaims() {
        // Setup
        final Long branchId = Fake.ID;
        when(mockClaimRepository.findAllByBranchIdAndTotalPtResponsibilityBalanceIsGreaterThan(Fake.ID, new BigDecimal("0.00"))).thenReturn(Collections.emptyList());
        when(mockClaimRepository.findAllByTotalPtResponsibilityBalanceIsGreaterThan(new BigDecimal("0.00"))).thenReturn(Collections.emptyList());
        when(mockUserService.getUserById(Fake.ID)).thenReturn(new User());

        // Run the test
        final List<Claim> result = claimServiceUnderTest.getOutstandingPatientResponsibilityBalanceClaims(branchId);

        // Verify the results
        assertEquals(Collections.EMPTY_LIST, result);
    }

    @Test
    void testUncollected() {
        // Setup
        java.sql.Date date = Fake.getSqlDate("2019-01-26");
        when(mockClaimRepository.findUncollectedByBranch(any(), any(Date.class), any(Date.class))).thenReturn(Fake.getClaimList());
        when(mockUserService.getUserById(Fake.ID)).thenReturn(Fake.getUserFakeID());

        // Run the test
        List<Claim> result = claimServiceUnderTest.uncollected(Fake.ID, date, date);

        // Verify the results
        assertEquals(1, result.size());
        assertEquals(Fake.ID, result.get(0).getUser().getId());

        result = claimServiceUnderTest.uncollected(null, date, date);

        // Verify the results
        assertEquals(1, result.size());
        assertEquals(Fake.ID, result.get(0).getUser().getId());

    }

    @Test
    void testGetAutoPostClaimResponseByClaimId() {
        // Setup
        AutoPostClaimResponse autoPostClaimResponse = new AutoPostClaimResponse();
        autoPostClaimResponse.setId(Fake.ID);
        when(mockAutoPostClaimResponseService.getAllByClaimId(Fake.ID)).thenReturn(Collections.singletonList(autoPostClaimResponse));

        // Run the test
        final List<AutoPostClaimResponse> result = claimServiceUnderTest.getAutoPostClaimResponseByClaimId(Fake.ID);

        // Verify the results
        assertEquals(Fake.ID, result.get(0).getId());
    }

    @Test
    void testClaimsSummary() {
        // Setup

        // Configure ClaimRepository.getActiveClaimsByFirstClaimSubmissionDate(...).
        when(mockClaimRepository.findByActivePrescriptionPatientPrimaryBranchIdAndDateOfServiceBetween(any(), any(Date.class), any(Date.class))).thenReturn(Fake.getClaimList());

        when(mockUserService.getUserById(Fake.ID)).thenReturn(Fake.getUserFakeID());

        // Configure ClaimRepository.findByDateOfServiceBetween(...).
        when(mockClaimRepository.findByDateOfServiceBetween(any(Date.class), any(Date.class))).thenReturn(Fake.getClaimList());

        // Configure InsuranceVerificationService.findByPatientInsuranceIdAndPrescriptionId(...).
        when(mockInsuranceVerificationService.findByPatientInsuranceIdAndPrescriptionId(any(), any())).thenReturn(Fake.getInsuranceVerification());

        // Configure InsuranceVerificationLCodeService.findByInsuranceVerificationId(...).
        when(mockInsuranceVerificationLCodeService.findByInsuranceVerificationId(anyLong())).thenReturn(Fake.getInsuranceVerification_l_codeList());

        when(mockUserService.findOne(Fake.ID)).thenReturn(Fake.getUserFakeID());

        // Run the test
        final Map<String, Object> result = claimServiceUnderTest.claimsSummary(new GregorianCalendar(2019, Calendar.JANUARY, 1).getTime(), new GregorianCalendar(2019, Calendar.JANUARY, 1).getTime(), Fake.ID, "submission");

        // Verify the results
        assertEquals(7, result.size());
        assertEquals(BigDecimal.ZERO, result.get("patientBalance"));
        assertEquals(BigDecimal.ZERO, result.get("patientPaid"));
        assertEquals(BigDecimal.ZERO, result.get("patientBilled"));
        assertEquals(BigDecimal.ZERO, result.get("insurancePaid"));
        assertEquals(BigDecimal.ZERO, result.get("insuranceBilled"));
        assertEquals(BigDecimal.TEN, result.get("insuranceBalance"));
//        assertEquals(Fake.ID, ((List<ClaimsSummaryDTO>) result.get("claimsSummaryDTOs")).get(0).getClaim().getId());
        assertEquals(Fake.ID, ((List<ClaimsSummaryDTO>) result.get("claimsSummaryDTOs")).get(0).getClaimsSummarySlimClaimDataDTO().getClaimId());
//        assertEquals(BigDecimal.TEN, ((List<ClaimsSummaryDTO>) result.get("claimsSummaryDTOs")).get(0).getClaim().getTotalClaimBalance());
        assertEquals(BigDecimal.TEN, ((List<ClaimsSummaryDTO>) result.get("claimsSummaryDTOs")).get(0).getClaimsSummarySlimClaimDataDTO().getTotalClaimBalance());
        assertEquals("220", ((List<ClaimsSummaryDTO>) result.get("claimsSummaryDTOs")).get(0).getIvlcs().get(0).getId().toString());
    }

    @Test
    void testClaimsSummaryPivotTableExport() {
        // Setup
        when(mockClaimRepository.findByActivePrescriptionPatientPrimaryBranchIdAndDateOfServiceBetween(any(), any(Date.class), any(Date.class))).thenReturn(Fake.getClaimList());
        when(mockUserService.getUserById(Fake.ID)).thenReturn(Fake.getUserFakeID());

        // Configure ClaimRepository.findByDateOfServiceBetween(...).
        when(mockClaimRepository.findByDateOfServiceBetween(any(Date.class), any(Date.class))).thenReturn(Fake.getClaimList());

        // Configure InsuranceVerificationService.findByPatientInsuranceIdAndPrescriptionId(...).
        when(mockInsuranceVerificationService.findByPatientInsuranceIdAndPrescriptionId(any(), any())).thenReturn(Fake.getInsuranceVerification());

        // Configure InsuranceVerificationLCodeService.findByInsuranceVerificationId(...).
        when(mockInsuranceVerificationLCodeService.findByInsuranceVerificationId(any())).thenReturn(Fake.getInsuranceVerification_l_codeList());

        when(mockUserService.findOne(Fake.ID)).thenReturn(Fake.getUserFakeID());

        // Run the test
        final String result = claimServiceUnderTest.claimsSummaryPivotTableExport(new GregorianCalendar(2019, Calendar.JANUARY, 1).getTime(), new GregorianCalendar(2019, Calendar.JANUARY, 1).getTime(), Fake.ID, "submission");

        // Verify the results
        assertEquals("Claim #,Prescription #,Branch,Device Name,Device Type,Treating Practitioner,Date Delivered,Insurance Company,Status,Ins. Billed,Ins. Balance,Ins. Paid,Pat. Billed,Pat. Balance,Pat. Paid,L-Code,Category,Mod 1,Mod 2,Mod 3,Mod 4,Total Allowable,Total Charge \n" +
                "1,99,San Antonio,DIABETIC SHOES AND INSERTS,orthotic,firstName lastName,,WellMed-United Healthcare,New Claim,0,10,0,0,0,0,,,,,,,,\n" +
                "1,99,San Antonio,DIABETIC SHOES AND INSERTS,orthotic,firstName lastName,,WellMed-United Healthcare,New Claim,,,,,,,A5500,Footwear,LT,RT,KX,,109.82,109.82\n", result);
    }

    @Test
    void testGetClaimTotalsReportDTOList() {
        Date date = Fake.getSqlDate("2019-01-01");
        String dateStart = DateUtil.getStringDate(date, DF_YYYY_MM_DD_HH_MM);
        String dateEnd = DateUtil.getStringDate(DateUtil.getEndOfDay(date), DF_YYYY_MM_DD_HH_MM);
        Object[] objects = new Object[]{0, 1, 2, 3};
        List<Object[]> list = new ArrayList<>();
        list.add(objects);
        when(mockClaimRepository.getTotalBilledReport(dateStart, dateEnd)).thenReturn(list);

        // Run the test
        final ClaimTotalsReportDTO result = claimServiceUnderTest.getClaimTotalsReportDTOList(date, date);

        // Verify the results
        assertEquals(Long.valueOf(0L), result.getClaimCount());
        assertEquals(BigDecimal.valueOf(Double.parseDouble("1.0")), result.getTotalAllowable());
        assertEquals(BigDecimal.valueOf(Double.parseDouble("5.0")), result.getTotalBillable());
        assertEquals(BigDecimal.valueOf(Double.parseDouble("3.0")), result.getTotalPatientResponsibility());
    }

    @Test
    void testOutstandingInsuranceBalances() {
        // Setup

        // Configure ClaimRepository.findByMostRecentClaimAndBranchId(...).
        when(mockClaimRepository.findByMostRecentClaimAndBranchId(anyLong())).thenReturn(Fake.getClaimList());

        // Configure ClaimRepository.findMostRecentClaim(...).
        when(mockClaimRepository.findMostRecentClaim()).thenReturn(Fake.getClaimList());

        // Configure InsuranceCompanyService.findAll(...).
        when(mockInsuranceCompanyService.findAll()).thenReturn(Fake.getInsuranceCompanyList());

        // Run the test
        List<Map<String, Object>> result = claimServiceUnderTest.outstandingInsuranceBalances(Fake.ID);

        // Verify the results
        assertEquals(2, result.size());
        assertEquals(BigDecimal.ZERO, result.get(0).get("thirtyDayBalance"));
        assertEquals(BigDecimal.ZERO, result.get(0).get("oneTwentyDayBalance"));
        assertEquals(BigDecimal.ZERO, result.get(0).get("ninetyDayBalance"));
        assertEquals(BigDecimal.ZERO, result.get(0).get("oneTwentyPlusDayBalance"));
        assertEquals(BigDecimal.TEN, result.get(0).get("noSubmissionBalance"));
        assertEquals(BigDecimal.TEN, result.get(0).get("insuranceBalance"));
        assertEquals(BigDecimal.ZERO, result.get(0).get("sixtyDayBalance"));
        assertEquals("WellMed-United Healthcare", ((InsuranceCompany) result.get(0).get("insuranceCompany")).getName());

        assertEquals(Fake.ID, ((List<ClaimDTO>) result.get(0).get("claimsWithBalance")).get(0).getClaim().getId());
        assertEquals(BigDecimal.ZERO, result.get(1).get("thirtyDayBalance"));
        assertEquals(BigDecimal.ZERO, result.get(1).get("oneTwentyDayBalance"));
        assertEquals(BigDecimal.ZERO, result.get(1).get("ninetyDayBalance"));
        assertEquals(BigDecimal.ZERO, result.get(1).get("oneTwentyPlusDayBalance"));
        assertEquals(BigDecimal.TEN, result.get(1).get("noSubmissionBalance"));
        assertEquals(BigDecimal.TEN, result.get(1).get("insuranceBalance"));
        assertEquals(BigDecimal.ZERO, result.get(1).get("sixtyDayBalance"));
        assertEquals("TOTALS FOR ALL INSURANCE COMPANIES", ((InsuranceCompany) result.get(1).get("insuranceCompany")).getName());

        Date juDate = new Date();
        DateTime today = new DateTime(juDate);
        List<ClaimSubmission> claimSubmissionList = Fake.getClaimSubmissionList();
        claimSubmissionList.get(0).setSubmissionDate(java.sql.Date.valueOf(String.valueOf(today.minusDays(15).toLocalDate())));
        when(mockClaimSubmissionService.findByClaimIdIn(anyList())).thenReturn(claimSubmissionList);

        result = claimServiceUnderTest.outstandingInsuranceBalances(null);

        // Verify the results
        assertEquals(2, result.size());
        assertEquals(BigDecimal.TEN, result.get(0).get("thirtyDayBalance"));
        assertEquals(BigDecimal.ZERO, result.get(0).get("oneTwentyDayBalance"));
        assertEquals(BigDecimal.ZERO, result.get(0).get("ninetyDayBalance"));
        assertEquals(BigDecimal.ZERO, result.get(0).get("oneTwentyPlusDayBalance"));
        assertEquals(BigDecimal.ZERO, result.get(0).get("noSubmissionBalance"));
        assertEquals(BigDecimal.TEN, result.get(0).get("insuranceBalance"));
        assertEquals(BigDecimal.ZERO, result.get(0).get("sixtyDayBalance"));
        assertEquals("WellMed-United Healthcare", ((InsuranceCompany) result.get(0).get("insuranceCompany")).getName());

        assertEquals(Fake.ID, ((List<ClaimDTO>) result.get(0).get("claimsWithBalance")).get(0).getClaim().getId());
        assertEquals(BigDecimal.TEN, result.get(1).get("thirtyDayBalance"));
        assertEquals(BigDecimal.ZERO, result.get(1).get("oneTwentyDayBalance"));
        assertEquals(BigDecimal.ZERO, result.get(1).get("ninetyDayBalance"));
        assertEquals(BigDecimal.ZERO, result.get(1).get("oneTwentyPlusDayBalance"));
        assertEquals(BigDecimal.ZERO, result.get(1).get("noSubmissionBalance"));
        assertEquals(BigDecimal.TEN, result.get(1).get("insuranceBalance"));
        assertEquals(BigDecimal.ZERO, result.get(1).get("sixtyDayBalance"));
        assertEquals("TOTALS FOR ALL INSURANCE COMPANIES", ((InsuranceCompany) result.get(1).get("insuranceCompany")).getName());
    }

    @Test
    void testPopulateInsuranceBalances() {
        // Setup
        Date juDate = new Date();
        DateTime today = new DateTime(juDate);
        DateTime thirtyDay = today.minusDays(29);
        DateTime sixtyDay = today.minusDays(59);
        DateTime ninetyDay = today.minusDays(89);
        DateTime oneTwentyDay = today.minusDays(119);
        DateTime oneTwentyPlusDay = today.minusDays(120);

        InsuranceBalances insuranceBalances = new InsuranceBalances();
        final ClaimDTO dto = new ClaimDTO(Fake.getClaim());
        final ClaimSubmission submission = Fake.getClaimSubmission();
        submission.setSubmissionDate(java.sql.Date.valueOf(String.valueOf(today.minusDays(15).toLocalDate())));

        // Run the test
        claimServiceUnderTest.populateInsuranceBalances(thirtyDay, sixtyDay, ninetyDay, oneTwentyDay, oneTwentyPlusDay, insuranceBalances, dto, submission);

        // Verify the results
        assertEquals(BigDecimal.TEN, insuranceBalances.thirtyDayBalance);
        assertEquals(BigDecimal.TEN, insuranceBalances.totalThirtyDayBalance);

        insuranceBalances = new InsuranceBalances();
        submission.setSubmissionDate(java.sql.Date.valueOf(String.valueOf(today.minusDays(35).toLocalDate())));
        claimServiceUnderTest.populateInsuranceBalances(thirtyDay, sixtyDay, ninetyDay, oneTwentyDay, oneTwentyPlusDay, insuranceBalances, dto, submission);
        // Verify the results
        assertEquals(BigDecimal.TEN, insuranceBalances.sixtyDayBalance);
        assertEquals(BigDecimal.TEN, insuranceBalances.totalSixtyDayBalance);

        insuranceBalances = new InsuranceBalances();
        submission.setSubmissionDate(java.sql.Date.valueOf(String.valueOf(today.minusDays(65).toLocalDate())));
        claimServiceUnderTest.populateInsuranceBalances(thirtyDay, sixtyDay, ninetyDay, oneTwentyDay, oneTwentyPlusDay, insuranceBalances, dto, submission);
        // Verify the results
        assertEquals(BigDecimal.TEN, insuranceBalances.ninetyDayBalance);
        assertEquals(BigDecimal.TEN, insuranceBalances.totalNinetyDayBalance);

        insuranceBalances = new InsuranceBalances();
        submission.setSubmissionDate(java.sql.Date.valueOf(String.valueOf(today.minusDays(95).toLocalDate())));
        claimServiceUnderTest.populateInsuranceBalances(thirtyDay, sixtyDay, ninetyDay, oneTwentyDay, oneTwentyPlusDay, insuranceBalances, dto, submission);
        // Verify the results
        assertEquals(BigDecimal.TEN, insuranceBalances.oneTwentyDayBalance);
        assertEquals(BigDecimal.TEN, insuranceBalances.totalOneTwentyDayBalance);

        insuranceBalances = new InsuranceBalances();
        submission.setSubmissionDate(java.sql.Date.valueOf(String.valueOf(today.minusDays(125).toLocalDate())));
        claimServiceUnderTest.populateInsuranceBalances(thirtyDay, sixtyDay, ninetyDay, oneTwentyDay, oneTwentyPlusDay, insuranceBalances, dto, submission);
        // Verify the results
        assertEquals(BigDecimal.TEN, insuranceBalances.oneTwentyPlusDayBalance);
        assertEquals(BigDecimal.TEN, insuranceBalances.totalOneTwentyPlusDayBalance);
    }

    @Test
    void testGetClaimSubmissionWithMaxId() {
        // Setup
        final Map<Long, List<ClaimSubmission>> claimIdClaimSubmissionMap = new HashMap<>();
        List<ClaimSubmission> claimSubmissionList = Fake.getClaimSubmissionList();
        ClaimSubmission claimSubmission = Fake.getClaimSubmission();
        claimSubmission.setId(Fake.ID2);
        claimSubmissionList.add(claimSubmission);
        claimIdClaimSubmissionMap.put(Fake.ID, claimSubmissionList);
        final ClaimDTO dto = new ClaimDTO(Fake.getClaim());

        // Run the test
        final ClaimSubmission result = claimServiceUnderTest.getClaimSubmissionWithMaxId(claimIdClaimSubmissionMap, dto);

        // Verify the results
        assertEquals(Fake.ID2, result.getId());
    }

    @Test
    void testSearch() {
        // Setup
        Sort sort = Sort.by(Sort.DEFAULT_DIRECTION, "id");
        final Pageable pageable =  PageRequest.of(0, 1000, sort);
        // Configure taskService.findByClaimIdAndType(...).
        final Task followUpTaskBefore = new Task();
        followUpTaskBefore.setId(Fake.ID);
        followUpTaskBefore.setClaimId(Fake.ID);
        followUpTaskBefore.setDueDate(Fake.getSqlDate("2019-01-10"));

        final Task followUpTaskAfter = new Task();
        followUpTaskAfter.setId(Fake.ID2);
        followUpTaskAfter.setClaimId(Fake.ID);
        followUpTaskAfter.setDueDate(Fake.getSqlDate("2019-01-11"));
        final List<Task> followUpTasks = Arrays.asList(followUpTaskBefore, followUpTaskAfter);
        when(mockTaskService.findByClaimIdAndType(Fake.ID, "follow_up")).thenReturn(followUpTasks);

        when(mockUserService.getUserById(Fake.ID)).thenReturn(Fake.getUserFakeID());
        Fake<Claim> fake = new Fake<>();
        Page<Claim> pageableClaim = fake.getFakePageImpl(Fake.getClaim());
        when(mockClaimRepository.findAll(any(Pageable.class))).thenReturn(pageableClaim);
        when(mockClaimRepository.findAll(any(Example.class), any(Pageable.class))).thenReturn(pageableClaim);

        // Run the test
        final List<Claim> result = claimServiceUnderTest.search(Fake.ID, "status", Fake.ID, Fake.ID, Fake.ID, Fake.ID, Fake.getSqlDate("2019-01-01"), Fake.getSqlDate("2019-01-20"), pageable);

        // Verify the results
        assertEquals(Fake.ID, result.get(0).getId());
    }

    @Test
    void testGetClaimExample() {
        // Run the test
        final Example<Claim> result = claimServiceUnderTest.getClaimExample(Fake.ID, "status", Fake.ID, Fake.ID, Fake.ID, Fake.ID, Fake.getClaim());

        // Verify the results
        assertEquals("patientId", ((ExampleMatcher.PropertySpecifier) result.getMatcher().getPropertySpecifiers().getSpecifiers().toArray()[0]).getPath());
        assertEquals("EXACT", ((ExampleMatcher.PropertySpecifier) result.getMatcher().getPropertySpecifiers().getSpecifiers().toArray()[0]).getStringMatcher().toString());
        assertEquals("4", result.getProbe().getPatientInsurance().getPatientId().toString());
        assertEquals("status", ((ExampleMatcher.PropertySpecifier) result.getMatcher().getPropertySpecifiers().getSpecifiers().toArray()[1]).getPath());
        assertEquals("EXACT", ((ExampleMatcher.PropertySpecifier) result.getMatcher().getPropertySpecifiers().getSpecifiers().toArray()[1]).getStringMatcher().toString());
        assertEquals("nymblStatusId", ((ExampleMatcher.PropertySpecifier) result.getMatcher().getPropertySpecifiers().getSpecifiers().toArray()[2]).getPath());
        assertEquals("EXACT", ((ExampleMatcher.PropertySpecifier) result.getMatcher().getPropertySpecifiers().getSpecifiers().toArray()[2]).getStringMatcher().toString());
        assertEquals("primaryBranchId", ((ExampleMatcher.PropertySpecifier) result.getMatcher().getPropertySpecifiers().getSpecifiers().toArray()[3]).getPath());
        assertEquals("EXACT", ((ExampleMatcher.PropertySpecifier) result.getMatcher().getPropertySpecifiers().getSpecifiers().toArray()[3]).getStringMatcher().toString());
        assertEquals("insuranceCompanyId", ((ExampleMatcher.PropertySpecifier) result.getMatcher().getPropertySpecifiers().getSpecifiers().toArray()[4]).getPath());
        assertEquals("EXACT", ((ExampleMatcher.PropertySpecifier) result.getMatcher().getPropertySpecifiers().getSpecifiers().toArray()[4]).getStringMatcher().toString());
        assertEquals("userId", ((ExampleMatcher.PropertySpecifier) result.getMatcher().getPropertySpecifiers().getSpecifiers().toArray()[5]).getPath());
        assertEquals("EXACT", ((ExampleMatcher.PropertySpecifier) result.getMatcher().getPropertySpecifiers().getSpecifiers().toArray()[5]).getStringMatcher().toString());
    }
    //populateZirmedFacilityDataFields
    //isPrintZirmed

    @Test
    void testPopulateZirmedFacilityDataFields() {
        // Setup
        final Map<String, String> claimDefaults = new HashMap<>();
        claimDefaults.put("provider_information", "user_credentials");

        // Configure DeliveryLocationRepository.findById(...).
        final DeliveryLocation deliveryLocation = new DeliveryLocation();
        deliveryLocation.setId(Fake.ID);
        deliveryLocation.setName("name");
        deliveryLocation.setStreetAddress("address");
        deliveryLocation.setCity("city");
        deliveryLocation.setState("state");
        deliveryLocation.setZipcode("zip");
        deliveryLocation.setNpi("npi");

        when(mockDeliveryLocationRepository.findById(anyLong())).thenReturn(Optional.of(deliveryLocation));
        String[] dataFields = new String[100];
        // Run the test
        claimServiceUnderTest.populateZirmedFacilityDataFields(Fake.getBranch(), "Neu Limbs, LLC.", claimDefaults, Fake.getPrescription(), dataFields);

        // Verify the results
        assertEquals(100, dataFields.length);
        assertEquals("Neu Limbs, LLC.", dataFields[Waystar837P.FacilityName.ordinal()]);
        assertEquals("**********", dataFields[Waystar837P.FacilityNPI.ordinal()]);
        assertEquals("5282 Medical Drive, Suite 105", dataFields[Waystar837P.FacilityAdd1.ordinal()]);
        assertEquals("San Antonio", dataFields[Waystar837P.FacilityCity.ordinal()]);
        assertEquals("TX", dataFields[Waystar837P.FacilityState.ordinal()]);
        assertEquals("78229", dataFields[Waystar837P.FacilityZip.ordinal()]);

        Branch branch = Fake.getBranch();
        branch.setHideServiceFacilityLocation(true);
        dataFields = new String[100];
        // Run the test
        claimServiceUnderTest.populateZirmedFacilityDataFields(branch, "", claimDefaults, Fake.getPrescription(), dataFields);

        // Verify the results
        assertEquals(100, dataFields.length);
        assertEquals("", dataFields[Waystar837P.FacilityName.ordinal()]);
        assertEquals("", dataFields[Waystar837P.FacilityAdd1.ordinal()]);
        assertEquals("", dataFields[Waystar837P.FacilityCity.ordinal()]);
        assertEquals("", dataFields[Waystar837P.FacilityState.ordinal()]);
        assertEquals("", dataFields[Waystar837P.FacilityZip.ordinal()]);

        dataFields = new String[100];
        Prescription prescription = Fake.getPrescription();
        prescription.setUseAddress(true);
        prescription.setDeliveryLocation("other");
        prescription.setTreatingPractitioner(Fake.getUserFakeID());
        prescription.setDeliveryLocationAddress("name,address,city,state,zip");
        // Run the test
        claimServiceUnderTest.populateZirmedFacilityDataFields(Fake.getBranch(), "name", claimDefaults, prescription, dataFields);

        // Verify the results
        assertEquals(100, dataFields.length);
        assertEquals("name", dataFields[Waystar837P.FacilityName.ordinal()]);
        assertEquals("**********", dataFields[Waystar837P.FacilityNPI.ordinal()]);
        assertEquals("address", dataFields[Waystar837P.FacilityAdd1.ordinal()]);
        assertEquals("city", dataFields[Waystar837P.FacilityCity.ordinal()]);
        assertEquals("state", dataFields[Waystar837P.FacilityState.ordinal()]);
        assertEquals("zip", dataFields[Waystar837P.FacilityZip.ordinal()]);

        dataFields = new String[100];
        prescription = Fake.getPrescription();
        prescription.setUseAddress(true);
        prescription.setDeliveryLocation("1");

        prescription.setTreatingPractitioner(Fake.getUserFakeID());
        prescription.setDeliveryLocationAddress("name,address,city,state,zip");
        // Run the test
        claimServiceUnderTest.populateZirmedFacilityDataFields(Fake.getBranch(), "", claimDefaults, prescription, dataFields);

        // Verify the results
        assertEquals(100, dataFields.length);
        assertEquals("name", dataFields[Waystar837P.FacilityName.ordinal()]);
        assertEquals("npi", dataFields[Waystar837P.FacilityNPI.ordinal()]);
        assertEquals("address", dataFields[Waystar837P.FacilityAdd1.ordinal()]);
        assertEquals("city", dataFields[Waystar837P.FacilityCity.ordinal()]);
        assertEquals("state", dataFields[Waystar837P.FacilityState.ordinal()]);
        assertEquals("zip", dataFields[Waystar837P.FacilityZip.ordinal()]);

    }

    @Test
    void testPopulateDataFieldsForInsuranceVerificationLCodes() {
        // Setup
        Claim claim = Fake.getClaim();

        Branch branch = Fake.getBranch();

        List<String> requests = new ArrayList<>();
        List<InsuranceVerification_L_Code> lCodes = Fake.getInsuranceVerification_l_codeList();
        lCodes.add(Fake.getInsuranceVerification_l_code());
        lCodes.add(Fake.getInsuranceVerification_l_code());
        lCodes.add(Fake.getInsuranceVerification_l_code());
        lCodes.add(Fake.getInsuranceVerification_l_code());
        lCodes.add(Fake.getInsuranceVerification_l_code());
        lCodes.add(Fake.getInsuranceVerification_l_code());
        String[] dataFields = new String[300];
        // Run the test

        claimServiceUnderTest.populateDataFieldsForInsuranceVerificationLCodes(claim, branch, "branch_npi", requests, lCodes, dataFields);
        // Verify the results
        assertEquals(300, dataFields.length);
        assertEquals("**********", dataFields[202]);
        assertEquals(1, requests.size());
        assertEquals("||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||10/29/2019||12||A5500|LT RT KX||109.82|2|||||**********||10/29/2019||12||A5500|LT RT KX||109.82|2|||||**********||10/29/2019||12||A5500|LT RT KX||109.82|2|||||**********||10/29/2019||12||A5500|LT RT KX||109.82|2|||||**********||10/29/2019||12||A5500|LT RT KX||109.82|2|||||**********||10/29/2019||12||A5500|LT RT KX||109.82|2|||||**********||10/29/2019||12||A5500|LT RT KX||109.82|2|||||**********||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||", requests.get(0));

    }

    @Test
    void generateOtherPatientInsuranceTest() {
        Long prescriptionId = 1L;

        InsuranceCompany primaryIC = createTestInsuranceCompany("Humana");
        InsuranceCompany secondaryIC = createTestInsuranceCompany("Blue Cross Blue Shield");
        InsuranceCompany tertiaryIC = createTestInsuranceCompany("Aetna");
        InsuranceCompany quaternaryIC = createTestInsuranceCompany("Medicare");

        PatientInsurance primaryPI = createTestPatientInsurance(1L, "Nymbl", "A", "Systems", "12345", primaryIC);
        PatientInsurance secondaryPI = createTestPatientInsurance(2L, "451", "B", "Tech", "67890", secondaryIC);
        PatientInsurance tertiaryPI = createTestPatientInsurance(3L, "Wayne", "C", "Enterprises", "45678", tertiaryIC);
        PatientInsurance quaternaryPI = createTestPatientInsurance(4L, "Richard", "John", "Grayson", "12390", quaternaryIC);

        InsuranceVerification primaryIV = createTestInsuranceVerification(1L, "primary", prescriptionId, primaryPI);
        InsuranceVerification secondaryIV = createTestInsuranceVerification(2L, "secondary", prescriptionId, secondaryPI);
        InsuranceVerification tertiaryIV = createTestInsuranceVerification(3L, "tertiary", prescriptionId, tertiaryPI);
        InsuranceVerification quaternaryIV = createTestInsuranceVerification(4L, "quaternary", prescriptionId, quaternaryPI);

        when(mockInsuranceVerificationService.findFirstByPrescriptionIdAndCarrierTypeOrderByIdDesc(prescriptionId, "primary")).thenReturn(primaryIV);
        when(mockInsuranceVerificationService.findFirstByPrescriptionIdAndCarrierTypeOrderByIdDesc(prescriptionId, "secondary")).thenReturn(secondaryIV);
        when(mockInsuranceVerificationService.findFirstByPrescriptionIdAndCarrierTypeOrderByIdDesc(prescriptionId, "tertiary")).thenReturn(tertiaryIV);
        when(mockInsuranceVerificationService.findFirstByPrescriptionIdAndCarrierTypeOrderByIdDesc(prescriptionId, "quaternary")).thenReturn(quaternaryIV);

        claimServiceUnderTest.generateOtherPatientInsurance(primaryIV);
        assertEquals(secondaryIV.getPatientInsurance().getFirstName(), "451");
        assertEquals(secondaryIV.getPatientInsurance().getInsuranceNumber(), "67890");
        assertEquals(secondaryIV.getPatientInsurance().getInsuranceCompany().getName(), "Blue Cross Blue Shield");

        claimServiceUnderTest.generateOtherPatientInsurance(secondaryIV);
        assertEquals(tertiaryIV.getPatientInsurance().getFirstName(), "Wayne");
        assertEquals(tertiaryIV.getPatientInsurance().getInsuranceNumber(), "45678");
        assertEquals(tertiaryIV.getPatientInsurance().getInsuranceCompany().getName(), "Aetna");

        claimServiceUnderTest.generateOtherPatientInsurance(tertiaryIV);
        assertEquals(quaternaryIV.getPatientInsurance().getFirstName(), "Richard");
        assertEquals(quaternaryIV.getPatientInsurance().getMiddleName(), "John");
        assertEquals(quaternaryIV.getPatientInsurance().getLastName(), "Grayson");

        when(mockInsuranceVerificationService.findFirstByPrescriptionIdAndCarrierTypeOrderByIdDesc(prescriptionId, "tertiary")).thenReturn(null);
        claimServiceUnderTest.generateOtherPatientInsurance(secondaryIV);
        assertEquals(primaryIV.getPatientInsurance().getFirstName(), "Nymbl");
        assertEquals(primaryIV.getPatientInsurance().getInsuranceNumber(), "12345");
        assertEquals(primaryIV.getPatientInsurance().getInsuranceCompany().getName(), "Humana");

        claimServiceUnderTest.generateOtherPatientInsurance(tertiaryIV);
        assertEquals(primaryIV.getPatientInsurance().getFirstName(), "Nymbl");
        assertEquals(primaryIV.getPatientInsurance().getInsuranceNumber(), "12345");
        assertEquals(primaryIV.getPatientInsurance().getInsuranceCompany().getName(), "Humana");
    }

    private InsuranceVerification createTestInsuranceVerification(Long id, String carrierType, Long prescriptionId, PatientInsurance pi) {
        InsuranceVerification iv = new InsuranceVerification();
        iv.setId(id);
        iv.setCarrierType(carrierType);
        iv.setPrescriptionId(prescriptionId);
        iv.setPatientInsurance(pi);
        return iv;
    }

    private PatientInsurance createTestPatientInsurance(Long id, String firstName, String middleName, String lastName,
                                                        String insuranceNumber, InsuranceCompany ic) {
        PatientInsurance pi = new PatientInsurance();
        pi.setId(id);
        pi.setFirstName(firstName);
        pi.setMiddleName(middleName);
        pi.setLastName(lastName);
        pi.setInsuranceNumber(insuranceNumber);
        pi.setInsuranceCompany(ic);
        return pi;
    }

    private InsuranceCompany createTestInsuranceCompany(String name) {
        InsuranceCompany ic = new InsuranceCompany();
        ic.setName(name);
        return ic;
    }

    @Test
    public void testDeleteClaim() {
        Claim testClaim = new Claim();
        testClaim.setId(26L);

        when(mockPaymentService.findByClaimId(testClaim.getId())).thenReturn(new ArrayList<>());
        when(mockAppliedPaymentService.findByClaimId(testClaim.getId())).thenReturn(new ArrayList<>());
        when(mockClaimSubmissionService.findByClaimId(testClaim.getId())).thenReturn(new ArrayList<>());
        when(mockClaimFileService.findByClaimId(testClaim.getId())).thenReturn(new ArrayList<>());
        when(mockAutoPostPatientService.findByClaimId(testClaim.getId())).thenReturn(new ArrayList<>());
        when(mockAutoPostClaimResponseService.getAllByClaimId(testClaim.getId())).thenReturn(new ArrayList<>());
        when(mockPatientStatementService.findAllByClaimIdIn(Collections.singletonList(testClaim.getId()))).thenReturn(new ArrayList<>());
        NymblStatusHistory nsh1 = new NymblStatusHistory();
        nsh1.setClaimId(testClaim.getId());
        nsh1.setNymblStatusId(11L);
        NymblStatusHistory nsh2 = new NymblStatusHistory();
        nsh2.setClaimId(testClaim.getId());
        nsh1.setNymblStatusId(31L);
        when(mockNymblStatusHistoryService.findByClaimId(testClaim.getId())).thenReturn(Arrays.asList(nsh1, nsh2));
        Task task1 = new Task();
        task1.setClaimId(testClaim.getId());
        when(mockTaskService.findByClaimId(testClaim.getId())).thenReturn(Collections.singletonList(task1));
        when(mockClaimRepository.getClaimIsAutoGeneratedCRT(anyLong())).thenReturn(false);

        ResponseEntity<?> result = claimServiceUnderTest.deleteClaim(testClaim.getId());
        assertEquals(result.getStatusCode(), HttpStatus.OK);
        assertEquals(result.getBody(), "This claim has been deleted");

        Payment payment1 = new Payment();
        payment1.setClaimId(testClaim.getId());
        when(mockPaymentService.findByClaimId(testClaim.getId())).thenReturn(Collections.singletonList(payment1));

        ResponseEntity<?> result2 = claimServiceUnderTest.deleteClaim(testClaim.getId());
        assertEquals(result2.getStatusCode(), HttpStatus.FORBIDDEN);
        assertEquals(result2.getBody(), "You cannot delete a claim that has an attached payment");
    }
}
