package com.nymbl.tenant.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.ObjectWriter;
import com.nymbl.Application;
import com.nymbl.config.TenantDatabaseConfig;
import com.nymbl.config.dto.reports.RxSalesReport;
import com.nymbl.tenant.TenantContext;
import org.json.JSONException;
import org.json.JSONObject;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;

import static org.junit.jupiter.api.Assertions.*;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = {Application.class, TenantDatabaseConfig.class})
@TestPropertySource(value = "classpath:test.properties")
class ReportServiceTest {

    @Autowired
    ReportService reportService;

    @BeforeEach
    void setUp() {
    }

    @AfterEach
    void tearDown() {
    }

//    @Test
//    void testGetSalesSummaryReport() throws JsonProcessingException, JSONException {
//        TenantContext.setCurrentTenant("transcend");
//        RxSalesReport test = reportService.getSalesSummaryReport(0L,55L, 99L);
//        ObjectWriter ow = new ObjectMapper().writer().withDefaultPrettyPrinter();
//        System.out.println(new JSONObject(ow.writeValueAsString(ow.writeValueAsString(test))).toString());
//        TenantContext.clear();
//        assertNotNull(test, "There is a real problem if we got to this point and it is null");
//    }
}