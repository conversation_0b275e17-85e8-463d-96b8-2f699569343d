package com.nymbl.tenant.service;

import com.nymbl.Application;
import com.nymbl.config.TenantDatabaseConfig;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = {Application.class, TenantDatabaseConfig.class})
@TestPropertySource(value = "classpath:test.properties")
class GeneralLedgerServiceTest {

//    private final String tenant = "teter";
//    private final String rootFolderTest = "/Users/<USER>/dumps/bireports";
//    private final String urlTesting = "****************************************************************************************************";

    @Autowired
    GeneralLedgerService generalLedgerService;

    @BeforeEach
    void setUp() {
    }

    @AfterEach
    void tearDown() {
    }

//    @Test
//    void runDailyLiveUpdateTest(){
//        TenantContext.setCurrentTenant("leverage_op");
//        System.out.println("start");
//        generalLedgerService.runDailyLiveGeneralLedgerPopulation();
//        System.out.println("end");
//        TenantContext.clear();
//    }

//    @Test
//    void runDailyGeneralLedgerHistoryPopulation() {
//         Need for test here.
//         Possible test check zero full table sum
//         Need to get base figures so this  does not change.
//
//        This is  not test just debug run below
//        TenantContext.setCurrentTenant(tenant);
//        generalLedgerService.runDailyGeneralLedgerHistoryPopulation(tenant);
//        generalLedgerService.importGeneralLedgerHistoryToMultitenant();
//        TenantContext.clear();
//    }
//
//    @Test
//    void loadForeignKeys() {
//    }

//    @Test
//    void testIsStaticGeneralLedgerEntryPresentForYearAndPeriod(){
//        TenantContext.setCurrentTenant(tenant);
//        boolean resultMay = generalLedgerService.isStaticGeneralLedgerEntryPresentForYearAndPeriod(2021L, 5L);
//        System.out.println(resultMay);
//        assert resultMay == true;
//        boolean resultJune = generalLedgerService.isStaticGeneralLedgerEntryPresentForYearAndPeriod(2021L, 6L);
//        System.out.println(resultJune);
//        assert resultJune != true;
//
//    }
}
