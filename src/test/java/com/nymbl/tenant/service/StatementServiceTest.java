package com.nymbl.tenant.service;

import com.nymbl.config.dto.*;
import com.nymbl.config.service.TableObjectContainer;
import com.nymbl.config.utils.DateUtil;
import com.nymbl.master.model.User;
import com.nymbl.master.repository.ClearingHousePayerRepository;
import com.nymbl.master.service.ClearingHousePayerService;
import com.nymbl.master.service.UserService;
import com.nymbl.tenant.model.*;
import com.nymbl.tenant.repository.ClaimRepository;
import com.nymbl.tenant.repository.ClaimSubmissionRepository;
import com.nymbl.tenant.repository.InsuranceVerificationRepository;
import com.nymbl.tenant.repository.PatientStatementRepository;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.Query;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.*;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.*;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.mockito.MockitoAnnotations.openMocks;

@SuppressWarnings("unchecked")
class StatementServiceTest {

    @Mock
    private PaymentService mockPaymentService;
    @Mock
    private InsuranceVerificationRepository mockInsuranceVerificationRepository;
    @Spy
    @InjectMocks
    private ClaimService mockClaimService;
    @Mock
    private ClaimRepository mockClaimRepository;
    @Spy
    @InjectMocks
    private ClearingHousePayerService mockClearingHousePayerService;
    @Mock
    private ClearingHousePayerRepository mockClearingHousePayerRepository;
    @Mock
    private NoteService mockNoteService;
    @Mock
    private PatientStatementService mockPatientStatementService;
    @Mock
    private PatientStatementRepository mockPatientStatementRepository;
    @Mock
    private CriticalMessageService mockCriticalMessageService;
    @Mock
    private AppliedPaymentService mockAppliedPaymentService;
    @Mock
    private InsuranceVerificationLCodeService mockInsuranceVerificationLCodeService;
    @Mock
    private AdjustmentService mockAdjustmentService;
    @Mock
    private PhoneNumberService mockPhoneNumberService;
    @Mock
    private L_CodeService mockLCodeService;
    @Mock
    private SystemSettingService mockSystemSettingService;
    @Mock
    private UserService mockUserService;
    @Mock
    private ClaimSubmissionRepository mockClaimSubmissionRepository;
    @Mock
    @PersistenceContext(unitName = "tenant")
    private EntityManager mockEntityManager;

    private StatementService statementServiceUnderTest;

    @BeforeEach
    void setUp() {
        openMocks(this);

        mockClearingHousePayerService = new ClearingHousePayerService(mockClearingHousePayerRepository, mockSystemSettingService);

        mockClaimService = new ClaimService(mockClaimRepository,
                mockUserService,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                mockClearingHousePayerService,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null);

        Contact mockContact = Fake.getContact();
        PatientContact pc = new PatientContact();
        pc.setContact(mockContact);
        Patient mockPatient = Fake.getPatient();
        mockPatient.setId(1L);
        mockPatient.setPatientGuarantor(pc);
        Prescription mockRx = Fake.getPrescription();
        mockRx.setId(1L);
        mockRx.setPatient(mockPatient);
        mockRx.setPatientId(mockPatient.getId());
        Claim mockClaim = Fake.getClaim();
        mockClaim.setBillingBranch(mockPatient.getPrimaryBranch());
        mockClaim.setBillingBranchId(mockPatient.getPrimaryBranchId());
        mockClaim.setPrescription(mockRx);
        mockClaim.setPrescriptionId(mockRx.getId());
        mockClaim.setDateOfService(java.sql.Date.valueOf("2020-08-02"));
        List<Claim> mockClaimList = new ArrayList<>();
        mockClaimList.add(mockClaim);
        Query mockQuery = Mockito.mock(Query.class);
        Mockito.when(mockQuery.getResultList()).thenReturn(mockClaimList);
        Mockito.when(mockEntityManager.createNativeQuery(Mockito.anyString(), ArgumentMatchers.<Class<Claim>>any())).thenReturn(mockQuery);
        statementServiceUnderTest = new StatementService(mockPaymentService, mockInsuranceVerificationRepository, mockClaimService, mockNoteService, mockPatientStatementService, mockCriticalMessageService, mockAppliedPaymentService, mockInsuranceVerificationLCodeService, mockPhoneNumberService, mockAdjustmentService, mockLCodeService, mockUserService, mockClaimSubmissionRepository, mockSystemSettingService);
        TableObjectContainer.getTableMap().clear();
    }

    @Test
    void testBuildAllClaimsByPatientStatementSentDateOrNotSentSQL() {
        // Setup
        TimeZone currentTz = TimeZone.getDefault();
        TimeZone.setDefault(TimeZone.getTimeZone("EST"));
        // Run the test
        final String result = statementServiceUnderTest.buildAllClaimsByPatientStatementSentDateOrNotSentSQL(0L, null, new GregorianCalendar(2019, Calendar.JANUARY, 1).getTime(), new GregorianCalendar(2019, Calendar.JANUARY, 1).getTime(), null, null, "all", null, null, null, "sortField", "direction", 0, 0, true);
        TimeZone.setDefault(currentTz);

        // Verify the results
        assertEquals("SELECT c.* FROM claim c INNER JOIN prescription rx ON c.prescription_id = rx.id INNER JOIN patient p ON rx.patient_id = p.id INNER JOIN patient_insurance pin ON c.patient_insurance_id = pin.id INNER JOIN insurance_company ic ON pin.insurance_company_id = ic.id INNER JOIN device_type dt ON rx.device_type_id = dt.id LEFT JOIN claim_submission cs ON c.id = cs.claim_id LEFT JOIN patient_statement ps ON c.id = ps.claim_id JOIN branch b ON p.primary_branch_id = b.id WHERE rx.active = 1 AND c.total_pt_responsibility_balance > 0 AND ((ps.id = (SELECT MAX(ps2.id) FROM patient_statement ps2 WHERE ps2.claim_id = c.id)) OR (ps.id is NULL))  AND (cs.id IS NULL OR cs.id in (SELECT MAX(cs2.id) FROM claim_submission cs2 WHERE cs2.claim_id = c.id)) AND (ps.sent_date between 'Tue Jan 01 00:00:00 EST 2019' and 'Tue Jan 01 00:00:00 EST 2019' OR (ps.id IS NULL) )  ORDER BY sortField direction LIMIT 0,0", result);
    }

    @Test
    void testgetListOfAllStatementDTOs() {
        TimeZone currentTz = TimeZone.getDefault();

        ClaimSubmission claimSubmission = Fake.getClaimSubmission();
        claimSubmission.setSubmissionDate(new java.sql.Date(System.currentTimeMillis()));
        PatientInsurance patientInsurance = new PatientInsurance();
        InsuranceCompany insuranceCompany = new InsuranceCompany();
        insuranceCompany.setName("IC");
        patientInsurance.setInsuranceCompany(insuranceCompany);
        claimSubmission.setPatientInsurance(patientInsurance);
        Claim claim = Fake.getClaim();
        claim.setDateOfService(java.sql.Date.valueOf("2020-08-02"));
        claim.setId(Fake.ID);
        claim.setPtBalancePushedDate(java.sql.Date.valueOf("2020-08-02"));
        Prescription prescription = new Prescription();
        prescription.setId(Fake.ID);
        claim.setPrescription(prescription);
        claim.setPrescriptionId(Fake.ID);
        User practitioner = new User();
        practitioner.setId(Fake.ID);
        prescription.setTreatingPractitioner(practitioner);
        Branch branch = Fake.getBranch();
        Patient patient = Fake.getPatient();
        patient.setId(Fake.ID);
        patient.setPrimaryBranchId(Fake.ID);
        patient.setPrimaryBranch(branch);
        patient.setPrimaryBranchId(branch.getId());
        prescription.setPatient(patient);
        prescription.setBranch(branch);
        claim.setBillingBranch(branch);
        DeviceType deviceType = Fake.getDeviceType();
        prescription.setDeviceType(deviceType);
        Contact contact = Fake.getContact();
        contact.setId(Fake.ID);
        PatientContact patientGuarantor = new PatientContact();
        patientGuarantor.setContact(contact);
        patient.setPatientGuarantor(patientGuarantor);
        claimSubmission.setId(Fake.ID);
        claimSubmission.setClaimId(Fake.ID);
        claimSubmission.setClaim(claim);
        final List<ClaimSubmission> claimSubmissions = Arrays.asList(claimSubmission);
        Note note = new Note();
        note.setId(Fake.ID);
        PatientStatement patientStatement = Fake.getPatientStatement();
        patientStatement.setId(Fake.ID);
        patientStatement.setClaimId(Fake.ID);
        Payment payment = new Payment();
        payment.setId(Fake.ID);
        payment.setClaimId(Fake.ID);
        payment.setPayerType("cache");
        AppliedPayment appliedPayment = new AppliedPayment();
        appliedPayment.setId(Fake.ID);
        appliedPayment.setClaimId(Fake.ID);
        appliedPayment.setPayment(payment);
        InsuranceVerification insuranceVerification = new InsuranceVerification();
        insuranceVerification.setId(Fake.ID);
        insuranceVerification.setPatientInsuranceId(Fake.ID);
        insuranceVerification.setPrescriptionId(Fake.ID);
        InsuranceVerification_L_Code insuranceVerification_l_code = new InsuranceVerification_L_Code();
        Prescription_L_Code prescription_l_code = new Prescription_L_Code();
        L_Code lCode = new L_Code();
        lCode.setName("A000");
        lCode.setFriendlyDescription("Happy");
        prescription_l_code.setLCode(lCode);
        prescription_l_code.setQuantity(Long.valueOf(1L));
        insuranceVerification_l_code.setPrescriptionLCode(prescription_l_code);
        when(mockNoteService.getByPrescriptionIdAndNoteType(anyLong(), anyString())).thenReturn(Arrays.asList(note));
        when(mockPatientStatementRepository.findTop1ByClaimIdOrderByIdDesc(anyLong())).thenReturn(patientStatement);
        when(mockAppliedPaymentService.findByClaimId(anyLong())).thenReturn(Arrays.asList(appliedPayment));
        when(mockPaymentService.findByClaimId(anyLong())).thenReturn(Arrays.asList(payment));
        when(mockInsuranceVerificationRepository.findByPatientInsuranceIdAndPrescriptionId(anyLong(), anyLong())).thenReturn(insuranceVerification);
        when(mockInsuranceVerificationLCodeService.getInsuranceVerificationLCodes(any(Claim.class), any(InsuranceVerification.class))).thenReturn(Arrays.asList(insuranceVerification_l_code));
        when(mockClaimSubmissionRepository.findClaimSubmissionByClaimByLastAndSubmissionDate(anyLong())).thenReturn(claimSubmission);
        when(mockPatientStatementService.findTop1ByClaimIdOrderByIdDesc(anyLong())).thenReturn(patientStatement);

        SystemSetting systemSetting2 = new SystemSetting();
        systemSetting2.setId(Fake.ID);
        systemSetting2.setSection("billing");
        systemSetting2.setField("include_patient_statement_sales_tax");
        systemSetting2.setValue("Y");
        when(mockSystemSettingService.findBySectionAndField("billing", "include_patient_statement_sales_tax")).thenReturn(systemSetting2);

        SystemSetting systemSetting3 = new SystemSetting();
        systemSetting3.setId(Fake.ID);
        systemSetting3.setSection("general");
        systemSetting3.setField("task_names_must_be_templates");
        systemSetting3.setValue("Y");
        when(mockSystemSettingService.findBySectionAndField("general", "task_names_must_be_templates")).thenReturn(systemSetting3);

        SystemSetting systemSetting4 = new SystemSetting();
        systemSetting4.setId(Fake.ID);
        systemSetting4.setSection("claim");
        systemSetting4.setField("use_single_claim");
        systemSetting4.setValue("N");
        when(mockSystemSettingService.findBySectionAndField("claim", "use_single_claim")).thenReturn(systemSetting3);

        // Run the test
        List<Claim> claimList = new ArrayList<>();
        claimList.add(claim);


        StatementDTO result = statementServiceUnderTest.getListOfAllStatementDTOs(claimList).get(0);

        // Verify the results
        assertEquals(Fake.ID, result.getClaimId());
        assertEquals(Fake.ID, result.getPatientId());
        assertEquals(Fake.ID, result.getPrescriptionId());
        assertEquals("firstName middleName lastName", result.getGuarantorName());
        assertEquals("streetAddress", result.getGuarantorNumberStreet());
        assertEquals("city, state 11111", result.getGuarantorCityStateZip());
        assertEquals("Neu Limbs, LLC.", result.getCompanyName());
        assertEquals("5282 Medical Drive, Suite 105", result.getCompanyNumberStreet());
        assertEquals("San Antonio, TX 78229", result.getCompanyCityStateZip());
        assertEquals("**********", result.getCompanyPhone());
        assertEquals("2020-08-02", result.getVisitDate());
        assertEquals(new SimpleDateFormat("yyyy-MM-dd").format(DateUtil.getCurrentDate()), result.getLastSentDate());
        assertEquals("Patricia A Benner", result.getPatientName());
        assertEquals("cache", result.getLastPayer());
        assertEquals(BigDecimal.ZERO, result.getTotalCharges());
        assertEquals(BigDecimal.ZERO, result.getTotalInsurancePaid());
        assertEquals(BigDecimal.ZERO, result.getTotalGuarantorPaid());
        assertEquals(BigDecimal.ZERO, result.getTotalAdjustments());
        assertEquals(BigDecimal.ZERO, result.getGuarantorBalanceOwed());
        assertEquals(BigDecimal.TEN, result.getInsuranceBalanceOwed());

        List<Claim> pagedClaims = new ArrayList<>();
        pagedClaims.add(claim);
        String query = "SELECT c.* FROM claim c INNER JOIN prescription rx ON c.prescription_id = rx.id INNER JOIN patient p ON rx.patient_id = p.id INNER JOIN patient_insurance pin ON c.patient_insurance_id = pin.id INNER JOIN insurance_company ic ON pin.insurance_company_id = ic.id INNER JOIN device_type dt ON rx.device_type_id = dt.id LEFT JOIN claim_submission cs ON c.id = cs.claim_id LEFT JOIN patient_statement ps ON c.id = ps.claim_id JOIN branch b ON p.primary_branch_id = b.id WHERE rx.active = 1 AND c.total_pt_responsibility_balance > 0 AND ((ps.id = (SELECT MAX(ps2.id) FROM patient_statement ps2 WHERE ps2.claim_id = c.id)) OR (ps.id is NULL))  AND (cs.id IS NULL OR cs.id in (SELECT MAX(cs2.id) FROM claim_submission cs2 WHERE cs2.claim_id = c.id)) AND ((0 = 0) OR (0 IS null) OR (p.id = 0)) AND ((null = 0) OR (null IS null) OR (p.primary_branch_id = null)) AND (ps.sent_date between 'Tue Jan 01 00:00:00 EST 2019' and 'Tue Jan 01 00:00:00 EST 2019' OR (ps.id IS NULL) )  ORDER BY cs.submission_date ASC LIMIT 0,10";
        Query mockedQuery = mock(Query.class);
        when(mockedQuery.getResultList()).thenReturn(pagedClaims);
        when(mockedQuery.setParameter(anyString(), any())).thenReturn(mockedQuery);
        when(mockEntityManager.createNativeQuery(query, Claim.class)).thenReturn(mockedQuery);

        String query2 = "SELECT c.* FROM claim c INNER JOIN prescription rx ON c.prescription_id = rx.id INNER JOIN patient p ON rx.patient_id = p.id INNER JOIN patient_insurance pin ON c.patient_insurance_id = pin.id INNER JOIN insurance_company ic ON pin.insurance_company_id = ic.id INNER JOIN device_type dt ON rx.device_type_id = dt.id LEFT JOIN claim_submission cs ON c.id = cs.claim_id LEFT JOIN patient_statement ps ON c.id = ps.claim_id JOIN branch b ON p.primary_branch_id = b.id WHERE rx.active = 1 AND c.total_pt_responsibility_balance > 0 AND ((ps.id = (SELECT MAX(ps2.id) FROM patient_statement ps2 WHERE ps2.claim_id = c.id)) OR (ps.id is NULL))  AND (cs.id IS NULL OR cs.id in (SELECT MAX(cs2.id) FROM claim_submission cs2 WHERE cs2.claim_id = c.id)) AND ((0 = 0) OR (0 IS null) OR (p.id = 0)) AND ((null = 0) OR (null IS null) OR (p.primary_branch_id = null)) AND (ps.sent_date between 'Tue Jan 01 00:00:00 EST 2019' and 'Tue Jan 01 00:00:00 EST 2019' OR (ps.id IS NULL) ) ";
        Query mockedQuery2 = mock(Query.class);
        when(mockedQuery2.getResultList()).thenReturn(pagedClaims);
        when(mockedQuery2.setParameter(anyString(), any())).thenReturn(mockedQuery2);
        when(mockEntityManager.createNativeQuery(query2, Claim.class)).thenReturn(mockedQuery2);

        final Page<StatementDTO> result2 = statementServiceUnderTest.statements(0L, null, new GregorianCalendar(2019, Calendar.JANUARY, 1).getTime(), new GregorianCalendar(2019, Calendar.JANUARY, 1).getTime(), null, null, "all", null, null, null, PageRequest.of(1, 10, Sort.by(new Sort.Order(Sort.Direction.ASC, "cs.submission_date").ignoreCase())), mockEntityManager);
        result = result2.getContent().get(0);
        assertEquals(1, result2.getTotalElements());
        assertEquals(Fake.ID, result.getClaimId());
        assertEquals(Fake.ID, result.getPatientId());
        assertEquals(Fake.ID, result.getPrescriptionId());
        assertEquals("firstName middleName lastName", result.getGuarantorName());
        assertEquals("streetAddress", result.getGuarantorNumberStreet());
        assertEquals("city, state 11111", result.getGuarantorCityStateZip());
        assertEquals("Neu Limbs, LLC.", result.getCompanyName());
        assertEquals("5282 Medical Drive, Suite 105", result.getCompanyNumberStreet());
        assertEquals("San Antonio, TX 78229", result.getCompanyCityStateZip());
        assertEquals("**********", result.getCompanyPhone());
        assertEquals("2020-08-02", result.getVisitDate());
        assertEquals(new SimpleDateFormat("yyyy-MM-dd").format(DateUtil.getCurrentDate()), result.getLastSentDate());
        assertEquals("Patricia A Benner", result.getPatientName());
        assertEquals("cache", result.getLastPayer());
        assertEquals(BigDecimal.ZERO, result.getTotalCharges());
        assertEquals(BigDecimal.ZERO, result.getTotalInsurancePaid());
        assertEquals(BigDecimal.ZERO, result.getTotalGuarantorPaid());
        assertEquals(BigDecimal.ZERO, result.getTotalAdjustments());
        assertEquals(BigDecimal.ZERO, result.getGuarantorBalanceOwed());
        assertEquals(BigDecimal.TEN, result.getInsuranceBalanceOwed());

        TimeZone.setDefault(currentTz);
    }

    @Test
    void testWriteStatementToXMLFile() {
        StatementDTO statementDTO = new StatementDTO();
        statementDTO.setNextComment("<tr>Next Comment</tr>");
        statementDTO.setStatementComment("<td>Statement Comment</td>");
        statementDTO.setNextAttempt("sent_to_collections");
        statementDTO.setClaimId(Fake.ID);
        final List<StatementDTO> statementList = Arrays.asList(statementDTO);
        final String expectedResult = "<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"yes\"?>\n" +
                "<nymbl>\n" +
                "    <statement>\n" +
                "        <claimId>1</claimId>\n" +
                "        <companyName>Demo</companyName>\n" +
                "        <statementComment>Statement Comment</statementComment>\n" +
                "        <nextComment>Next Comment</nextComment>\n" +
                "        <nextAttempt>sent_to_collections</nextAttempt>\n" +
                "    </statement>\n" +
                "</nymbl>\n";
        Claim claim = Fake.getClaim();
        claim.setTotalPtResponsibilityBalance(BigDecimal.TEN);
        claim.setId(Fake.ID);

        Adjustment adjustment = new Adjustment();
        adjustment.setId(1L);
        adjustment.setName("Collections");
        adjustment.setOperation("-");
        adjustment.setSelected(true);
        adjustment.setWithdraw(false);
        List<L_CodeDTO> l_CodeDTOList = getL_codeDTOS();
        Map<String, Object> lcMap = new HashMap<>();
        lcMap.put("lCodes", l_CodeDTOList);
        when(mockLCodeService.loadLCodeLists(any(), any(), any(), any(), any())).thenReturn(lcMap);
        when(mockAdjustmentService.findById(any())).thenReturn(adjustment);
        when(mockAdjustmentService.findByName(any())).thenReturn(adjustment);
        when(mockClearingHousePayerRepository.findById(anyLong())).thenReturn(Optional.of(claim.getResponsiblePatientInsurance().getInsuranceCompany().getClearingHousePayer()));
        when(mockClaimRepository.findById(anyLong())).thenReturn(Optional.of(claim));
        spy(mockClaimService).save(claim);
        when(mockPaymentService.save(any(Payment.class))).thenCallRealMethod();
        when(mockCriticalMessageService.save(any(CriticalMessage.class))).thenCallRealMethod();
        when(mockPatientStatementService.save(any(PatientStatement.class))).thenCallRealMethod();
        Map<String, Object> tM = TableObjectContainer.getTableMap();
        final String result = statementServiceUnderTest.writeStatementToXMLFile(statementList);

        assertTrue(((CriticalMessage) TableObjectContainer.getObjectFromList(CriticalMessage.class.getName(), 0)).getMessage().contains("Claim #1 on "));
        assertTrue(((CriticalMessage) TableObjectContainer.getObjectFromList(CriticalMessage.class.getName(), 0)).getMessage().contains(" in the amount of $10 has been sent to collections."));
        assertEquals("patient_adjustment", ((Payment) TableObjectContainer.getObjectFromList(Payment.class.getName(), 0)).getPaymentType());
        assertEquals("Sending patient responsibility balance to collections.  Remaining patient responsibility balance is reduced to zero.", ((Payment) TableObjectContainer.getObjectFromList(Payment.class.getName(), 0)).getDescription());
        assertEquals(BigDecimal.ZERO, ((Payment) TableObjectContainer.getObjectFromList(Payment.class.getName(), 0)).getAmount());
        assertEquals("0.00", ((Payment) TableObjectContainer.getObjectFromList(Payment.class.getName(), 0)).getUnappliedAmount().toString());
        assertEquals(BigDecimal.TEN, ((Payment) TableObjectContainer.getObjectFromList(Payment.class.getName(), 0)).getAdjustment());


        assertEquals("sent_to_collections", ((PatientStatement) TableObjectContainer.getObjectFromList(PatientStatement.class.getName(), 0)).getStatus());
        assertEquals("Next Comment", ((PatientStatement) TableObjectContainer.getObjectFromList(PatientStatement.class.getName(), 0)).getComment());
        assertEquals(expectedResult, result);
    }

    @Test
    void testUpdatePatientExportDates() throws Exception {
        // Setup
        Timestamp currentTime = new Timestamp(Calendar.getInstance().getTimeInMillis());
        java.sql.Date currentDate = new java.sql.Date(currentTime.getTime());

        Prescription prescription = Fake.getPrescription();
        PatientInsurance patientInsurance = Fake.getPatientInsurance();
        Branch branch = Fake.getBranch();

        Claim claim0 = new Claim();
        claim0.setId(Long.parseLong("471"));
        claim0.setUncollected(new BigDecimal("0.00"));
        claim0.setPrescription(prescription);
        claim0.setPrescriptionId(prescription.getId());
        claim0.setPatientInsuranceId(patientInsurance.getId());
        claim0.setPatientInsurance(patientInsurance);
//        claim0.setStatus("post_to_pt_responsibility");
        claim0.setNymblStatusId(Fake.POST_TO_PT_RESPONSIBILITY_ID);
        claim0.setTotalClaimBalance(new BigDecimal("0.00"));
        claim0.setTotalPtResponsibilityBalance(new BigDecimal("80.22"));
        claim0.setTotalClaimPaid(new BigDecimal("0.00"));
        claim0.setTotalPtResponsibilityAmount(new BigDecimal("120.22"));
        claim0.setTotalPtResponsibilityPaid(new BigDecimal("40.00"));
        claim0.setTotalClaimAmount(new BigDecimal("174.00"));
        claim0.setDateOfService(new java.sql.Date(Long.parseLong("1584676800000")));
        claim0.setBillingBranchId(branch.getId());
        claim0.setBillingBranch(branch);
        claim0.setAcceptAssignment(true);
        claim0.setUpdatedAt(new java.sql.Date(Long.parseLong("1585713600000")));
        claim0.setUpdatedById(Long.parseLong("1"));
        claim0.setUpdatedBy(Fake.getUserFakeID());
        claim0.setUser(Fake.getUserFakeID());
        claim0.setUserId(Fake.ID);
        claim0.setCreatedById(Fake.ID);
        claim0.setCreatedBy(Fake.getUserFakeID());
        claim0.setPatientExportDate(currentDate);

        StatementDTO statementDTO0 = new StatementDTO();
        statementDTO0.setClaimId(claim0.getId());
        statementDTO0.setPrescriptionId(claim0.getPrescription().getId());
        statementDTO0.setPatientId(Fake.ID);
        statementDTO0.setVisitDate("2020-03-20");
        statementDTO0.setTotalInsurancePaid(new BigDecimal("0"));
        statementDTO0.setSubmittedDate("2020-03-24");
        statementDTO0.setLastSentDate("2020-04-24");
        statementDTO0.setNextAttempt("sent_to_collections");
        StatementLCodeWrapper serviceLines0 = new StatementLCodeWrapper();
        List<StatementLCodeDTO> serviceLine0 = new ArrayList<>();
        StatementLCodeDTO statementLCodeDTO0 = new StatementLCodeDTO();
        statementLCodeDTO0.setTotalCharge(new BigDecimal("87"));
        statementLCodeDTO0.setQuantity(Long.parseLong("1"));
        statementLCodeDTO0.setBillingFee(new BigDecimal("87"));
        statementLCodeDTO0.setLcodeDescription("GC STOCKING THIGHLNGTH 18-30");
        statementLCodeDTO0.setLcode("A6533");
        serviceLine0.add(statementLCodeDTO0);
        StatementLCodeDTO statementLCodeDTO1 = new StatementLCodeDTO();
        statementLCodeDTO1.setTotalCharge(new BigDecimal("87"));
        statementLCodeDTO1.setQuantity(Long.parseLong("1"));
        statementLCodeDTO1.setBillingFee(new BigDecimal("87"));
        statementLCodeDTO1.setLcodeDescription("GC STOCKING THIGHLNGTH 18-30");
        statementLCodeDTO1.setLcode("A6533");
        serviceLine0.add(statementLCodeDTO1);
        serviceLines0.setServiceLine(serviceLine0);
        statementDTO0.setServiceLines(serviceLines0);
        statementDTO0.setGuarantorName("Joshua Howard");
        statementDTO0.setGuarantorNumberStreet("6414 JIMAE CT");
        statementDTO0.setGuarantorCityStateZip("INDEPENDENCE, KY 41051");
        statementDTO0.setCompanyName("Durretts Orthotics and Prosthetics");
        statementDTO0.setCompanyNumberStreet("20 Medical Village Dr. Suite 100");
        statementDTO0.setCompanyCityStateZip("Edgewood, KY 41017");
        statementDTO0.setCompanyPhone("**********");
        statementDTO0.setInsuranceName("ANTHEM FEDERAL (R)");
        statementDTO0.setVisitLocation("20 Medical Village Dr. Suite 100, Edgewood, KY 41017");
        statementDTO0.setVisitDescription("COMPRESSION GARMENTS");
        statementDTO0.setTotalCharges(new BigDecimal("294.22"));
        statementDTO0.setTotalGuarantorPaid(new BigDecimal("40"));
        statementDTO0.setGuarantorBalanceOwed(new BigDecimal("80.22"));
        statementDTO0.setInsuranceBalanceOwed(new BigDecimal("0"));
        statementDTO0.setStatementDate("2020-04-24");
        statementDTO0.setAgeInDays(Long.parseLong("31"));
        statementDTO0.setLastPaymentDate("2023-03-23");
        statementDTO0.setLastPayer("Insurance");
        statementDTO0.setLastPaymentAmount(new BigDecimal("0"));
        statementDTO0.setPatientName("Joshua Howard");
        statementDTO0.setTotalAdjustments(new BigDecimal("0"));
        statementDTO0.setProviderName("");
        statementDTO0.setUserId(Long.parseLong("1"));


        List<L_CodeDTO> l_CodeDTOList = new ArrayList<>();
        L_CodeDTO l_CodeDTO0 = new L_CodeDTO();
        l_CodeDTO0.setName("A6533");
        l_CodeDTO0.setId(Long.parseLong("7825"));
        l_CodeDTO0.setPrescriptionId(Long.parseLong("564"));
        l_CodeDTO0.setAdjustment(new BigDecimal("0"));
        l_CodeDTO0.setTotalCharge(new BigDecimal("87.00"));
        l_CodeDTO0.setQuantity(Long.parseLong("1"));
        l_CodeDTO0.setPayment(new BigDecimal("0"));
        l_CodeDTO0.setTotalAllowable(new BigDecimal("60.11"));
        l_CodeDTO0.setCovered(false);
        l_CodeDTO0.setBill(false);
        l_CodeDTO0.setPrescriptionLCodeId(Long.parseLong("1333"));
        l_CodeDTO0.setBalance(new BigDecimal("40.11"));
        l_CodeDTO0.setEndingBalance(new BigDecimal("40.11"));
        l_CodeDTOList.add(l_CodeDTO0);
        L_CodeDTO l_CodeDTO1 = new L_CodeDTO();
        l_CodeDTO1.setName("A6533");
        l_CodeDTO1.setId(Long.parseLong("7825"));
        l_CodeDTO1.setPrescriptionId(Long.parseLong("564"));
        l_CodeDTO1.setAdjustment(new BigDecimal("0"));
        l_CodeDTO1.setTotalCharge(new BigDecimal("87.00"));
        l_CodeDTO1.setQuantity(Long.parseLong("1"));
        l_CodeDTO1.setPayment(new BigDecimal("0"));
        l_CodeDTO1.setTotalAllowable(new BigDecimal("60.11"));
        l_CodeDTO1.setCovered(false);
        l_CodeDTO1.setBill(false);
        l_CodeDTO1.setPrescriptionLCodeId(Long.parseLong("1334"));
        l_CodeDTO1.setBalance(new BigDecimal("40.11"));
        l_CodeDTO1.setEndingBalance(new BigDecimal("40.11"));
        l_CodeDTOList.add(l_CodeDTO1);

        final List<StatementDTO> statementDTOs = Arrays.asList(statementDTO0);

        Adjustment adjustment = new Adjustment();
        adjustment.setId(1L);
        adjustment.setName("Collections");
        adjustment.setOperation("-");
        adjustment.setSelected(true);
        adjustment.setWithdraw(false);

        Map<String, Object> lCodeMap = new HashMap<>();
        lCodeMap.put("lCodes", l_CodeDTOList);

        when(mockAdjustmentService.findById(any())).thenReturn(adjustment);
        when(mockAdjustmentService.findByName(any())).thenReturn(adjustment);
        when(mockPaymentService.save(any(Payment.class))).thenCallRealMethod();
        when(mockCriticalMessageService.save(any(CriticalMessage.class))).thenCallRealMethod();
        when(mockPatientStatementService.save(any(PatientStatement.class))).thenCallRealMethod();
        when(mockLCodeService.loadLCodeLists(any(), any(), any(), any(), any())).thenReturn(lCodeMap);
        when(mockClaimRepository.findById(anyLong())).thenReturn(Optional.of(claim0));
        when(mockClaimService.save(claim0)).thenCallRealMethod();
        when(mockAppliedPaymentService.setClaimPayment(any(SetClaimDTO.class), any(Payment.class))).thenReturn(true);
        when(mockAppliedPaymentService.compare(any(), any(), any())).thenCallRealMethod();

        // Run the test
        final List<StatementDTO> result = statementServiceUnderTest.updatePatientExportDates(statementDTOs, false);
        HashMap<String, Object> hm = TableObjectContainer.getTableMap();

        assertEquals(new BigDecimal("80.22"), ((Payment) TableObjectContainer.getObjectFromList(Payment.class.getName(), 0)).getUnappliedAdjustment());
        assertEquals(new BigDecimal("80.22"), ((Payment) TableObjectContainer.getObjectFromList(Payment.class.getName(), 0)).getAdjustment());
        assertEquals(new BigDecimal("0.00"), ((Payment) TableObjectContainer.getObjectFromList(Payment.class.getName(), 0)).getUnappliedAmount());
        assertEquals("patient_adjustment", ((Payment) TableObjectContainer.getObjectFromList(Payment.class.getName(), 0)).getPaymentType());
        assertEquals("patient_adjustment", ((Payment) TableObjectContainer.getObjectFromList(Payment.class.getName(), 0)).getPayerType());
        assertEquals("sent_to_collections", ((PatientStatement) TableObjectContainer.getObjectFromList(PatientStatement.class.getName(), 0)).getStatus());
        SimpleDateFormat formatter2 = new SimpleDateFormat("MM/dd/yyyy");
        assertEquals("Claim #471 on " + formatter2.format(new Date()) + " in the amount of $80.22 has been sent to collections.", ((CriticalMessage) TableObjectContainer.getObjectFromList(CriticalMessage.class.getName(), 0)).getMessage());
        assertEquals("Sending patient responsibility balance to collections.  Remaining patient responsibility balance is reduced to zero.", ((Payment) TableObjectContainer.getObjectFromList(Payment.class.getName(), 0)).getDescription());
    }

    private List<L_CodeDTO> getL_codeDTOS() {
        List<L_CodeDTO> l_CodeDTOList = new ArrayList<>();
        L_CodeDTO l_CodeDTO0 = new L_CodeDTO();
        l_CodeDTO0.setName("A6533");
        l_CodeDTO0.setId(Long.parseLong("7825"));
        l_CodeDTO0.setPrescriptionId(Long.parseLong("564"));
        l_CodeDTO0.setAdjustment(new BigDecimal("0"));
        l_CodeDTO0.setTotalCharge(new BigDecimal("87.00"));
        l_CodeDTO0.setQuantity(Long.parseLong("1"));
        l_CodeDTO0.setPayment(new BigDecimal("0"));
        l_CodeDTO0.setTotalAllowable(new BigDecimal("60.11"));
        l_CodeDTO0.setCovered(false);
        l_CodeDTO0.setBill(false);
        l_CodeDTO0.setPrescriptionLCodeId(Long.parseLong("1333"));
        l_CodeDTO0.setBalance(new BigDecimal("40.11"));
        l_CodeDTO0.setEndingBalance(new BigDecimal("40.11"));
        l_CodeDTOList.add(l_CodeDTO0);
        L_CodeDTO l_CodeDTO1 = new L_CodeDTO();
        l_CodeDTO1.setName("A6533");
        l_CodeDTO1.setId(Long.parseLong("7825"));
        l_CodeDTO1.setPrescriptionId(Long.parseLong("564"));
        l_CodeDTO1.setAdjustment(new BigDecimal("0"));
        l_CodeDTO1.setTotalCharge(new BigDecimal("87.00"));
        l_CodeDTO1.setQuantity(Long.parseLong("1"));
        l_CodeDTO1.setPayment(new BigDecimal("0"));
        l_CodeDTO1.setTotalAllowable(new BigDecimal("60.11"));
        l_CodeDTO1.setCovered(false);
        l_CodeDTO1.setBill(false);
        l_CodeDTO1.setPrescriptionLCodeId(Long.parseLong("1334"));
        l_CodeDTO1.setBalance(new BigDecimal("40.11"));
        l_CodeDTO1.setEndingBalance(new BigDecimal("40.11"));
        l_CodeDTOList.add(l_CodeDTO1);
        return l_CodeDTOList;
    }

    @Test
    void testApplyAdjustments() {
        // Setup
        Claim claim0 = new Claim();
        claim0.setId(Long.parseLong("347"));
//        claim0.setStatus("sent_to_next_payer");
        claim0.setNymblStatusId(Fake.SENT_TO_NEXT_PAYER_ID);
        claim0.setUserId(Long.parseLong("948"));
        claim0.setTotalClaimAmount(new BigDecimal("502.00"));
        claim0.setTotalClaimPaid(new BigDecimal("0.00"));
        claim0.setTotalClaimBalance(new BigDecimal("379.00"));
        claim0.setTotalPtResponsibilityAmount(new BigDecimal("61.50"));
        claim0.setTotalPtResponsibilityPaid(new BigDecimal("0.00"));
        claim0.setTotalPtResponsibilityBalance(new BigDecimal("61.50"));
        claim0.setUncollected(new BigDecimal("0.00"));
        //claim0.setAdditionalComment("additionalComment");

        List<L_CodeDTO> lCodeDTOSList = new ArrayList<>();
        L_CodeDTO l_CodeDTO0 = new L_CodeDTO();
        l_CodeDTO0.setName("A5500");
        l_CodeDTO0.setId(Long.parseLong("4956"));
        l_CodeDTO0.setPrescriptionId(Long.parseLong("272"));
        l_CodeDTO0.setAdjustment(new BigDecimal("0"));
        l_CodeDTO0.setTotalCharge(new BigDecimal("89.00"));
        l_CodeDTO0.setQuantity(Long.parseLong("1"));
        l_CodeDTO0.setPayment(new BigDecimal("0"));
        l_CodeDTO0.setTotalAllowable(new BigDecimal("57.66"));
        l_CodeDTO0.setCovered(false);
        l_CodeDTO0.setBill(false);
        l_CodeDTO0.setPrescriptionLCodeId(Long.parseLong("679"));
        l_CodeDTO0.setBalance(new BigDecimal("89.00"));
        l_CodeDTO0.setEndingBalance(new BigDecimal("89.00"));
        lCodeDTOSList.add(l_CodeDTO0);
        L_CodeDTO l_CodeDTO1 = new L_CodeDTO();
        l_CodeDTO1.setName("A5500");
        l_CodeDTO1.setId(Long.parseLong("4956"));
        l_CodeDTO1.setPrescriptionId(Long.parseLong("272"));
        l_CodeDTO1.setAdjustment(new BigDecimal("0"));
        l_CodeDTO1.setTotalCharge(new BigDecimal("89.00"));
        l_CodeDTO1.setQuantity(Long.parseLong("1"));
        l_CodeDTO1.setPayment(new BigDecimal("0"));
        l_CodeDTO1.setTotalAllowable(new BigDecimal("57.66"));
        l_CodeDTO1.setCovered(false);
        l_CodeDTO0.setBill(false);
        l_CodeDTO1.setPrescriptionLCodeId(Long.parseLong("680"));
        l_CodeDTO1.setBalance(new BigDecimal("89.00"));
        l_CodeDTO1.setEndingBalance(new BigDecimal("89.00"));
        lCodeDTOSList.add(l_CodeDTO1);
        L_CodeDTO l_CodeDTO2 = new L_CodeDTO();
        l_CodeDTO2.setName("A5514");
        l_CodeDTO2.setId(Long.parseLong("8108"));
        l_CodeDTO2.setPrescriptionId(Long.parseLong("272"));
        l_CodeDTO2.setAdjustment(new BigDecimal("0"));
        l_CodeDTO2.setTotalCharge(new BigDecimal("162.00"));
        l_CodeDTO2.setQuantity(Long.parseLong("3"));
        l_CodeDTO2.setPayment(new BigDecimal("0"));
        l_CodeDTO2.setTotalAllowable(new BigDecimal("92.25"));
        l_CodeDTO2.setCovered(false);
        l_CodeDTO2.setBill(false);
        l_CodeDTO2.setPrescriptionLCodeId(Long.parseLong("681"));
        l_CodeDTO2.setBalance(new BigDecimal("131.25"));
        l_CodeDTO2.setEndingBalance(new BigDecimal("131.25"));
        lCodeDTOSList.add(l_CodeDTO2);
        L_CodeDTO l_CodeDTO3 = new L_CodeDTO();
        l_CodeDTO3.setName("A5514");
        l_CodeDTO3.setId(Long.parseLong("8108"));
        l_CodeDTO3.setPrescriptionId(Long.parseLong("272"));
        l_CodeDTO3.setAdjustment(new BigDecimal("0"));
        l_CodeDTO3.setTotalCharge(new BigDecimal("162.00"));
        l_CodeDTO3.setQuantity(Long.parseLong("3"));
        l_CodeDTO3.setPayment(new BigDecimal("0"));
        l_CodeDTO3.setTotalAllowable(new BigDecimal("92.25"));
        l_CodeDTO3.setCovered(false);
        l_CodeDTO3.setBill(false);
        l_CodeDTO3.setPrescriptionLCodeId(Long.parseLong("682"));
        l_CodeDTO3.setBalance(new BigDecimal("131.25"));
        l_CodeDTO3.setEndingBalance(new BigDecimal("131.25"));
        lCodeDTOSList.add(l_CodeDTO3);


        // Run the test
        List<L_CodeDTO> lCodeDTOList = statementServiceUnderTest.applyAdjustments(claim0, lCodeDTOSList);

        // Verify the results
        assertEquals(new BigDecimal("0"), lCodeDTOList.get(0).getEndingBalance());
        assertEquals(new BigDecimal("89.00"), lCodeDTOList.get(0).getAdjustment());
        assertEquals(new BigDecimal("0"), lCodeDTOList.get(1).getEndingBalance());
        assertEquals(new BigDecimal("89.00"), lCodeDTOList.get(1).getAdjustment());

        assertEquals(new BigDecimal("0"), lCodeDTOList.get(2).getEndingBalance());
        assertEquals(new BigDecimal("131.25"), lCodeDTOList.get(2).getAdjustment());
        assertEquals(new BigDecimal("0"), lCodeDTOList.get(3).getEndingBalance());
        assertEquals(new BigDecimal("131.25"), lCodeDTOList.get(3).getAdjustment());
    }

    @Test
    void testApplyAdjustments2() {
        Timestamp currentTime = new Timestamp(Calendar.getInstance().getTimeInMillis());
        java.sql.Date currentDate = new java.sql.Date(currentTime.getTime());

        Prescription prescription = Fake.getPrescription();
        PatientInsurance patientInsurance = Fake.getPatientInsurance();
        Branch branch = Fake.getBranch();
        Claim claim0 = new Claim();
        claim0.setId(Long.parseLong("471"));
        claim0.setUncollected(new BigDecimal("0.00"));
        claim0.setPrescription(prescription);
        claim0.setPrescriptionId(prescription.getId());
        claim0.setPatientInsuranceId(patientInsurance.getId());
        claim0.setPatientInsurance(patientInsurance);
//        claim0.setStatus("post_to_pt_responsibility");
        claim0.setNymblStatusId(Fake.POST_TO_PT_RESPONSIBILITY_ID);
        claim0.setTotalClaimBalance(new BigDecimal("0.00"));
        claim0.setTotalPtResponsibilityBalance(new BigDecimal("80.22"));
        claim0.setTotalClaimPaid(new BigDecimal("0.00"));
        claim0.setTotalPtResponsibilityAmount(new BigDecimal("120.22"));
        claim0.setTotalPtResponsibilityPaid(new BigDecimal("40.00"));
        claim0.setTotalClaimAmount(new BigDecimal("174.00"));
        claim0.setDateOfService(new java.sql.Date(Long.parseLong("1584676800000")));
        claim0.setBillingBranchId(branch.getId());
        claim0.setBillingBranch(branch);
        claim0.setAcceptAssignment(true);
        claim0.setUpdatedAt(new java.sql.Date(Long.parseLong("1585713600000")));
        claim0.setUpdatedById(Long.parseLong("1"));
        claim0.setUpdatedBy(Fake.getUserFakeID());
        claim0.setUser(Fake.getUserFakeID());
        claim0.setUserId(Fake.ID);
        claim0.setCreatedById(Fake.ID);
        claim0.setCreatedBy(Fake.getUserFakeID());
        claim0.setPatientExportDate(currentDate);

        List<L_CodeDTO> l_CodeDTOList = getL_codeDTOS();

        List<L_CodeDTO> lCodeDTOList = statementServiceUnderTest.applyAdjustments(claim0, l_CodeDTOList);

        assertEquals(new BigDecimal("40.11"), lCodeDTOList.get(0).getAdjustment());
        assertEquals(new BigDecimal("0"), lCodeDTOList.get(0).getEndingBalance());
        assertEquals(new BigDecimal("40.11"), lCodeDTOList.get(1).getAdjustment());
        assertEquals(new BigDecimal("0"), lCodeDTOList.get(1).getEndingBalance());
    }


    @Test
    void testApplyAdjustmentsBelowADollar() {
        Timestamp currentTime = new Timestamp(Calendar.getInstance().getTimeInMillis());
        java.sql.Date currentDate = new java.sql.Date(currentTime.getTime());

        Prescription prescription = Fake.getPrescription();
        PatientInsurance patientInsurance = Fake.getPatientInsurance();
        Branch branch = Fake.getBranch();
        Claim claim0 = new Claim();
        claim0.setId(Long.parseLong("471"));
        claim0.setUpdatedBy(Fake.getUserFakeID());
        claim0.setUser(Fake.getUserFakeID());
        claim0.setUserId(Fake.ID);
        claim0.setCreatedById(Fake.ID);
        claim0.setCreatedBy(Fake.getUserFakeID());
//        claim0.setStatus("post_to_pt_responsibility");
        claim0.setNymblStatusId(Fake.POST_TO_PT_RESPONSIBILITY_ID);
        claim0.setBillingBranchId(Long.parseLong("1"));
        claim0.setBillingBranch(branch);
        claim0.setAcceptAssignment(true);
        claim0.setCreatedAt(new java.sql.Date(Long.parseLong("1582606800000")));
        claim0.setPatientInsurance(patientInsurance);
        claim0.setTotalClaimBalance(new BigDecimal("-0.99"));
        claim0.setTotalPtResponsibilityBalance(new BigDecimal("0.99"));
        claim0.setTotalClaimPaid(new BigDecimal("23.65"));
        claim0.setTotalPtResponsibilityAmount(new BigDecimal("0.99"));
        claim0.setTotalPtResponsibilityPaid(new BigDecimal("0.00"));
        claim0.setTotalClaimAmount(new BigDecimal("84.00"));
        claim0.setDateResolved(new java.sql.Date(Long.parseLong("1584504000000")));
        claim0.setPrescriptionId(Long.parseLong("361"));
        claim0.setPatientInsuranceId(Long.parseLong("593"));
        claim0.setPrescription(prescription);
        claim0.setDateOfService(new java.sql.Date(Long.parseLong("1582606800000")));
        claim0.setUpdatedAt(new java.sql.Date(Long.parseLong("1587700800000")));
        claim0.setCreatedById(Long.parseLong("948"));
        claim0.setUpdatedById(Long.parseLong("1"));
        claim0.setPatientExportDate(new java.sql.Date(Long.parseLong("1588046400000")));
        claim0.setUncollected(new BigDecimal("0.00"));

        List<L_CodeDTO> l_CodeDTOList = new ArrayList<>();
        L_CodeDTO l_CodeDTO0 = new L_CodeDTO();
        l_CodeDTO0.setName("L4205");
        l_CodeDTO0.setId(Long.parseLong("4946"));
        l_CodeDTO0.setQuantity(Long.parseLong("2"));
        l_CodeDTO0.setPrescriptionId(Long.parseLong("361"));
        l_CodeDTO0.setAdjustment(new BigDecimal("0.00"));
        l_CodeDTO0.setTotalCharge(new BigDecimal("84.00"));
        l_CodeDTO0.setPayment(new BigDecimal("0.00"));
        l_CodeDTO0.setTotalAllowable(new BigDecimal("24.64"));
        l_CodeDTO0.setCovered(true);
        l_CodeDTO0.setBill(true);
        l_CodeDTO0.setPrescriptionLCodeId(Long.parseLong("846"));
        l_CodeDTO0.setBalance(new BigDecimal("0.00"));
        l_CodeDTO0.setEndingBalance(new BigDecimal("0.00"));
        l_CodeDTOList.add(l_CodeDTO0);

        List<L_CodeDTO> lCodeDTOList = statementServiceUnderTest.applyAdjustments(claim0, l_CodeDTOList);

        assertEquals(new BigDecimal("0.00"), lCodeDTOList.get(0).getAdjustment());
        assertEquals(new BigDecimal("0"), lCodeDTOList.get(0).getEndingBalance());
    }

    @Test
    public void testPopulateClaimIdList() {
        List<StatementDTO> statementDTOList = Fake.getStatementDTOList();
        List<Long> claimIdList = statementServiceUnderTest.populateClaimIdList(statementDTOList);
        assertEquals(1, claimIdList.size());
        assertEquals("318", claimIdList.get(0).toString());
    }


    @Test
    public void testGetLastestPatientStatement() {
        List<PatientStatement> patientStatementList = statementServiceUnderTest.getLastestPatientStatement(Fake.getPatientStatementList());
        assertEquals(1, patientStatementList.size());
        assertEquals("54", patientStatementList.get(0).getId().toString());
    }

    @Test
    public void testBuildExportStatementSentToCollectionsCSV() {
        List<PatientStatement> patientStatementList = new ArrayList<>();

        String date = DateUtil.getCurrentDateDB().toString();
        PatientContact patientGuarantor = new PatientContact();
        patientGuarantor.setContact(Fake.getContact());
        PatientStatement patientStatement = Fake.getPatientStatement();
        patientStatement.getClaim().getPrescription().getPatient().setPatientGuarantor(patientGuarantor);
        patientStatementList.add(patientStatement);
        StringBuilder result = statementServiceUnderTest.buildExportStatementSentToCollectionsCSV(patientStatementList);
        assertTrue(result.toString().contains("Sent Date,Patient Id,Patient Last Name,Patient First Name,Patient Middle Initial,Patient Address 1 Line 1,Patient Address 1 Line 2,Patient Address 1 City,Patient Address 1 State,Patient Address 1 Zipcode,Patient Address 2 Line 1,Patient Address 2 Line 2,Patient Address 2 City,Patient Address 2 State,Patient Address 2 Zipcode,Patient Home Phone,Patient Cell Phone,Patient Work Phone,Patient SSN,Patient DOB,Guarantor Last Name,Guarantor First Name,Guarantor Middle Initial,Guarantor Address,Guarantor City,Guarantor State,Guarantor Zipcode,Guarantor Phone Number,Pt Balance Pushed,Claim Date of Service,Claim Uncollected Amount,Claim Id\n"));
        assertTrue(result.toString().contains(date + ",4,Benner,Patricia,A,1259 Thompson Pl,,San Antonio,TX,78226,,,,,,,************,,451336432,11/30/1957,lastName,firstName,m,streetAddress,city,state,11111,,,,0,1\n"));
    }
}
