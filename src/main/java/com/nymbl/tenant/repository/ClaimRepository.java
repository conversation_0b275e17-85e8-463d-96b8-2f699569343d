package com.nymbl.tenant.repository;

import com.nymbl.config.SqlConstants;
import com.nymbl.tenant.model.Claim;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * Created by <PERSON> on 05/17/2017.
 */
public interface ClaimRepository extends JpaRepository<Claim, Long>, JpaSpecificationExecutor<Claim> {

    @Query(value = "select c from Claim c inner join c.prescription p where p.patientId = :patientId")
    List<Claim> findPatientClaim0(@Param("patientId") Long patientId);

    @Query(value = "select c from Claim c inner join c.prescription p where c.totalPtResponsibilityBalance > 0 and c.totalClaimBalance < 1 and p.patientId = :patientId")
    List<Claim> findPatientClaim1(@Param("patientId") Long patientId);

    List<Claim> findByPrescriptionId(Long prescriptionId);

    Claim findTopByPrescriptionIdOrderByIdAsc(Long prescriptionId);

    @Query(value = "SELECT Count(distinct c.id), SUM(Distinct(i.total_allowable)), SUM(Distinct(c.total_claim_amount)), SUM(Distinct(c.total_pt_responsibility_amount)) " +
            "FROM insurance_verification_l_code i " +
            "JOIN prescription_l_code p ON p.id = i.prescription_l_code_id " +
            "JOIN claim c ON c.prescription_id = p.prescription_id " +
            "JOIN claim_submission s ON s.claim_id = c.id " +
            "WHERE s.submission_date between :startDate and :endDate", nativeQuery = true)
    List<Object[]> getTotalBilledReport(@Param("startDate") String startDate, @Param("endDate") String endDate);

    @Query(value = "SELECT c FROM Claim c " +
            "WHERE c.id IN (SELECT DISTINCT cs.claimId FROM ClaimSubmission cs WHERE cs.submissionDate BETWEEN :startDate AND :endDate) ")
    List<Claim> getClaimsByClaimSubmissionDate(@Param("startDate") Date startDate,
                                               @Param("endDate") Date endDate);

//    @Query(value = "select distinct c.* from claim c\n" +
//        "join (\n" +
//        getRowNumberOrderedInfoActivePrescriptionsWithClaimsWithClaimSubmissionOnly +
//        ") as a where a.num=1 and a.claim_id=c.id\n" +
//        "AND a.claim_submission_date BETWEEN :startDate AND :endDate ; ", nativeQuery = true)
//    List<Claim> getActiveClaimsByFirstClaimSubmissionDate(@Param("startDate") Date startDate,
//                                                          @Param("endDate") Date endDate);


    @Query(value = ClaimSQL.hcpcBillingQuery, nativeQuery = true)
    List<Object[]> hcpcBilling(@Param(value = "branchId") Long branchId,
                               @Param(value = "startDate") Date startDate,
                               @Param(value = "endDate") Date endDate,
                               @Param(value = "deviceType") String deviceType,
                               @Param(value = "isSubmission") boolean isSubmission,
                               @Param(value = "usePatientBranch") boolean usePatientBranch);

    @Query(value = "select c from Claim c where c.additionalComment IS NOT NULL AND c.additionalComment <> ''")
    List<Claim> findByAdditionalCommentIsNotNull();

    @Query(value = ClaimSQL.findMassIvlcTotalsByClaimIdsQuery, nativeQuery = true)
    List<Object[]> findMassIvlcTotalsByClaimIds(@Param(value = "branchId") Long branchId,
                                                @Param(value = "startDate") Date startDate,
                                                @Param(value = "endDate") Date endDate,
                                                @Param(value = "deviceType") String deviceType,
                                                @Param(value = "isSubmission") boolean isSubmission,
                                                @Param(value = "usePatientBranch") boolean usePatientBranch);


    @Query(value = ClaimSQL.findClaimsWithPaymentWithOrWithoutClaimSubmissionQuery, nativeQuery = true)
    List<Claim> findClaimsWithPaymentWithOrWithoutClaimSubmission(@Param(value = "branchId") Long branchId,
                                                                  @Param(value = "startDate") Date startDate,
                                                                  @Param(value = "endDate") Date endDate);

    @Query(value = ClaimSQL.findByPrescriptionClaimBranchIdAndDateOfServiceBetweenQuery, nativeQuery = true)
    List<Claim> findByPrescriptionClaimBranchIdAndDateOfServiceBetween(@Param(value = "branchId") Long branchId,
                                                                                @Param(value = "startDate") Date startDate,
                                                                                @Param(value = "endDate") Date endDate,
                                                                                @Param(value = "isSubmission") boolean isSubmission,
                                                                                @Param(value = "requiresClaimSubmission") boolean requiresClaimSubmission);


    @Query(value = "select distinct c.* from claim c\n" +
            "join prescription rx on rx.id=c.prescription_id\n" +
            "join patient pt on pt.id=rx.patient_id\n" +
            "join (\n" +
            ClaimSQL.getRowNumberOrderedInfoActivePrescriptionsWithClaimsWithClaimSubmissionOnly +
            ") as a on a.prescription_id=rx.id and (:branchId is null OR :branchId = 0 OR c.billing_branch_id= :branchId) and a.claim_id=c.id \n" +
            "where a.claim_submission_date BETWEEN :startDate AND :endDate " +
            "AND rx.active = 1 AND rx.id <> 0 AND pt.active = 1 AND pt.id <> 0 \n"
            , nativeQuery = true)
    List<Claim> findByActivePrescriptionPatientPrimaryBranchIdAndDateOfServiceBetween(@Param(value = "branchId") Long branchId,
                                                                                      @Param(value = "startDate") Date startDate,
                                                                                      @Param(value = "endDate") Date endDate);

    List<Claim> findByDateOfServiceBetween(@Param(value = "startDate") Date startDate,
                                           @Param(value = "endDate") Date endDate);

    List<Claim> findAllByTotalPtResponsibilityBalanceIsGreaterThan(@Param(value = "total_pt_responsibility_balance") BigDecimal patientBalance);


    @Query(value = "SELECT claim.* FROM claim WHERE uncollected IS NOT NULL AND uncollected > 0 AND patient_export_date BETWEEN :startDate AND :endDate ", nativeQuery = true)
    List<Claim> findUncollected(@Param(value = "startDate") Date startDate,
                                @Param(value = "endDate") Date endDate);

    @Query(value = "SELECT c.* FROM claim c \n" +
            "JOIN payment p on p.claim_id = c.id\n" +
            "JOIN prescription rx ON c.prescription_id = rx.id \n" +
            "JOIN patient pt ON rx.patient_id = pt.id \n" +
            "WHERE c.uncollected IS NOT NULL AND c.uncollected > 0\n" +
            "and p.description = 'Sending patient responsibility balance to collections.  Remaining patient responsibility balance is reduced to zero.' " +
            "AND p.deposit_date BETWEEN :startDate AND :endDate " +
            "AND (:branchId is null OR :branchId = 0 OR c.billing_branch_id = :branchId)", nativeQuery = true)
    List<Claim> findUncollectedByBranch(@Param(value = "branchId") Long branchId,
                                        @Param(value = "startDate") Date startDate,
                                        @Param(value = "endDate") Date endDate);

    @Query(value = "SELECT c FROM Claim c " +
            "INNER JOIN c.prescription p " +
            "INNER JOIN p.patient pt " +
            "WHERE c.totalPtResponsibilityBalance IS NOT NULL " +
            "AND c.totalPtResponsibilityBalance > :patientBalance " +
            "AND c.billingBranchId = :branchId " +
            "AND p.active = true AND p.id <> 0 ")
    List<Claim> findAllByBranchIdAndTotalPtResponsibilityBalanceIsGreaterThan(@Param("branchId") Long branchId, @Param("patientBalance") BigDecimal patientBalance);

    Claim findTop1ByPrescriptionIdAndPatientInsuranceIdOrderByIdDesc(Long prescriptionId, Long patientInsuranceId);

    Claim findByPatientInsuranceId(Long patientInsuranceID);

//    @Query(value = "SELECT c.* FROM claim c " +
//            "INNER JOIN prescription p ON c.prescription_id=p.id " +
//            "WHERE p.active = true " +
//            "AND c.id IN (SELECT MAX(id) FROM claim GROUP BY prescription_id) " +
//            "AND total_pt_responsibility_balance > 0 " +
//            "ORDER BY c.prescription_id", nativeQuery = true)
//    List<Claim> findAllMaxClaimsWherePrescriptionIsActiveAndPatientResponsibilityBalanceGreaterThanZero();

    @Query(value = "SELECT c.* FROM claim c " +
            "JOIN prescription p ON c.prescription_id = p.id " +
            "JOIN patient pt ON p.patient_id = pt.id " +
            "JOIN branch b ON c.billing_branch_id = b.id " +
            "WHERE p.active = 1 " +
            "AND c.total_claim_balance > 0 " +
            "AND b.id = :branchId ;", nativeQuery = true)
    List<Claim> findByMostRecentClaimAndBranchId(@Param("branchId") Long branchId);

    @Query(value = "SELECT c.* FROM claim c " +
            "JOIN prescription p ON c.prescription_id = p.id " +
            "JOIN patient pt ON p.patient_id = pt.id " +
            "WHERE p.active = 1 " +
            "AND c.total_claim_balance > 0 ;", nativeQuery = true)
    List<Claim> findMostRecentClaim();

    @Query(value = "SELECT SUM(total_claim_balance) FROM claim c " +
            "JOIN prescription p ON p.id = c.prescription_id " +
            "JOIN patient pt ON pt.id = p.patient_id " +
            "WHERE p.active = 1 " +
            "AND (:branchId IS null OR :branchId = 0 OR c.billing_branch_id = :branchId) ;", nativeQuery = true)
    BigDecimal getSumOfInsuranceBalancesFromAllClaimsWithActivePrescriptionByBranch(@Param(value = "branchId") Long branchId);

    @Query(value = "SELECT SUM(total_pt_responsibility_balance) FROM claim c " +
            "JOIN claim_submission cs ON cs.claim_id = c.id " +
            "JOIN prescription p ON p.id = c.prescription_id " +
//            "JOIN patient pt ON pt.id = p.patient_id " +
            "WHERE p.active = 1 " +
            "AND (:branchId IS null OR :branchId = 0 OR c.billing_branch_id = :branchId) ;", nativeQuery = true)
    BigDecimal getSumOfPatientBalancesFromAllClaimsWithActivePrescriptionByBranch(@Param(value = "branchId") Long branchId);

    @Query(value = "SELECT c.* FROM claim c " +
            "JOIN claim_submission cs ON cs.claim_id = c.id " +
            "LEFT JOIN prescription p ON c.prescription_id = p.id " +
            "LEFT JOIN patient pt ON p.patient_id = pt.id " +
            "WHERE p.active = 1 " +
            "AND (:branchId IS null OR :branchId = 0 OR c.billing_branch_id = :branchId)" +
            "AND (c.total_claim_balance != 0 OR c.total_pt_responsibility_balance != 0) ; ", nativeQuery = true)
    List<Claim> getAllClaimsWithActivePrescriptionAndOutstandingBalanceByBranch(@Param("branchId") Long branchId);

    @Query(value = "SELECT c.* FROM claim c " +
            "WHERE (c.total_claim_balance > 0.00 " +
            "OR c.total_pt_responsibility_balance > 0.00) " +
            "AND c.updated_at is not null " +
            "AND c.updated_at < (CURRENT_DATE - INTERVAL 30 DAY) " +
            "and (:branchId = 0 OR :branchId IS null OR c.billing_branch_id = :branchId) " +
            "ORDER BY updated_at ASC " +
            "LIMIT 50; ", nativeQuery = true)
    List<Claim> findAllByUpdatedAtBeforeAndTotalClaimBalanceGreaterThanOrTotalPtResponsibilityBalanceGreaterThanAndBranchId(@Param("branchId") Long branchId);


    @Query(value = "SELECT cs.claim_id AS claimId, pt.id as patientId, ic.id as insuranceId, concat(pt.first_name, ' ', pt.last_name) as ptName, iv.carrier_type, MIN(cs.submission_date), c.date_of_service " +
            "FROM claim_submission cs " +
            "INNER JOIN claim c ON cs.claim_id = c.id " +
            "INNER JOIN insurance_verification iv ON cs.patient_insurance_id = iv.patient_insurance_id " +
            "INNER JOIN insurance_verification_l_code ivlc ON iv.id = ivlc.insurance_verification_id " +
            "INNER JOIN patient_insurance pin ON cs.patient_insurance_id = pin.id " +
            "INNER JOIN insurance_company ic ON pin.insurance_company_id = ic.id " +
            "INNER JOIN patient pt ON pin.patient_id = pt.id " +
            "LEFT JOIN task t ON t.claim_id = c.id " +
            "WHERE (:branchId = 0 OR :branchId IS null OR c.billing_branch_id = :branchId) " +
            "AND c.total_claim_balance <> 0 " +
            "GROUP BY cs.claim_id, cs.patient_insurance_id, iv.carrier_type ; ", nativeQuery = true)
    List<Object[]> getLightWeightClaimDataWithBalanceGreaterThanZero(@Param("branchId") Long branchId);

    @Query(value = "SELECT DISTINCT c.* FROM claim c " +
            "INNER JOIN prescription rx ON c.prescription_id = rx.id " +
            "INNER JOIN patient pt ON rx.patient_id = pt.id " +
            "WHERE rx.active = 1 " +
            "AND pt.active = 1 " +
            "AND (:branchId = 0 OR :branchId IS null OR c.billing_branch_id = :branchId) ", nativeQuery = true)
    List<Claim> findAllClaimsWithActivePatientAndActivePrescriptionByBranch(@Param("branchId") Long branchId);

    //Put in Where prescriptionId not Zero
    @Query(value = ClaimSQL.findAllLightWeightClaimByBranchQuery, nativeQuery = true)
    List<Object[]> findAllLightWeightClaimByBranch(@Param(value = "startDate") java.util.Date startDate, @Param(value = "endDate") java.util.Date endDate, @Param("branchId") Long branchId, @Param("isSubmission") boolean isSubmission);

    @Query(value = "select c.* from claim c join (" + ClaimSQL.getRowNumberOrderedInfoActivePrescriptionsWithClaimsWithClaimSubmissionOnly + ") as cso on cso.claim_id=c.id and cso.prescription_id=c.prescription_id and cso.patient_insurance_id=c.patient_insurance_id \n" +
            "where cso.num=1 \n" +
            "AND (:branchId = 0 OR :branchId IS null OR cso.billing_branch_id = :branchId) \n" +
            "and cso.claim_submission_date BETWEEN :startDate AND :endDate ", nativeQuery = true)
    List<Claim> findFirstClaimSubmissionOnly(@Param(value = "startDate") java.util.Date startDate, @Param(value = "endDate") java.util.Date endDate, @Param("branchId") Long branchId);

    @Query(value = "SELECT c.* FROM claim c " +
            "JOIN prescription rx ON c.prescription_id = rx.id " +
            "JOIN patient pt ON rx.patient_id = pt.id " +
            "WHERE c.total_pt_responsibility_balance < 0 " +
            "AND (:branchId = 0 OR :branchId IS null OR c.billing_branch_id = :branchId) " +
            "AND pt.active = 1 AND pt.id <> 0 " +
            "AND rx.active = 1 AND rx.id <> 0 ;", nativeQuery = true)
    List<Claim> getPatientRefundClaimListByBranch(@Param(value = "branchId") Long branchId);

    @Query(value = SqlConstants.findClaimsByPrescriptionIdWithoutClaimSubmissionAndAppliedPayments, nativeQuery = true)
    List<Claim> getClaimsByPrescriptionIdWithoutSubmissionOrAppliedPayments(@Param(value = "prescriptionId") Long prescriptionId);

    @Query(value = SqlConstants.findClaimsToUpdateWithoutAnyClaimSubmissionOrAppliedPaymentsOnThePrescription, nativeQuery = true)
    List<Claim> getClaimsToUpdateByPrescriptionNotHavingClaimSubmissionsOrAppliedPayments(@Param(value = "prescriptionId") Long prescriptionId);

    @Query(value = "select DATEDIFF(FROM_UNIXTIME(UNIX_TIMESTAMP()), FROM_UNIXTIME((rev_timestamp/1000))) from audit_revision where revision_id = " +
    		"(select min(a.revision_id) from claim_audit a join claim c on a.id = c.id and a.nymbl_status_id = c.nymbl_status_id where c.id = :claimId)", nativeQuery = true)
    Integer getDaysInClaimStatus(@Param("claimId") Long claimId);

    @Query(value = "SELECT COUNT(*) " +
        "FROM claim c " +
        "JOIN prescription rx ON c.prescription_id = rx.id " +
        "JOIN patient pt ON rx.patient_id = pt.id " +
            "JOIN claim_submission csb on c.id = csb.claim_id " +
            "WHERE (:branchId is null OR pt.primary_branch_id = :branchId) " +
            "AND pt.primary_branch_id is not null " +
            "AND c.created_by_id = :userId " +
            "AND c.created_at BETWEEN :startDate AND :endDate ;",
            nativeQuery = true)
    Integer getClaimCountByBranchAndCreatedByUserBetweenDate(@Param("branchId") Long branchId,
                                                             @Param("userId") Long userId,
                                                             @Param("startDate") String startDate,
                                                             @Param("endDate") String endDate);

    @Query(value = "SELECT IF(parent.rental_billing_periods IS NOT NULL, 'true', 'false') FROM claim c " +
            "LEFT JOIN prescription rx ON c.prescription_id = rx.id " +
            "LEFT JOIN prescription parent ON rx.parent_id = parent.id " +
            "WHERE c.id = :claimId ;", nativeQuery = true)
    Boolean getClaimIsAutoGeneratedCRT(@Param("claimId") Long claimId);

}

