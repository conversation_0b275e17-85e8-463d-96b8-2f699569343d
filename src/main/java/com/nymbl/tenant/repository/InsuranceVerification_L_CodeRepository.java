package com.nymbl.tenant.repository;

import com.nymbl.config.SqlConstants;
import com.nymbl.tenant.model.InsuranceVerification_L_Code;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * Created by <PERSON> on 05/17/2017.
 */
@Repository
public interface InsuranceVerification_L_CodeRepository extends JpaRepository<InsuranceVerification_L_Code, Long>, JpaSpecificationExecutor<InsuranceVerification_L_Code> {

    InsuranceVerification_L_Code findByInsuranceVerificationIdAndPrescriptionLCodeId(Long insuranceVerificationId, Long prescriptionLCodeId);

    List<InsuranceVerification_L_Code> findByInsuranceVerificationId(Long insuranceVerificationId);

    List<InsuranceVerification_L_Code> findByInsuranceVerificationIdAndCoveredTrue(Long insuranceVerificationId);

    List<InsuranceVerification_L_Code> findByInsuranceVerificationIdAndBillTrueOrderByPrescriptionLCode_OrderNum(Long insuranceVerificationId);
    @Query(value = "SELECT * FROM insurance_verification_l_code ivlc " +
        "JOIN prescription_l_code plc ON ivlc.prescription_l_code_id = plc.id " +
        "WHERE insurance_verification_id =  :insuranceVerificationId " +
        "AND ivlc.covered = 1 " +
        "ORDER BY plc.order_num ", nativeQuery = true)
    List<InsuranceVerification_L_Code> findByInsuranceVerificationIdAndCoveredTrueAndOrderByPlcOrderNum(@Param(value = "insuranceVerificationId") Long insuranceVerificationId);

    @Query(value = "SELECT * FROM insurance_verification_l_code ivlc " +
            "JOIN prescription_l_code plc ON ivlc.prescription_l_code_id = plc.id " +
            "WHERE insurance_verification_id =  :insuranceVerificationId " +
            "AND ivlc.bill = 1 " +
            "ORDER BY plc.order_num ", nativeQuery = true)
    List<InsuranceVerification_L_Code> findByInsuranceVerificationIdAndBillTrueAndOrderByPlcOrderNum(@Param(value = "insuranceVerificationId") Long insuranceVerificationId);

    InsuranceVerification_L_Code findTopByInsuranceVerificationIdAndPrescriptionLCodeIdOrderByIdDesc(Long insuranceVerificationId, Long prescriptionLCodeId);

    List<InsuranceVerification_L_Code> findByPrescriptionLCodeId(Long prescriptionLCodeId);

    //String getRowNumberOrderedInfoPrescriptionsWithClaimsWithClaimSubmissionOnly = getRowNumberOrderedInfoActivePrescriptionsWithClaimsWithClaimSubmissionOnly;

    List<InsuranceVerification_L_Code> findByPrescriptionLCode_PrescriptionIdAndInsuranceVerificationCarrierType(Long prescriptionId, String carrierType);

    @Query(value = InsuranceVerificationSQL.findByPrescriptionIdWithCalculatedFirstCarrier, nativeQuery = true)
    List<InsuranceVerification_L_Code> findByPrescriptionIdWithCalculatedFirstCarrier(@Param(value = "prescriptionId") Long prescriptionId);

    List<InsuranceVerification_L_Code> findByInsuranceVerification_PatientInsurance_IdAndInsuranceVerification_Prescription_Id(Long patientInsuranceId, Long prescriptionId);

    @Query(value = "SELECT sum(total_allowable) FROM insurance_verification_l_code ivlc " +
        "JOIN insurance_verification iv ON ivlc.insurance_verification_id = iv.id " +
        "JOIN prescription rx ON iv.prescription_id = rx.id " +
        "JOIN patient p ON rx.patient_id = p.id " +
        "JOIN claim c ON c.prescription_id = rx.id " +
        "JOIN (" +
            InsuranceVerificationSQL.getJoinNumEqualOne +
            "   ) as ivf ON ivf.insurance_verification_id=ivlc.insurance_verification_id and ivf.patient_insurance_id=iv.patient_insurance_id and ivf.prescription_id=iv.prescription_id " +
            "WHERE rx.active = 1 AND (c.total_claim_balance > 0 or c.total_pt_responsibility_balance > 0) AND ivlc.covered = 1 AND p.id = :patientId ", nativeQuery = true)
    BigDecimal getTotalAllowableByPatientId(@Param(value = "patientId") Long patientId);

    @Query(value = "SELECT SUM(COALESCE(ivlc.total_charge, 0.00)) AS billable, " +
            "SUM(COALESCE(ivlc.total_allowable, 0.00)) AS allowable, " +
            "SUM(COALESCE(ivlc.total_charge,0.00)) - sum(COALESCE(ivlc.total_allowable, 0.00)) AS writeOff, " +
            "SUM(COALESCE(ivlc.sales_tax,0.00)) AS salesTax " +
            "FROM insurance_verification_l_code ivlc " +
            "WHERE ivlc.id IN " +
            "(SELECT DISTINCT ivlc.id FROM claim c " +
            "JOIN claim_submission cs ON cs.claim_id = c.id " +
            "JOIN prescription rx ON c.prescription_id = rx.id  " +
            "JOIN patient pt ON rx.patient_id = pt.id " +
            "JOIN insurance_verification iv ON iv.patient_insurance_id = c.responsible_patient_insurance_id and iv.prescription_id = rx.id " +
            "JOIN insurance_verification_l_code ivlc ON ivlc.insurance_verification_id = iv.id " +
            "JOIN device_type dt ON rx.device_type_id = dt.id " +
            "JOIN (" +
            InsuranceVerificationSQL.getJoinNumEqualOne +
            "   ) as ivf ON ivf.insurance_verification_id=ivlc.insurance_verification_id and ivf.patient_insurance_id=iv.patient_insurance_id and ivf.prescription_id=iv.prescription_id " +
            "WHERE rx.active = 1 AND pt.active = 1 AND rx.id <> 0 " +
            "AND dt.orthotic_or_prosthetic like :deviceType " +
            "AND ivf.claim_submission_date BETWEEN :startDate AND :endDate  " +
            "AND ( :branchId = 0 OR :branchId IS null OR c.billing_branch_id = :branchId ) " +
            "AND ivlc.use_sales_tax = 1 ) ;", nativeQuery = true)
    List<Object[]> getSalesTaxTotals(@Param(value = "startDate") Date startDate,
                                     @Param(value = "endDate") Date endDate,
                                     @Param(value = "branchId") Long branchId,
                                     @Param(value = "deviceType") String deviceType);

    //AR - AGING
    @Query(value = "SELECT SUM(COALESCE(ivlc.total_charge, 0.00)) AS billable, " +
            "SUM(COALESCE(ivlc.total_allowable, 0.00)) AS allowable," +
            "(SUM(COALESCE(ivlc.total_charge,0.00)) - sum(COALESCE(ivlc.total_allowable, 0.00))) * -1 AS writeOff " +
        "FROM insurance_verification_l_code ivlc " +
        "WHERE ivlc.id IN" +
        "(SELECT DISTINCT ivlc.id FROM claim c " +
        "   JOIN claim_submission cs ON cs.claim_id = c.id " +
            "   JOIN prescription rx ON c.prescription_id = rx.id " +
            "   JOIN patient pt ON rx.patient_id = pt.id " +
            "   JOIN prescription_l_code plc ON plc.prescription_id = rx.id " +
            "   JOIN insurance_verification_l_code ivlc ON ivlc.prescription_l_code_id = plc.id " +
            "   JOIN insurance_verification iv ON ivlc.insurance_verification_id = iv.id " +
            "   JOIN device_type dt ON rx.device_type_id = dt.id " +
            "   JOIN (" +
            InsuranceVerificationSQL.getJoinNumEqualOne +
            "   ) as ivf ON ivf.insurance_verification_id=ivlc.insurance_verification_id and ivf.patient_insurance_id=iv.patient_insurance_id and ivf.prescription_id=iv.prescription_id " +
            "   WHERE rx.active = 1 AND pt.active = 1 " +
            "   AND iv.patient_insurance_id = c.responsible_patient_insurance_id " +
            "   AND dt.orthotic_or_prosthetic like :deviceType " +
            "   AND ivf.claim_submission_date BETWEEN :startDate AND :endDate " +
            "   AND ( :branchId = 0 OR :branchId IS null OR c.billing_branch_id = :branchId ));", nativeQuery = true)
    List<Object[]> getReportsBillablesAndAllowables(@Param(value = "startDate") Date startDate,
                                                    @Param(value = "endDate") Date endDate,
                                                    @Param(value = "branchId") Long branchId,
                                                    @Param(value = "deviceType") String deviceType);

    @Query(value = InsuranceVerificationSQL.getPractitionerBillingTotalsBySubmissionDateBetweenAndBranchIdQuery, nativeQuery = true)
    List<Object[]> getPractitionerBillingTotalsBySubmissionDateBetweenAndBranchId(@Param(value = "startDate") String startDate,
                                                                                  @Param(value = "endDate") String endDate,
                                                                                  @Param(value = "branchId") Long branchId,
                                                                                  @Param(value = "deviceType") String deviceType,
                                                                                  @Param(value = "isSubmission") boolean isSubmission,
                                                                                  @Param(value = "usePatientBranch") boolean usePatientBranch);

    @Query(value = InsuranceVerificationSQL.getViewUserBillingTotalsBySubmissionDateBetweenAndBranchIdQuery, nativeQuery = true)
    List<Object[]> getViewUserBillingTotalsBySubmissionDateBetweenAndBranchId(@Param(value = "startDate") String startDate,
                                                                              @Param(value = "endDate") String endDate,
                                                                              @Param(value = "branchId") Long branchId,
                                                                              @Param(value = "deviceType") String deviceType,
                                                                              @Param(value = "isSubmission") boolean isSubmission,
                                                                              @Param(value = "usePatientBranch") boolean usePatientBranch);

    @Query(value = "SELECT rx.treating_practitioner_id AS treatingPractitionerId, " +
        "lcc.id AS lCodeCategory,  " +
        "SUM(COALESCE(ivlc.total_charge, 0.00)) AS billable, " +
        "SUM(COALESCE(ivlc.total_allowable, 0.00)) AS allowable " +
        "FROM insurance_verification_l_code ivlc " +
        "JOIN prescription_l_code plc ON ivlc.prescription_l_code_id = plc.id " +
        "JOIN prescription rx ON plc.prescription_id = rx.id " +
        "JOIN patient pt ON rx.patient_id = pt.id " +
        "JOIN l_code lc ON plc.l_code_id = lc.id " +
        "LEFT JOIN l_code_category lcc ON lc.l_code_category_id = lcc.id " +
        "WHERE ivlc.id IN " +
        "(SELECT DISTINCT ivlc.id FROM claim c " +
        "   JOIN claim_submission cs ON cs.claim_id = c.id " +
            "   JOIN prescription rx ON c.prescription_id = rx.id " +
            "   JOIN patient pt ON rx.patient_id = pt.id " +
            "   JOIN prescription_l_code plc ON plc.prescription_id = rx.id " +
            "   JOIN insurance_verification_l_code ivlc ON ivlc.prescription_l_code_id = plc.id " +
            "   JOIN insurance_verification iv ON ivlc.insurance_verification_id = iv.id " +
            "   JOIN device_type dt ON rx.device_type_id = dt.id " +
            "   JOIN (" +
            InsuranceVerificationSQL.getJoinNumEqualOne +
            "   ) as ivf ON ivf.insurance_verification_id=ivlc.insurance_verification_id and ivf.patient_insurance_id=iv.patient_insurance_id and ivf.prescription_id=iv.prescription_id " +
            "   WHERE rx.active = 1 AND pt.active = 1 " +
            "   AND iv.patient_insurance_id = c.responsible_patient_insurance_id " +
            "   AND dt.orthotic_or_prosthetic like :deviceType " +
            "   AND ivf.claim_submission_date BETWEEN :startDate AND :endDate " +
            "   AND ( :branchId = 0 OR :branchId IS null OR c.billing_branch_id = :branchId )  ) " +
            "GROUP BY rx.treating_practitioner_id, lcc.id " +
            "ORDER BY rx.treating_practitioner_id ; ", nativeQuery = true)
    List<Object[]> getPractitionerBillingTotalsCategoriesBySubmissionDateBetweenAndBranchId(@Param(value = "startDate") String startDate,
                                                                                            @Param(value = "endDate") String endDate,
                                                                                            @Param(value = "branchId") Long branchId,
                                                                                            @Param(value = "deviceType") String deviceType);

    @Query(value = InsuranceVerificationSQL.getIvlcTotalsByClaimIdQuery, nativeQuery = true)
    List<Object[]> getIvlcTotalsByClaimId(@Param(value = "claimId") Long claimId);

    // IMPORTANT! If this query is changed, the above query `getIvlcTotalsByClaimId` must also be changed!
//    @Query(value = "SELECT ivlc.* " +
//            "FROM claim c " +
//            "INNER JOIN prescription rx ON c.prescription_id = rx.id " +
//            "INNER JOIN prescription_l_code plc ON plc.prescription_id = rx.id " +
//            "INNER JOIN patient_insurance pin ON c.responsible_patient_insurance_id = pin.id " +
//            "INNER JOIN insurance_verification iv ON pin.id = iv.patient_insurance_id AND iv.prescription_id = rx.id " +
//            "INNER JOIN insurance_verification_l_code ivlc ON iv.id = ivlc.insurance_verification_id and ivlc.prescription_l_code_id = plc.id " +
//            "WHERE c.id = :claimId ; ", nativeQuery = true)
    @Query(value = "SELECT ivlc.* " +
            "FROM claim c " +
            "INNER JOIN prescription rx ON c.prescription_id = rx.id " +
            "INNER JOIN patient p ON rx.patient_id = p.id " +
            "INNER JOIN insurance_verification iv ON c.responsible_patient_insurance_id = iv.patient_insurance_id AND iv.prescription_id = c.prescription_id " +
            "INNER JOIN insurance_verification_l_code ivlc ON iv.id = ivlc.insurance_verification_id " +
            "WHERE c.id = :claimId AND rx.id <> 0 AND rx.active = 1 AND p.id <> 0 AND p.active = 1 ; ", nativeQuery = true)
    List<InsuranceVerification_L_Code> getIvlcsByClaimId(@Param(value = "claimId") Long claimId);

    List<InsuranceVerification_L_Code> findByExpirationDate(Date expirationDate);


    @Query(value = InsuranceVerificationSQL.ivlcsByExpirationDateWhereInsuranceVerificationIsActiveAndRxHasNoClaimSubmissionQuery, nativeQuery = true)
    List<InsuranceVerification_L_Code> findByExpirationDateWhereInsuranceVerificationIsActiveAndRxHasNoClaimSubmission(@Param(value = "expirationDate") String expirationDate);

    @Query(value = SqlConstants.findPrimaryIvlcForPrescriptionByPrescriptionId, nativeQuery = true)
    List<InsuranceVerification_L_Code> findPrimaryIvlcForPrescriptionByPrescriptionId(@Param(value = "prescriptionId") Long prescriptionId);


    @Query(value = InsuranceVerificationSQL.ivlcsByPrescriptionIdQuery, nativeQuery = true)
    List<Object[]> getIvlcsByPrescriptionId(@Param(value = "prescriptionId") Long prescriptionId);

    @Query(value = SqlConstants.findProperSalesNumbersByPrescriptionId, nativeQuery = true)
    List<Object[]> getProperSalesNumbersByPrescriptionId(@Param(value = "prescriptionId") Long prescriptionId);

    @Query(value = SqlConstants.findPrimaryIvlcForPrescriptionByPrescriptionId, nativeQuery = true)
    List<InsuranceVerification_L_Code> getPrimaryIvlcForPrescriptionByPrescriptionId(@Param(value = "prescriptionId") Long prescriptionId);

}
