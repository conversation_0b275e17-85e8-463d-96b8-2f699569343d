package com.nymbl.tenant.model;

import com.fasterxml.jackson.annotation.JsonBackReference;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.nymbl.config.utils.JsonDateDeserializer;
import com.nymbl.config.utils.JsonDateSerializer;
import com.nymbl.master.model.User;
import com.nymbl.tenant.model.interfaces.ClaimInfo;
import jakarta.persistence.*;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.envers.Audited;
import org.hibernate.envers.NotAudited;

import java.math.BigDecimal;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * Created by <PERSON> on 05/16/2017.
 */
@Getter
@Setter
@EqualsAndHashCode(callSuper = false)
@Entity
@Table(name = "claim")
@Audited
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({
        "id", "prescriptionId", "status", "resubmissionCode", "totalClaimAmount", "totalClaimPaid", "totalClaimBalance",
        "totalPtResponsibilityAmount", "totalPtResponsibilityPaid", "totalPtResponsibilityBalance", "claimBatchId", "dateOfService", "additionalComment", "patientInvoicePrintDate", "invoicePrintDate", "patientExportDate"
})
@JsonIgnoreProperties(ignoreUnknown = true)
public class Claim extends ModelStub implements ClaimInfo {

    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Id
    private Long id;

    @NotAudited
    @Version
    private Integer version = 0;

    @Column(name = "total_claim_amount")
    private BigDecimal totalClaimAmount = BigDecimal.ZERO;

    @Column(name = "total_claim_paid")
    private BigDecimal totalClaimPaid = BigDecimal.ZERO;

    @Column(name = "total_claim_balance")
    private BigDecimal totalClaimBalance = BigDecimal.ZERO;

    @Column(name = "total_pt_responsibility_amount")
    private BigDecimal totalPtResponsibilityAmount = BigDecimal.ZERO;

    @Column(name = "total_pt_responsibility_paid")
    private BigDecimal totalPtResponsibilityPaid = BigDecimal.ZERO;

    @Column(name = "total_pt_responsibility_balance")
    private BigDecimal totalPtResponsibilityBalance = BigDecimal.ZERO;

    @Column(name = "uncollected")
    private BigDecimal uncollected = BigDecimal.ZERO;

    @Column(name = "additional_comment")
    private String additionalComment;

    @Column(name = "additional_info")
    private String additionalInfo;

    @Column(name = "resubmission_code")
    private String resubmissionCode;

    @Column(name = "original_ref_num")
    private String originalRefNum;

    @Column(name = "accept_assignment")
    private Boolean acceptAssignment = true;

    @Column(name = "payment_plan")
    private Boolean paymentPlan;

    @JsonSerialize(using = JsonDateSerializer.class)
    @JsonDeserialize(using = JsonDateDeserializer.class)
    @Column(name = "patient_invoice_print_date")
    private Date patientInvoicePrintDate;

    @JsonSerialize(using = JsonDateSerializer.class)
    @JsonDeserialize(using = JsonDateDeserializer.class)
    @Column(name = "invoice_print_date")
    private Date invoicePrintDate;

    @JsonSerialize(using = JsonDateSerializer.class)
    @JsonDeserialize(using = JsonDateDeserializer.class)
    @Column(name = "patient_export_date")
    private java.sql.Date patientExportDate;

    @JsonSerialize(using = JsonDateSerializer.class)
    @JsonDeserialize(using = JsonDateDeserializer.class)
    @Column(name = "date_of_service")
    private java.sql.Date dateOfService;

    @JsonSerialize(using = JsonDateSerializer.class)
    @JsonDeserialize(using = JsonDateDeserializer.class)
    @Column(name = "date_resolved")
    private java.sql.Date dateResolved;

    @JsonSerialize(using = JsonDateSerializer.class)
    @JsonDeserialize(using = JsonDateDeserializer.class)
    @Column(name = "pt_balance_pushed_date")
    private java.sql.Date ptBalancePushedDate;

    @Column(name = "nymbl_status_id")
    private Long nymblStatusId;

    @Column(name = "prescription_id")
    private Long prescriptionId;

    @Column(name = "user_id")
    private Long userId;

    @Column(name = "patient_insurance_id")
    private Long patientInsuranceId;

    @Column(name = "other_patient_insurance_id")
    private Long otherPatientInsuranceId;

    @Column(name = "responsible_patient_insurance_id")
    private Long responsiblePatientInsuranceId;

    @Column(name = "billing_branch_id")
    private Long billingBranchId;

    @Column(name = "form1500_template_id")
    private Long form1500TemplateId;

    @Column(name = "override_rt_rules_engine", columnDefinition = "TINYINT(1) DEFAULT '0'")
    private Boolean overrideRTRulesEngine;

    @Column(name = "created_by_id")
    private Long createdById;

    @JsonSerialize(using = JsonDateSerializer.class)
    @JsonDeserialize(using = JsonDateDeserializer.class)
    @Column(name = "created_at")
    private java.sql.Date createdAt;

    @Column(name = "updated_by_id")
    private Long updatedById;

    @JsonSerialize(using = JsonDateSerializer.class)
    @JsonDeserialize(using = JsonDateDeserializer.class)
    @Column(name = "updated_at")
    private java.sql.Date updatedAt;

    @Column(name = "overflow_narrative")
    private String overflowNarrative;

    @Column(name = "nymbl_rcm")
    private Boolean nymblRcm = true;

    @NotAudited
    @ManyToOne
    @JoinColumn(name = "nymbl_status_id", referencedColumnName = "id", insertable = false, updatable = false, foreignKey = @ForeignKey(name = "FK_claim_nymblstatusid"))
    private NymblStatus nymblStatus;

    @NotAudited
    @ManyToOne
    @JoinColumn(name = "prescription_id", referencedColumnName = "id", insertable = false, updatable = false, foreignKey = @ForeignKey(name = "FK_claim_prescriptionid"))
    private Prescription prescription;

    @NotAudited
    @Transient
    @ManyToOne
//    @JoinColumn(name = "user_id", referencedColumnName = "id", insertable = false, updatable = false, foreignKey = @ForeignKey(name = "FK_claim_userid"))
    private User user;

    @NotAudited
    @ManyToOne
    @JoinColumn(name = "patient_insurance_id", referencedColumnName = "id", insertable = false, updatable = false, foreignKey = @ForeignKey(name = "FK_claim_patientinsuranceid"))
    private PatientInsurance patientInsurance;

    @NotAudited
    @ManyToOne
    @JoinColumn(name = "other_patient_insurance_id", referencedColumnName = "id", insertable = false, updatable = false, foreignKey = @ForeignKey(name = "FK_claim_otherpatientinsuranceid"))
    private PatientInsurance otherPatientInsurance;

    @NotAudited
    @ManyToOne
    @JoinColumn(name = "responsible_patient_insurance_id", referencedColumnName = "id", insertable = false, updatable = false, foreignKey = @ForeignKey(name = "FK_claim_responsiblepatientinsuranceid"))
    private PatientInsurance responsiblePatientInsurance;

    @NotAudited
    @ManyToOne
    @JoinColumn(name = "billing_branch_id", referencedColumnName = "id", insertable = false, updatable = false, foreignKey = @ForeignKey(name = "FK_claim_billingbranchid"))
    private Branch billingBranch;

    @NotAudited
    @Transient
    @ManyToOne
//    @JoinColumn(name = "created_by_id", referencedColumnName = "id", insertable = false, updatable = false, foreignKey = @ForeignKey(name = "FK_claim_createdbyid"))
    private User createdBy;

    @NotAudited
    @Transient
    @ManyToOne
//    @JoinColumn(name = "updated_by_id", referencedColumnName = "id", insertable = false, updatable = false, foreignKey = @ForeignKey(name = "FK_claim_updatedbyid"))
    private User updatedBy;

    @NotAudited
    @OneToMany(mappedBy = "claim")
    @JsonBackReference("claim-submissions")
    private List<ClaimSubmission> claimSubmissions;

    @NotAudited
    @OneToMany(mappedBy = "claim")
    @JsonBackReference("applied-payments")
    private List<AppliedPayment> appliedPayments;

    @NotAudited
    @OneToMany(mappedBy = "claim")
    @JsonBackReference("tasks")
    private List<Task> tasks;

    @PrePersist
    public void prePersist() {
        java.sql.Date now = new java.sql.Date(Calendar.getInstance().getTime().getTime());
        createdAt = now;
        updatedAt = now;
        if (nymblStatusId == null) {
            nymblStatusId = 11L;
        }
        setDefaults();
    }

    @PreUpdate
    public void preUpdate() {
        java.sql.Date now = new java.sql.Date(Calendar.getInstance().getTime().getTime());
        updatedAt = now;
        setDefaults();
    }

    public void setDefaults() {
        if (totalClaimAmount == null) {
            totalClaimAmount = BigDecimal.ZERO;
        }
        if (totalClaimPaid == null) {
            totalClaimPaid = BigDecimal.ZERO;
        }
        if (totalClaimBalance == null) {
            totalClaimBalance = BigDecimal.ZERO;
        }
        if (totalPtResponsibilityAmount == null) {
            totalPtResponsibilityAmount = BigDecimal.ZERO;
        }
        if (totalPtResponsibilityPaid == null) {
            totalPtResponsibilityPaid = BigDecimal.ZERO;
        }
        if (totalPtResponsibilityBalance == null) {
            totalPtResponsibilityBalance = BigDecimal.ZERO;
        }
    }

    @Override
    public String toString() {
        return "Claims Details Changed " + id;
    }

}
