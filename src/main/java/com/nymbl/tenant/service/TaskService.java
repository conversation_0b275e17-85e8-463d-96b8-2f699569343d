package com.nymbl.tenant.service;

import com.nymbl.config.Constants;
import com.nymbl.config.dto.reports.ITaskTracking;
import com.nymbl.config.service.AbstractTableService;
import com.nymbl.config.utils.DateUtil;
import com.nymbl.config.utils.StringUtil;
import com.nymbl.master.service.UserService;
import com.nymbl.tenant.model.Notification;
import com.nymbl.tenant.model.Task;
import com.nymbl.tenant.model.UserNotification;
import com.nymbl.tenant.model.UserRole;
import com.nymbl.tenant.repository.TaskRepository;
import com.nymbl.tenant.repository.UserNotificationRepository;
import com.nymbl.tenant.repository.UserRoleRepository;
import com.nymbl.tenant.specification.TaskSpecs;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class TaskService extends AbstractTableService<Task, Long> {

  private final TaskRepository taskRepository;
  private final UserService userService;
  private final RoleService roleService;
  private final NotificationService notificationService;
  private final ClaimService claimService;
  private final UserNotificationRepository userNotificationRepository;

  private final UserRoleRepository userRoleRepository;

  @Autowired
  public TaskService(
          TaskRepository taskRepository,
          UserService userService,
          RoleService roleService,
          NotificationService notificationService,
          @Lazy ClaimService claimService,
          UserNotificationRepository userNotificationRepository, UserRoleRepository userRoleRepository) {
    super(taskRepository);
    this.taskRepository = taskRepository;
    this.userService = userService;
    this.roleService = roleService;
    this.notificationService = notificationService;
    this.claimService = claimService;
    this.userNotificationRepository = userNotificationRepository;
    this.userRoleRepository = userRoleRepository;
  }

  public List<Task> search(
      Long userId,
      String keyword,
      String priority,
      Boolean completed,
      String ownedBy,
      Long patientId,
      Long prescriptionId,
      Long claimId,
      Long branchId,
      String insuranceId,
      java.util.Date startDate,
      java.util.Date endDate,
      Pageable pageable) {
    Page<Task> tasks = null;
    List<Long> employeeIds =
        userService.getActiveEmployeesByManagerId(userId).stream()
            .map(u -> u.getId())
            .collect(Collectors.toList());
    List<Long> userRoles =
            userRoleRepository.findByUserId(userId).stream()
                    .map(UserRole::getRoleId)
                    .collect(Collectors.toList());
    TaskSpecs spec =
        new TaskSpecs(
            keyword,
            priority,
            startDate,
            endDate,
            completed,
            ownedBy,
            patientId,
            prescriptionId,
            claimId,
            userId,
            employeeIds,
            insuranceId != null && !StringUtil.isBlank(insuranceId) ? Long.parseLong(insuranceId) : null,
            branchId, userRoles);
    if (spec.getHasSpec()) {
      tasks = taskRepository.findAll(spec, pageable);
    } else {
      tasks = taskRepository.findAll(pageable);
    }

    for (Task o : tasks.getContent()) {
      loadForeignKeys(o);
    }

    List<Task> finalList = new ArrayList<>(tasks.getContent());
    for (Task t : finalList) {
      if (t.getClaimId() != null) {
        t.setClaim(claimService.findOne(t.getClaimId()));
      }
    }
    return finalList;
  }

  public String exportTasks(
      Long userId,
      String keyword,
      String priority,
      Boolean completed,
      String ownedBy,
      String insuranceId,
      Long patientId,
      Long branchId,
      java.util.Date startDate,
      java.util.Date endDate,
      Pageable pageable) {
    List<Task> results =
        search(
            userId,
            keyword,
            priority,
            completed,
            ownedBy,
            patientId,
            null,
            null,
            branchId,
            insuranceId,
            startDate,
            endDate,
            pageable);
    return exportTasksCsvContent(results);
  }

  public String exportTasksCsvContent(List<Task> results) {
    StringBuilder sb = new StringBuilder();
    sb.append(
        "name,priority,due_date,patient,prescription,claim_id,insurance,user,completed_by,date_completed,created_by,created_date,description\n");
    for (Task task : results) {
      Object field = null;
      if (task.getName() != null) {
        addFieldForCsv(sb, "\"" + task.getName() + "\"");
      } else {
        addFieldForCsv(sb, null);
      }
      if (task.getPriority() != null) {
        addFieldForCsv(sb, task.getPriority());
      } else {
        addFieldForCsv(sb, null);
      }

      if (task.getDueDate() != null) {
        addFieldForCsv(sb, DateUtil.getStringDate(task.getDueDate(), Constants.WAYSTAR_DATE_FORMAT));
      } else {
        addFieldForCsv(sb, null);
      }

      if (task.getPatientId() != null && task.getPatient() != null) {
        addFieldForCsv(
            sb,
            "\"#"
                + task.getPatientId()
                + " "
                + task.getPatient().getFirstName()
                + " "
                + task.getPatient().getLastName()
                + "\"");
      } else {
        addFieldForCsv(sb, null);
      }

      if (task.getPrescription() != null
          && task.getPrescription().getDeviceType() != null
          && task.getPrescription().getDeviceType().getName() != null) {
        addFieldForCsv(sb, "\"" + task.getPrescription().getDeviceType().getName() + "\"");
      } else {
        addFieldForCsv(sb, null);
      }

      if (task.getClaimId() != null) {
        addFieldForCsv(sb, task.getClaimId());
      } else {
        addFieldForCsv(sb, null);
      }

      if (task.getClaim() != null
          && task.getClaim().getPatientInsurance() != null
          && task.getClaim().getPatientInsurance().getInsuranceCompany() != null
          && task.getClaim().getPatientInsurance().getInsuranceCompany().getName() != null) {
        addFieldForCsv(
            sb,
            "\"" + task.getClaim().getPatientInsurance().getInsuranceCompany().getName() + "\"");
      } else {
        addFieldForCsv(sb, null);
      }

      if (task.getUser() != null
          && task.getUser().getFirstName() != null
          && task.getUser().getLastName() != null) {
        addFieldForCsv(
            sb, "\"" + task.getUser().getFirstName() + " " + task.getUser().getLastName() + "\"");
      } else {
        addFieldForCsv(sb, null);
      }
      if (task.getCompletedBy() != null
          && task.getCompletedBy().getFirstName() != null
          && task.getCompletedBy().getLastName() != null) {
        addFieldForCsv(
            sb,
            "\""
                + task.getCompletedBy().getFirstName()
                + " "
                + task.getCompletedBy().getLastName()
                + "\"");
      } else {
        addFieldForCsv(sb, null);
      }

      if (task.getDateCompleted() != null) {
        addFieldForCsv(
            sb, DateUtil.getStringDate(task.getDateCompleted(), Constants.WAYSTAR_DATE_FORMAT));
      } else {
        addFieldForCsv(sb, null);
      }

      if (task.getCreatedBy() != null) {
        addFieldForCsv(
            sb,
            "\""
                + task.getCreatedBy().getFirstName()
                + " "
                + task.getCreatedBy().getLastName()
                + "\"");
      } else {
        addFieldForCsv(sb, null);
      }

      if (task.getCreatedAt() != null) {
        addFieldForCsv(
            sb, DateUtil.getStringDate(task.getCreatedAt(), Constants.WAYSTAR_DATE_FORMAT));
      } else {
        addFieldForCsv(sb, null);
      }

      if (task.getDescription() != null && task.getDescription().length() > 32767) {
        String warning = " ERROR: This text is too long to fit and must be truncated";
        String temp = task.getDescription().substring(0, 32767 - warning.length()) + warning;
        sb.append("\"" + temp + "\"");
      } else {
        sb.append(task.getDescription() != null ? "\"" + task.getDescription() + "\"" : "");
      }
      sb.append("\n");
    }
    // System.out.println("results = \n"+sb.toString());
    return sb.toString();
  }

  public void addFieldForCsv(StringBuilder sb, Object field) {
    sb.append(field != null ? field.toString().concat(",") : ",");
  }

  public List<Task> findByPrescriptionId(Long prescriptionId) {
    return taskRepository.findByPrescriptionId(prescriptionId);
  }

  public List<Task> findByClaimIdAndType(Long claimId, String type) {
    List<Task> results = taskRepository.findByClaimIdAndType(claimId, type);
    for (Task o : results) {
      loadForeignKeys(o);
    }
    return results;
  }

  public List<Task> findByPatientId(Long patientId) {
    List<Task> results = taskRepository.findByPatientId(patientId);
    for (Task o : results) {
      loadForeignKeys(o);
    }
    return results;
  }

  public List<Task> findByClaimId(Long claimId) {
    List<Task> results = taskRepository.findByClaimId(claimId);
    for (Task o : results) {
      loadForeignKeys(o);
    }
    return results;
  }

  public List<Task> findByDueDateAndCompleted(Date date) {
    List<Task> tasks = taskRepository.findByDueDateAndCompleted(date, false);
    for (Task o : tasks) {
      loadForeignKeys(o);
    }
    return tasks;
  }

  public List<Task> findByDueDateAndTypeAndCompleted(Date date, String type, Boolean completed) {
    List<Task> tasks = taskRepository.findByDueDateAndTypeAndCompleted(date, type, completed);
    for (Task o : tasks) {
      loadForeignKeys(o);
    }
    return tasks;
  }

  public List<ITaskTracking> getTasksByCreatedDate(Date startDate, Date endDate) {
    List<ITaskTracking> results = taskRepository.getTaskTrackingReport(startDate, endDate);

    return results;
  }

  public Task saveTask(Task task) {
    if (task.getCompleted() && task.getDateCompleted() == null) {
      task.setCompletedById(userService.getCurrentUser().getId());
      task.setDateCompleted(new Timestamp(Calendar.getInstance().getTimeInMillis()));
    } else if (!task.getCompleted()) {
      task.setCompletedById(null);
      task.setDateCompleted(null);
    }
    if (task.getId() == null && task.getCreatedById() == null)
      task.setCreatedById(userService.getCurrentUser().getId());
    Task savedTask = save(task);
    if (savedTask.getUser() != null || savedTask.getRole() != null) {
      notificationService.createTaskNotification(savedTask);
    }

    // TODO determine if the read notification should be done here
    return savedTask;
  }

  public void deleteTaskAndNotification(Task task) {
    Notification n = notificationService.findOneByTaskId(task.getId());
    if (n != null) {
      List<UserNotification> userNotifications =
          userNotificationRepository.findByNotificationId(n.getId());
      userNotificationRepository.deleteAll(userNotifications);
      notificationService.delete(n.getId());
    }
    taskRepository.delete(task);
  }

  private boolean isMine(Long userId, Task t) {
    // unused and not role-compliant (NYM-448)
    return userId.equals(t.getUserId());
  }

  private boolean isManager(Long userId, Task t) {
    boolean result;
    result =
        t != null
            && t.getUser() != null
            && userId != null
            && (t.getUser().getManagerId() != null && t.getUser().getManagerId().equals(userId)
                || t.getUser().getManagerTwoId() != null
                    && t.getUser().getManagerTwoId().equals(userId)
                || t.getUser().getManagerThreeId() != null
                    && t.getUser().getManagerThreeId().equals(userId));

    return result;

    // if (t == null || t.getUser() == null || t.getUser().getManagerId() == null) {
    //     return false;
    // }
    // return false;
  }

  @Override
  public void loadForeignKeys(Task o) {
    if (o != null) {
      if (o.getUserId() != null) {
        o.setUser(userService.findOne(o.getUserId()));
      }
      if (o.getRoleId() != null) {
        o.setRole(roleService.findOne(o.getRoleId()));
      }
      if (o.getCreatedById() != null) {
        o.setCreatedBy(userService.findOne(o.getCreatedById()));
      }
      if (o.getCompletedById() != null) {
        o.setCompletedBy(userService.findOne(o.getCompletedById()));
      }
    }
  }
}
