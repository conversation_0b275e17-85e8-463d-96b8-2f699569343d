package com.nymbl.tenant.controller;

import com.nymbl.config.controller.AbstractController;
import com.nymbl.config.service.Print1500Service;
import com.nymbl.config.utils.FileUtil;
import com.nymbl.tenant.model.BulkClaimJob;
import com.nymbl.tenant.model.Claim;
import com.nymbl.tenant.model.ClaimFile;
import com.nymbl.tenant.model.ClaimSubmission;
import com.nymbl.tenant.service.BulkClaimJobService;
import com.nymbl.tenant.service.ClaimFileService;
import com.nymbl.tenant.service.ClaimService;
import com.nymbl.tenant.service.ClaimSubmissionService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/claim-file")
public class ClaimFileController extends AbstractController<ClaimFile, Long> {

    private static final Logger logger = LoggerFactory.getLogger(ClaimFileController.class);

    private final ClaimFileService claimFileService;
    private final ClaimSubmissionService claimSubmissionService;
    private final Print1500Service print1500Service;
    private final FileUtil fileUtil;
    private final BulkClaimJobService bulkClaimJobService;
    private final ClaimService claimService;

    @Autowired
    public ClaimFileController(ClaimFileService claimFileService,
                               ClaimSubmissionService claimSubmissionService,
                               Print1500Service print1500Service,
                               FileUtil fileUtil,
                               BulkClaimJobService bulkClaimJobService,
                               ClaimService claimService) {
        super(claimFileService);
        this.claimFileService = claimFileService;
        this.claimSubmissionService = claimSubmissionService;
        this.print1500Service = print1500Service;
        this.fileUtil = fileUtil;
        this.bulkClaimJobService = bulkClaimJobService;
        this.claimService = claimService;
    }

    //    @Auditable(entry = "Get Claim Submissions By Claim Id")
    @GetMapping(value = "/claim/{claimId}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> findByClaimId(@PathVariable Long claimId, HttpServletRequest request) {
        List<ClaimFile> results = claimFileService.findByClaimId(claimId);
        return ResponseEntity.ok(results);
    }

    @GetMapping(value = "/cms-print/{claimFileId}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> printSubmittedCMS(@PathVariable Long claimFileId, HttpServletRequest request) throws Exception {
        logger.info("Starting printSubmittedCMS for claimFileId: {}", claimFileId);
        List<byte[]> results = new ArrayList<>();
        ClaimFile claimFile = claimFileService.findOne(claimFileId);
        if (claimFile == null) {
            logger.error("ClaimFile not found for ID: {}", claimFileId);
            return ResponseEntity.notFound().build();
        }
        logger.info("Found ClaimFile with ID: {}", claimFileId);

        List<ClaimSubmission> claimSubmissions = claimSubmissionService.findByClaimFileId(claimFileId);
        logger.info("Found {} ClaimSubmissions for ClaimFile ID: {}", claimSubmissions != null ? claimSubmissions.size() : 0, claimFileId);

        // Check if this is a bulk claim submission
        if (claimSubmissions != null && !claimSubmissions.isEmpty()) {
            ClaimSubmission submission = claimSubmissions.get(0);
            logger.info("Examining ClaimSubmission");

            Claim claim = null;
            try {
                claim = submission.getClaim();
            } catch (Exception e) {
                logger.error("Error getting claim from submission: {}", e.getMessage());
            }

            if (claim != null) {
                Long claimId = null;
                try {
                    claimId = claim.getId();
                    logger.info("Found Claim ID: {}", claimId);
                } catch (Exception e) {
                    logger.error("Error getting claim ID: {}", e.getMessage());
                }

                Boolean isBulkSubmission = false;
                String bulkClaimJobId = null;

                try {
                    isBulkSubmission = claim.getBulkSubmission();
                    bulkClaimJobId = claim.getBulkClaimJobId();
                    logger.info("Claim bulk info - bulkSubmission: {}, bulkClaimJobId: {}",
                        isBulkSubmission, bulkClaimJobId);
                } catch (Exception e) {
                    logger.error("Error getting bulk claim info: {}", e.getMessage());
                }

                if (Boolean.TRUE.equals(isBulkSubmission) && bulkClaimJobId != null && claimId != null) {
                    logger.info("Detected bulk claim submission, redirecting to bulk CMS print endpoint for claim: {}", claimId);
                    return printBulkCMS(claimId, request);
                }
            } else {
                logger.warn("Claim is null for ClaimSubmission");
            }
        } else {
            logger.warn("No ClaimSubmissions found for ClaimFile ID: {}", claimFileId);
        }

        // Regular claim processing
        logger.info("Processing as regular claim file");
        String fileContents = null;
        try {
            fileContents = claimFile.getContents();
            logger.info("Got file contents, length: {}", fileContents != null ? fileContents.length() : 0);
        } catch (Exception e) {
            logger.error("Error getting file contents: {}", e.getMessage());
        }

        List<String> files = print1500Service.printFromClaimFile(fileContents, claimSubmissions);
        logger.info("Generated {} PDF files", files.size());

        for (String file : files) {
            logger.info("Loading file: {}", file);
            byte[] fileData = fileUtil.loadEobContents(file);
            results.add(fileData);
        }
        logger.info("Returning {} PDF files", results.size());
        return ResponseEntity.ok(results);
    }

    /**
     * Print CMS 1500 form for a claim that is part of a bulk submission.
     *
     * @param claimId the claim ID
     * @param request the HTTP request
     * @return the response entity with the PDF file contents
     * @throws Exception if an error occurs
     */
    @GetMapping(value = "/cms-print-bulk/{claimId}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> printBulkCMS(@PathVariable Long claimId, HttpServletRequest request) throws Exception {
        logger.info("Starting printBulkCMS for claimId: {}", claimId);

        List<byte[]> results = new ArrayList<>();

        // Get the claim
        Claim claim = null;
        try {
            claim = claimService.findOne(claimId);
            logger.info("Retrieved claim with ID: {}", claimId);
        } catch (Exception e) {
            logger.error("Error retrieving claim with ID {}: {}", claimId, e.getMessage(), e);
            return ResponseEntity.status(500).body("Error retrieving claim: " + e.getMessage());
        }

        if (claim == null) {
            logger.error("Claim not found: {}", claimId);
            return ResponseEntity.notFound().build();
        }

        // Check if the claim is part of a bulk submission
        Boolean isBulkSubmission = false;
        String bulkClaimJobId = null;

        try {
            isBulkSubmission = claim.getBulkSubmission();
            bulkClaimJobId = claim.getBulkClaimJobId();
            logger.info("Claim bulk info - bulkSubmission: {}, bulkClaimJobId: {}",
                isBulkSubmission, bulkClaimJobId);
        } catch (Exception e) {
            logger.error("Error getting bulk claim info: {}", e.getMessage(), e);
            return ResponseEntity.status(500).body("Error checking bulk claim status: " + e.getMessage());
        }

        if (bulkClaimJobId == null || !Boolean.TRUE.equals(isBulkSubmission)) {
            logger.error("Claim {} is not part of a bulk submission", claimId);
            return ResponseEntity.badRequest().body("Claim is not part of a bulk submission");
        }

        // Get the bulk claim job
        BulkClaimJob bulkClaimJob = null;
        try {
            bulkClaimJob = bulkClaimJobService.findByJobId(bulkClaimJobId);
            logger.info("Retrieved bulk claim job with ID: {}", bulkClaimJobId);
        } catch (Exception e) {
            logger.error("Error retrieving bulk claim job with ID {}: {}", bulkClaimJobId, e.getMessage(), e);
            return ResponseEntity.status(500).body("Error retrieving bulk claim job: " + e.getMessage());
        }

        if (bulkClaimJob == null) {
            logger.error("Bulk claim job not found for ID: {}", bulkClaimJobId);
            return ResponseEntity.notFound().build();
        }

        // Get the X12 file content
        String x12Content = null;
        try {
            // Use reflection to get the X12 content
            java.lang.reflect.Method getX12FileContentMethod = bulkClaimJob.getClass().getMethod("getX12FileContent");
            Object x12ContentObj = getX12FileContentMethod.invoke(bulkClaimJob);
            if (x12ContentObj instanceof String) {
                x12Content = (String) x12ContentObj;
            }
            logger.info("Retrieved X12 content, length: {}", x12Content != null ? x12Content.length() : 0);
        } catch (Exception e) {
            logger.error("Error retrieving X12 content from bulk claim job: {}", e.getMessage(), e);
            return ResponseEntity.status(500).body("Error retrieving X12 content: " + e.getMessage());
        }

        if (x12Content == null || x12Content.isEmpty()) {
            logger.error("X12 file content not found for bulk claim job: {}", bulkClaimJobId);
            Map<String, String> errorResponse = new HashMap<>();
            errorResponse.put("error", "X12 content is not available for this bulk claim job. The original X12 file may not be stored in the system.");
            return ResponseEntity.badRequest().body(errorResponse);
        }

        try {
            // Print the CMS 1500 form for this specific claim from the bulk X12 file
            logger.info("Calling print1500Service.printBulkX12CMS for claim: {}", claimId);
            List<String> files = print1500Service.printBulkX12CMS(x12Content, claimId);
            logger.info("Generated {} PDF files", files.size());

            // Convert the files to byte arrays
            for (String file : files) {
                logger.info("Loading file: {}", file);
                byte[] fileData = fileUtil.loadEobContents(file);
                results.add(fileData);
            }

            logger.info("Returning {} PDF files", results.size());
            return ResponseEntity.ok(results);
        } catch (Exception e) {
            logger.error("Error printing CMS 1500 form for bulk claim {}: {}", claimId, e.getMessage(), e);
            return ResponseEntity.badRequest().body("Error printing CMS 1500 form: " + e.getMessage());
        }
    }
}
