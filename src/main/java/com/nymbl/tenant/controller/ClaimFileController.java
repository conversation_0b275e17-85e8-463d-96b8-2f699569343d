package com.nymbl.tenant.controller;

import com.nymbl.config.controller.AbstractController;
import com.nymbl.config.service.Print1500Service;
import com.nymbl.config.utils.FileUtil;
import com.nymbl.tenant.model.ClaimFile;
import com.nymbl.tenant.model.ClaimSubmission;
import com.nymbl.tenant.service.ClaimFileService;
import com.nymbl.tenant.service.ClaimSubmissionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;

@RestController
@RequestMapping("/api/claim-file")
public class ClaimFileController extends AbstractController<ClaimFile, Long> {

    private final ClaimFileService claimFileService;
    private final ClaimSubmissionService claimSubmissionService;
    private final Print1500Service print1500Service;
    private final FileUtil fileUtil;

    @Autowired
    public ClaimFileController(ClaimFileService claimFileService,
                               ClaimSubmissionService claimSubmissionService,
                               Print1500Service print1500Service,
                               FileUtil fileUtil) {
        super(claimFileService);
        this.claimFileService = claimFileService;
        this.claimSubmissionService = claimSubmissionService;
        this.print1500Service = print1500Service;
        this.fileUtil = fileUtil;
    }

    //    @Auditable(entry = "Get Claim Submissions By Claim Id")
    @GetMapping(value = "/claim/{claimId}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> findByClaimId(@PathVariable Long claimId, HttpServletRequest request) {
        List<ClaimFile> results = claimFileService.findByClaimId(claimId);
        return ResponseEntity.ok(results);
    }

    @GetMapping(value = "/cms-print/{claimFileId}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> printSubmittedCMS(@PathVariable Long claimFileId, HttpServletRequest request) throws Exception {
        List<byte[]> results = new ArrayList<>();
        ClaimFile claimFile = claimFileService.findOne(claimFileId);
        List<ClaimSubmission> claimSubmissions = claimSubmissionService.findByClaimFileId(claimFileId);
        List<String> files = print1500Service.printFromClaimFile(claimFile.getContents(), claimSubmissions);
        for (String file : files) {
            byte[] contents = fileUtil.loadEobContents(file);
            results.add(contents);
        }
        return ResponseEntity.ok(results);
    }
}
