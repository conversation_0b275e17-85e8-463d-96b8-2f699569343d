package com.nymbl.tenant.controller;

import com.nymbl.config.security.PrivilegeChecker;
import com.nymbl.tenant.dto.BulkClaimJobDTO;
import com.nymbl.tenant.model.BulkClaimJob;
import com.nymbl.tenant.model.Claim;
import com.nymbl.tenant.model.SystemSetting;
import com.nymbl.tenant.service.BulkClaimJobService;
import com.nymbl.tenant.service.ClaimService;
import com.nymbl.tenant.service.SystemSettingService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * REST controller for managing bulk claim jobs.
 */
@RestController
@RequestMapping("/api/bulk-claim-jobs")
@Tag(name = "Bulk Claim Jobs", description = "Endpoints for managing bulk claim job operations")
public class BulkClaimJobController {

    private static final Logger logger = LoggerFactory.getLogger(BulkClaimJobController.class);

    private final BulkClaimJobService bulkClaimJobService;
    private final ClaimService claimService;
    private final PrivilegeChecker privilegeChecker;
    private final SystemSettingService systemSettingService;

    @Autowired
    public BulkClaimJobController(BulkClaimJobService bulkClaimJobService,
                                 ClaimService claimService,
                                 PrivilegeChecker privilegeChecker,
                                 SystemSettingService systemSettingService) {
        this.bulkClaimJobService = bulkClaimJobService;
        this.claimService = claimService;
        this.privilegeChecker = privilegeChecker;
        this.systemSettingService = systemSettingService;
    }

    /**
     * GET /api/bulk-claim-jobs : Get all bulk claim jobs.
     *
     * @return the ResponseEntity with status 200 (OK) and the list of bulk claim jobs in body
     */
    @GetMapping
    @Operation(summary = "Get all bulk claim jobs", description = "Returns a list of all bulk claim jobs")
    public ResponseEntity<List<BulkClaimJobDTO>> getAllBulkClaimJobs() {
        List<BulkClaimJob> jobs = bulkClaimJobService.findAll();
        List<BulkClaimJobDTO> jobDTOs = jobs.stream()
                .map(BulkClaimJobDTO::fromEntity)
                .toList();
        return ResponseEntity.ok(jobDTOs);
    }

    /**
     * GET /api/bulk-claim-jobs/:id : Get a bulk claim job by ID.
     *
     * @param jobId the ID of the bulk claim job to retrieve
     * @return the ResponseEntity with status 200 (OK) and the bulk claim job in body, or with status 404 (Not Found)
     */
    @GetMapping("/{jobId}")
    @Operation(summary = "Get a bulk claim job by ID", description = "Returns a bulk claim job by its ID")
    public ResponseEntity<BulkClaimJobDTO> getBulkClaimJob(@PathVariable String jobId) {
        BulkClaimJob job = bulkClaimJobService.findByJobId(jobId);
        if (job == null) {
            return ResponseEntity.notFound().build();
        }
        return ResponseEntity.ok(BulkClaimJobDTO.fromEntity(job));
    }

    /**
     * GET /api/bulk-claim-jobs/:id/claims : Get all claims associated with a bulk claim job.
     *
     * @param jobId the ID of the bulk claim job
     * @return the ResponseEntity with status 200 (OK) and the list of claims in body, or with status 404 (Not Found)
     */
    @GetMapping("/{jobId}/claims")
    @Operation(summary = "Get all claims associated with a bulk claim job", description = "Returns a list of claims associated with a bulk claim job")
    public ResponseEntity<List<Claim>> getClaimsByBulkClaimJobId(@PathVariable String jobId) {
        BulkClaimJob job = bulkClaimJobService.findByJobId(jobId);
        if (job == null) {
            return ResponseEntity.notFound().build();
        }
        List<Claim> claims = claimService.findByBulkClaimJobId(jobId);
        return ResponseEntity.ok(claims);
    }

    /**
     * GET /api/bulk-claim-jobs/:id/x12 : Download the X12 file for a bulk claim job.
     *
     * @param jobId the ID of the bulk claim job
     * @return the ResponseEntity with status 200 (OK) and the X12 file as content, or with status 404 (Not Found)
     */
    @GetMapping("/{jobId}/x12")
    @Operation(summary = "Download the X12 file for a bulk claim job", description = "Returns the X12 file content for a bulk claim job")
    public ResponseEntity<String> downloadX12File(@PathVariable String jobId) {
        BulkClaimJob job = bulkClaimJobService.findByJobId(jobId);
        if (job == null) {
            return ResponseEntity.notFound().build();
        }

        // Get the X12 file content
        String x12Content = job.getX12FileContent();

        if (x12Content == null || x12Content.isEmpty()) {
            return ResponseEntity.notFound().build();
        }

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.TEXT_PLAIN);
        headers.setContentDispositionFormData("attachment", "bulk_claim_" + jobId + ".txt");

        return new ResponseEntity<>(x12Content, headers, HttpStatus.OK);
    }

    /**
     * POST /api/bulk-claim-jobs/validate : Validate claims for bulk submission.
     *
     * @param claimIds the list of claim IDs to validate
     * @return the ResponseEntity with status 200 (OK) and validation results in body
     */
    @PostMapping("/validate")
    @Operation(summary = "Validate claims for bulk submission", description = "Validates if the selected claims can be submitted together in a bulk file")
    public ResponseEntity<Map<String, Object>> validateBulkSubmission(@RequestBody List<Long> claimIds) {
        // Check if bulk claims are enabled in system settings
        SystemSetting setting = systemSettingService.findBySectionAndField("billing", "enable_bulk_claims");
        String enableBulkClaims = setting != null ? setting.getValue() : "N";
        if (!"Y".equals(enableBulkClaims)) {
            return ResponseEntity.status(HttpStatus.FORBIDDEN)
                    .body(Map.of(
                            "valid", false,
                            "errors", List.of("Bulk claims feature is not enabled for this tenant")
                    ));
        }

        // Check if user has the bulk_claim_submit privilege
        if (!privilegeChecker.hasPrivilege("bulk_claim_submit")) {
            return ResponseEntity.status(HttpStatus.FORBIDDEN)
                    .body(Map.of(
                            "valid", false,
                            "errors", List.of("You do not have permission to submit bulk claims")
                    ));
        }

        List<String> errors = claimService.validateBulkSubmission(claimIds);
        Map<String, Object> result = Map.of(
            "valid", errors.isEmpty(),
            "errors", errors
        );
        return ResponseEntity.ok(result);
    }

    /**
     * POST /api/bulk-claim-jobs/submit : Submit claims in bulk.
     *
     * @param request the request containing claim IDs and billing branch ID
     * @return the ResponseEntity with status 200 (OK) and job information in body
     */
    @PostMapping("/submit")
    @Operation(summary = "Submit claims in bulk", description = "Creates a bulk claim job and processes the claims in a batch")
    public ResponseEntity<Map<String, Object>> submitBulkClaims(@RequestBody BulkClaimSubmitRequest request) {
        try {
            // Check if bulk claims are enabled in system settings
            SystemSetting setting = systemSettingService.findBySectionAndField("billing", "enable_bulk_claims");
            String enableBulkClaims = setting != null ? setting.getValue() : "N";
            if (!"Y".equals(enableBulkClaims)) {
                return ResponseEntity.status(HttpStatus.FORBIDDEN)
                        .body(Map.of(
                                "success", false,
                                "error", "Bulk claims feature is not enabled for this tenant"
                        ));
            }

            // Check if user has the bulk_claim_submit privilege
            if (!privilegeChecker.hasPrivilege("bulk_claim_submit")) {
                return ResponseEntity.status(HttpStatus.FORBIDDEN)
                        .body(Map.of(
                                "success", false,
                                "error", "You do not have permission to submit bulk claims"
                        ));
            }

            Map<String, Object> result = claimService.sendBulkClaimFiles(request.getClaimIds(), request.getBillingBranchId());
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            logger.error("Error submitting bulk claims", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of(
                        "success", false,
                        "error", e.getMessage()
                    ));
        }
    }

    /**
     * Request class for bulk claim submission.
     */
    public static class BulkClaimSubmitRequest {
        private List<Long> claimIds;
        private Long billingBranchId;

        public List<Long> getClaimIds() {
            return claimIds;
        }

        public void setClaimIds(List<Long> claimIds) {
            this.claimIds = claimIds;
        }

        public Long getBillingBranchId() {
            return billingBranchId;
        }

        public void setBillingBranchId(Long billingBranchId) {
            this.billingBranchId = billingBranchId;
        }
    }
}
