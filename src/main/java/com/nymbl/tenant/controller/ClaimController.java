package com.nymbl.tenant.controller;

import com.nymbl.config.Constants;
import com.nymbl.config.controller.AbstractController;
import com.nymbl.config.dto.ClaimSearchDTO;
import com.nymbl.config.dto.PhysicianBillingEditDTO;
import com.nymbl.config.dto.StatementDTO;
import com.nymbl.config.exception.X12Exception;
import com.nymbl.config.service.ClaimProfileService;
import com.nymbl.config.service.Print1500Service;
import com.nymbl.config.utils.DateUtil;
import com.nymbl.config.utils.FileUtil;
import com.nymbl.config.utils.StringUtil;
import com.nymbl.tenant.Auditable;
import com.nymbl.tenant.dashboard.dto.ClaimDto;
import com.nymbl.tenant.dashboard.service.ClaimsDtoService;
import com.nymbl.tenant.forms.PdfReaderService;
import com.nymbl.tenant.interfaces.IClaimTotals;
import com.nymbl.tenant.model.AutoPostClaimResponse;
import com.nymbl.tenant.model.Claim;
import com.nymbl.tenant.service.ClaimService;
import com.nymbl.tenant.service.PaymentService;
import com.nymbl.tenant.service.StatementService;
import jakarta.servlet.http.HttpServletRequest;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.nymbl.config.utils.OptimisticLockingUtil.SAVED;

/**
 * Created by Bradley Moore on 05/21/2017.
 */
@Slf4j
@RestController
@RequestMapping("/api/claim")
public class ClaimController extends AbstractController<Claim, Long> {

    private final ClaimService claimService;
    private final StatementService statementService;
    private final ClaimProfileService claimProfileService;
    private final FileUtil fileUtil;
    private final ClaimsDtoService claimsDtoService;
    private final Print1500Service print1500Service;
    private final PdfReaderService pdfReaderService;
    private final PaymentService paymentService;

    @Autowired
    public ClaimController(ClaimService claimService,
                           StatementService statementService,
                           ClaimProfileService claimProfileService,
                           FileUtil fileUtil,
                           ClaimsDtoService claimsDtoService,
                           Print1500Service print1500Service,
                           PdfReaderService pdfReaderService,
                           PaymentService paymentService) {
        super(claimService);
        this.claimService = claimService;
        this.statementService = statementService;
        this.claimProfileService = claimProfileService;
        this.fileUtil = fileUtil;
        this.claimsDtoService = claimsDtoService;
        this.print1500Service = print1500Service;
        this.pdfReaderService = pdfReaderService;
        this.paymentService = paymentService;
    }

    @GetMapping(value = "/patient/{patientId}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> findByPatientId(@PathVariable Long patientId,
                                             @RequestParam(name = "claimFor", required = false) Integer claimFor,
                                             HttpServletRequest request) {
        if (claimFor == null) claimFor = 0;
        List<Claim> results = claimService.findByPrescriptionPatientId(patientId, claimFor);
        return ResponseEntity.ok(results);
    }

    @GetMapping(value = "/updateAt/totalClaimBalance/totalPtResponsibilityBalance", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> findAllByUpdatedAtBeforeAndTotalClaimBalanceGreaterThanOrTotalPtResponsibilityBalanceGreaterThanAndBranchId(@RequestParam(name = "branchId", required = false) Long branchId, HttpServletRequest request) {
        List<Claim> results = claimService.findAllByUpdatedAtBeforeAndTotalClaimBalanceGreaterThanOrTotalPtResponsibilityBalanceGreaterThanAndBranchId(branchId);
        return ResponseEntity.ok(results);
    }

    @GetMapping(value = "/prescription/{prescriptionId}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> findByPrescriptionId(@PathVariable Long prescriptionId,
                                                  @RequestParam(name = "claimFor", required = false) Integer claimFor,
                                                  HttpServletRequest request) {
        List<Claim> results = claimService.findByPrescriptionId(prescriptionId);
        return ResponseEntity.ok(results);
    }

    @GetMapping(value = "/autopostclaimresponses/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> findAutoPostClaimResponsesById(@PathVariable Long id, HttpServletRequest request) {
        List<AutoPostClaimResponse> result = claimService.getAutoPostClaimResponseByClaimId(id);
        return ResponseEntity.ok(result);
    }

    @GetMapping(value = "/view/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> findByClaimId(@PathVariable Long id, HttpServletRequest request) {
        Claim result = claimService.findOne(id);
        return ResponseEntity.ok(result);
    }

    @Auditable(entry = "Update Claim")
    @Override
    @PostMapping(produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> save(@RequestBody Claim claim, HttpServletRequest request) {
        Map<String, Object> respMap;
        try {
                respMap = claimService.saveForVersion(claim);

                if (null != respMap.get(SAVED)) {
                    return ResponseEntity.ok(respMap.get(SAVED));
                }

            return ResponseEntity.badRequest().body(respMap);
        } catch ( Exception e){
            String exceptionAsString = StringUtil.getExceptionAsString(e);
            log.error(exceptionAsString);
            return ResponseEntity.badRequest().body(StringUtil.getExceptionAsString(e));
            }
        }

    @Auditable(entry = "Add Claim By Prescription Id")
    @GetMapping(value = "/add/{prescriptionId}")
    public ResponseEntity<?> addClaim(@PathVariable Long prescriptionId,
                                      @RequestParam(name = "userId", required = false) Long userId,
                                      @RequestParam(name = "billingBranchId", required = false) Long billingBranchId,
                                      @RequestParam(name = "dateOfService", required = false) java.sql.Date dateOfService,
                                      @RequestParam(name = "resend", required = false) boolean resend,
                                      HttpServletRequest request) throws Exception {
        Claim result = claimService.addClaim(prescriptionId, userId, billingBranchId, dateOfService, resend);
        List<Claim> list = claimService.findByPrescriptionId(prescriptionId);
        claimService.auditNewlyAddedClaim(list);
        return ResponseEntity.ok(result);
    }

    @GetMapping(value = "/search", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> search(@RequestParam(name = "claimId", required = false) Long claimId,
                                    @RequestParam(name = "patientId", required = false) Long patientId,
                                    @RequestParam(name = "status", required = false) String status,
                                    @RequestParam(name = "nymblStatusId", required = false) Long nymblStatusId,
                                    @RequestParam(name = "branchId", required = false) Long branchId,
                                    @RequestParam(name = "insuranceCompanyId", required = false) Long insuranceCompanyId,
                                    @RequestParam(name = "userId", required = false) Long userId,
                                    @RequestParam(name = "startDate", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date startDate,
                                    @RequestParam(name = "endDate", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date endDate,
                                    @RequestParam(name = "showUnresolved", required = false) Boolean showUnresolved,
                                    @RequestParam(name = "dosStartDate", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date dosStartDate,
                                    @RequestParam(name = "dosEndDate", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date dosEndDate,
                                    @RequestParam(name = "prescriptionId", required = false) Long prescriptionId,
                                    Boolean export,
                                    Pageable pageable,
                                    HttpServletRequest request) {
        java.sql.Date sqlStartDate = startDate != null ? new java.sql.Date(startDate.getTime()) : null;
        java.sql.Date sqlEndDate = endDate != null ? new java.sql.Date(endDate.getTime()) : null;
        java.sql.Date sqlDosStartDate = dosStartDate != null ? new java.sql.Date(dosStartDate.getTime()) : null;
        java.sql.Date sqlDosEndDate = dosEndDate != null ? new java.sql.Date(dosEndDate.getTime()) : null;
        Page<ClaimSearchDTO> results = claimService.sortSearch(claimId, patientId, status, nymblStatusId
                , branchId, insuranceCompanyId, userId, sqlStartDate, sqlEndDate, sqlDosStartDate
                , sqlDosEndDate, showUnresolved, pageable, export, prescriptionId);
        return ResponseEntity.ok(results);
    }

    @GetMapping(value = "/export-claims", produces = MediaType.APPLICATION_JSON_VALUE)
    public String exportClaims(@RequestParam(name = "claimId", required = false) Long claimId,
                               @RequestParam(name = "patientId", required = false) Long patientId,
                               @RequestParam(name = "status", required = false) String status,
                               @RequestParam(name = "nymblStatusId", required = false) Long nymblStatusId,
                               @RequestParam(name = "branchId", required = false) Long branchId,
                               @RequestParam(name = "insuranceCompanyId", required = false) Long insuranceCompanyId,
                               @RequestParam(name = "userId", required = false) Long userId,
                               @RequestParam(name = "startDate", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date startDate,
                               @RequestParam(name = "endDate", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date endDate,
                               @RequestParam(name = "showUnresolved", required = false) Boolean showUnresolved,
                               @RequestParam(name = "dosStartDate", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date dosStartDate,
                               @RequestParam(name = "dosEndDate", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date dosEndDate,
                               @RequestParam(name = "prescriptionId", required = false) Long prescriptionId,
                               Boolean export,
                               Pageable pageable,
                               HttpServletRequest request) {
        java.sql.Date sqlStartDate = startDate != null ? new java.sql.Date(startDate.getTime()) : null;
        java.sql.Date sqlEndDate = endDate != null ? new java.sql.Date(endDate.getTime()) : null;
        java.sql.Date sqlDosStartDate = dosStartDate != null ? new java.sql.Date(dosStartDate.getTime()) : null;
        java.sql.Date sqlDosEndDate = dosEndDate != null ? new java.sql.Date(dosEndDate.getTime()) : null;
        String results = claimService.exportClaims(claimId, patientId, status, nymblStatusId, branchId, insuranceCompanyId
                , userId, sqlStartDate, sqlEndDate, sqlDosStartDate, sqlDosEndDate, showUnresolved, pageable, export, prescriptionId);
        return results;
    }

    @Auditable(entry = "Send Claim Files")
    @GetMapping(value = "/send-files", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> sendClaimFiles(@RequestParam(name = "claimIds") List<Long> claimIds,
                                            @RequestParam(name = "billingBranchId", required = false) Long billingBranchId,
                                            HttpServletRequest request) {
        try {
            boolean success = claimService.sendClaimFiles(claimIds, billingBranchId);
            if (success) {
                return ResponseEntity.ok("Claims sent successfully.");
            } else {
                return ResponseEntity.badRequest().body("Claims did not pass edit rules.");
            }
        } catch (X12Exception e) {
            String messages = e.getMessage();
            return ResponseEntity.badRequest().body(messages);
        }
    }

//    @PostMapping(value = "/send-files-post", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
//    @ResponseBody
//    public ResponseEntity<?> sendClaimFilesPost(@RequestBody List<Long> claimIds, HttpServletRequest request){
//        try {
//            boolean success = claimService.sendClaimFiles(claimIds, null);
//            if (success) {
//                return ResponseEntity.ok("Claims sent successfully.");
//            } else {
//                return ResponseEntity.badRequest().body("Claims did not pass edit rules.");
//            }
//        } catch (X12Exception e) {
//            String messages = e.getMessage();
//            return ResponseEntity.badRequest().body(messages);
//        }
//    }

    @GetMapping(value = "/view-files/all", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> viewFilesAll(@RequestParam(name = "claimId") Long claimId,
                                          @RequestParam(name = "billingBranchId", required = false) Long billingBranchId,
                                          @RequestParam(name = "patientInsuranceId", required = false) Long patientInsuranceId,
                                          @RequestParam(name = "otherPatientInsuranceId", required = false) Long otherPatientInsuranceId,
                                          @RequestParam(name = "form1500TemplateId", required = false) Long form1500TemplateId,
                                          HttpServletRequest request) throws IOException {
        // Full
        Map<String, List<String>> result = print1500Service.getAllPrintClaimFiles(claimId, true, billingBranchId, patientInsuranceId, otherPatientInsuranceId, form1500TemplateId);
        String path = result.size() == 1 ? result.get("files").get(0) : pdfReaderService.mergePdfs(result.get("files"), claimId, "filled_");
        byte[] contents = fileUtil.loadEobContents(path);
        Map<String, Object> response = new HashMap<>();
        response.put("result_full", contents);
        pdfReaderService.delete1500pdfForms(path);
        // Blank
        result = print1500Service.getAllPrintClaimFiles(claimId, false, billingBranchId, patientInsuranceId, otherPatientInsuranceId, form1500TemplateId);
        path = result.size() == 1 ? result.get("files").get(0) : pdfReaderService.mergePdfs(result.get("files"), claimId, "blank_");
        contents = fileUtil.loadEobContents(path);
        response.put("result_blank", contents);
        if (!result.get("validation_errors").isEmpty()) {
            response.put("validation_errors", result.get("validation_errors"));
        }
        pdfReaderService.delete1500pdfForms(path);
        return ResponseEntity.ok(response);
    }

    @GetMapping(value = "/view-files/full", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> viewFilesFull(@RequestParam(name = "claimId") Long claimId,
                                           @RequestParam(name = "billingBranchId", required = false) Long billingBranchId,
                                           @RequestParam(name = "patientInsuranceId", required = false) Long patientInsuranceId,
                                           @RequestParam(name = "otherPatientInsuranceId", required = false) Long otherPatientInsuranceId,
                                           @RequestParam(name = "form1500TemplateId", required = true) Long form1500TemplateId,
                                           HttpServletRequest request) throws IOException {

        Map<String, List<String>> result = print1500Service.getAllPrintClaimFiles(claimId, true, billingBranchId, patientInsuranceId, otherPatientInsuranceId, form1500TemplateId);
        String path = result.get("files").size() == 1 ? result.get("files").get(0) : pdfReaderService.mergePdfs(result.get("files"), claimId, "filled_");

        byte[] contents = fileUtil.loadEobContents(path);
        Map<String, Object> response = new HashMap<>();
        response.put("result", contents);
        if (!result.get("validation_errors").isEmpty()) {
            response.put("validation_errors", result.get("validation_errors"));
        }
        pdfReaderService.delete1500pdfForms(path);

        return ResponseEntity.ok(response);
    }

    @GetMapping(value = "/view-files/blank", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> viewFilesBlank(@RequestParam(name = "claimId") Long claimId,
                                            @RequestParam(name = "billingBranchId", required = false) Long billingBranchId,
                                            @RequestParam(name = "patientInsuranceId", required = false) Long patientInsuranceId,
                                            @RequestParam(name = "otherPatientInsuranceId", required = false) Long otherPatientInsuranceId,
                                            @RequestParam(name = "form1500TemplateId", required = true) Long form1500TemplateId,
                                            HttpServletRequest request) throws IOException {

        Map<String, List<String>> result = print1500Service.getAllPrintClaimFiles(claimId, false, billingBranchId, patientInsuranceId, otherPatientInsuranceId, form1500TemplateId);
        String path = result.get("files").size() == 1 ? result.get("files").get(0) : pdfReaderService.mergePdfs(result.get("files"), claimId, "blank_");
        byte[] contents = fileUtil.loadEobContents(path);
        Map<String, Object> response = new HashMap<>();
        response.put("result", contents);
        if (!result.get("validation_errors").isEmpty()) {
            response.put("validation_errors", result.get("validation_errors"));
        }
        pdfReaderService.delete1500pdfForms(path);

        return ResponseEntity.ok(response);
    }

    @GetMapping(value = "/bulk-update-status", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> bulkUpdateStatus(@RequestParam(name = "claimIds") List<Long> claimIds,
    										  @RequestParam(name = "nymblStatusId") Long nymblStatusId,
                                              HttpServletRequest request) {
        claimService.bulkUpdateClaimStatus(claimIds, nymblStatusId);
    	return ResponseEntity.ok(null);
    }

    @GetMapping(value = "/reassign-claim", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> reassignClaims(@RequestParam(name = "claimIds") List<Long> claimIds,
                                            @RequestParam(name = "userId") Long userId,
                                            HttpServletRequest request) {
        claimService.reassignClaims(claimIds, userId);
        return ResponseEntity.ok(null);
    }

    @GetMapping(value = "/statements/patients", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> findPatientStatements(@RequestParam(name = "patientId", required = false) Long patientId,
                                                   @RequestParam(name = "branchId", required = false) Long branchId,
                                                   @RequestParam(name = "insuranceCompanyId", required = false) Long insuranceCompanyId,
                                                   @RequestParam(name = "aging", required = false) Integer aging,
                                                   @RequestParam(name = "submittedStartDate", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date submittedStartDate,
                                                   @RequestParam(name = "submittedEndDate", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date submittedEndDate,
                                                   @RequestParam(name = "lastSentStartDate", required = false) String lastSentStartDate,
                                                   @RequestParam(name = "lastSentEndDate", required = false) String lastSentEndDate,
                                                   @RequestParam(name = "attempt", required = false) String attempt,
                                                   @RequestParam(name = "dosStartDate", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date dosStartDate,
                                                   @RequestParam(name = "dosEndDate", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date dosEndDate,
                                                   @RequestParam(name = "claimId", required = false) Long claimId,
                                                   @RequestParam(name = "prescriptionId", required = false) Long prescriptionId,
                                                   @RequestParam(name = "unappliedPayments", required = false) Boolean unappliedPayments,
                                                   Pageable pageable,
                                                   HttpServletRequest request) {
        java.sql.Date startDate = lastSentStartDate != null ? DateUtil.getDate(lastSentStartDate, Constants.DF_YYYY_MM_DD) : null;
        java.sql.Date endDate = lastSentEndDate != null ? DateUtil.getDate(lastSentEndDate, Constants.DF_YYYY_MM_DD) : null;
        java.sql.Date sqlDosStartDate = dosStartDate != null ? new java.sql.Date(dosStartDate.getTime()) : null;
        java.sql.Date sqlDosEndDate = dosEndDate != null ? new java.sql.Date(dosEndDate.getTime()) : null;
        Page result = statementService.statements(patientId, branchId, startDate, endDate, sqlDosStartDate, sqlDosEndDate, attempt, claimId, prescriptionId, unappliedPayments, pageable);
        return ResponseEntity.ok(result);
    }

    @PostMapping(value = "/export-dates", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> updatePatientExportDates(@RequestBody List<StatementDTO> statementDTOs,
                                                      HttpServletRequest request) throws Exception {
        List<StatementDTO> results = statementService.updatePatientExportDates(statementDTOs, false);
        return ResponseEntity.ok(results);

    }

    @PostMapping(value = "statements/patients/export", produces = "text/plain;charset=UTF-8")
    @ResponseBody
    public ResponseEntity<?> createPatientStatementExportFile(@RequestBody List<StatementDTO> statementDTOS,
                                                              HttpServletRequest request) {
        String result = statementService.writeStatementToXMLFile(statementDTOS);
        return ResponseEntity.ok(result);
    }

    @PostMapping(value = "statements/collections/export", produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public ResponseEntity<?> exportStatementsSentToCollections(@RequestBody List<StatementDTO> statementDTOS, HttpServletRequest request) {
        String result = statementService.exportStatementsSentToCollections(statementDTOS);
        return ResponseEntity.ok(result);
    }

    @PostMapping(value = "/update/physicians", produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public ResponseEntity<?> updatePhysicians(@RequestBody PhysicianBillingEditDTO dto, HttpServletRequest request) {
        Claim result = claimService.updateClaimPhysicianInfo(dto);
        return ResponseEntity.ok(result);
    }

    @GetMapping(value = "profile/{patientId}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> profileLoad(@PathVariable Long patientId,
                                         @RequestParam(name = "getArchived", required = false) boolean getArchived) {
        return ResponseEntity.ok(claimProfileService.load(patientId, getArchived));
    }

    @GetMapping(value = "transaction-history/claim/{claimId}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> loadTransactionHistoryByClaimId(@PathVariable Long claimId) {
        return ResponseEntity.ok(paymentService.loadTransactionHistoryByClaimId(claimId));
    }

    @GetMapping(value = "transaction-history/prescription/{prescriptionId}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> loadTransactionHistoryByPrescriptionId(@PathVariable Long prescriptionId) {
        return ResponseEntity.ok(paymentService.loadTransactionHistoryByPrescriptionId(prescriptionId));
    }

    @GetMapping(value = "projected-vs-billed", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> getProjectedVsBilled(@RequestParam(name = "branchIds") List<Long> branchIds,
                                                  @RequestParam(name = "isPatient", required = true) boolean isPatient,
                                                  @RequestParam(name = "isPrescription", required = true) boolean isPrescription,
                                                  HttpServletRequest request) {
        List<Map<String, String>> results = claimService.getProjectedVsBilled(branchIds, isPatient, isPrescription);
        return ResponseEntity.ok(results);
    }

    @PostMapping(value = "/delete", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> deleteClaim(@RequestBody Claim claim,
                                         HttpServletRequest request) {
        ResponseEntity<?> response = claimService.deleteClaim(claim.getId());
        if (response.getStatusCode().equals(HttpStatus.OK)) claimService.auditDeleteClaim(claim);
        return ResponseEntity.ok(response);
    }

    @GetMapping(value = "/updateAt/totalClaimBalance/totalPtResponsibilityBalanceDto", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> findClaimsWithNoActivityDashboard(@RequestParam(name = "branchId", required = false) Long branchId, HttpServletRequest request) {
        List<ClaimDto> results = claimsDtoService.findClaimsWithNoActivityDashboard(branchId);
        return ResponseEntity.ok(results);
    }

    @GetMapping(value = "/totals/{rxId}/{claimId}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> getClaimTotals(@PathVariable Long rxId,
                                            @PathVariable Long claimId,
                                            HttpServletRequest request) {
        IClaimTotals result = claimService.getClaimTotals(claimId, rxId);
        return ResponseEntity.ok(result);
    }

    @Data
    class ClaimDTO {
        private List<Long> claimIds;
        private Long billingBranchId;
    }
}
