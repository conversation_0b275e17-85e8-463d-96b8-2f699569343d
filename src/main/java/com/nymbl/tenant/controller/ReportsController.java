package com.nymbl.tenant.controller;

import com.nymbl.config.dto.reports.*;
import com.nymbl.master.repository.CompanyRepository;
import com.nymbl.tenant.Timed;
import com.nymbl.tenant.service.ReportService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDate;
import java.time.OffsetDateTime;
import java.util.Date;
import java.util.List;

@RestController
@RequestMapping("/api/report/internal")
public class ReportsController {

    ReportService reportService;

    @Autowired
    CompanyRepository companyRepository;

    @Autowired
    public ReportsController(ReportService reportService) {
        this.reportService = reportService;

    }

    @Timed
    @GetMapping(value = "/sales/summary", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> salesSummary(@RequestParam(name = "branchId", required = false) Long branchId,
                                          @RequestParam(name = "start") Long start,
                                          @RequestParam(name = "end") Long end) {
        RxSalesReport result = reportService.getSalesSummaryReport(branchId, start, end);
        return ResponseEntity.ok(result);
    }

    @Timed
    @GetMapping(value = "/sales/lcode/summary", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> salesByLCodeSummary(@RequestParam(name = "branchId") Long branchId,
                                          @RequestParam(name = "start") Long start,
                                          @RequestParam(name = "end") Long end,
                                                 @RequestParam(name = "lcode", required = false) Long[] lCodeId) {
        RxLCodeSalesReport result = reportService.getSalesByLCodeSummaryReport(branchId, start, end);
        return ResponseEntity.ok(result);
    }

    @Timed
    @GetMapping(value = "/hcpc/detail", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> HcpcDetail(@RequestParam(name = "branchId") Long branchId,
                                                 @RequestParam(name = "start") Long start,
                                                 @RequestParam(name = "end") Long end,
                                                 @RequestParam(name = "lcode", required = false) Long[] lCodeId) {
        HcpcDetailReport result = reportService.getHCPCDetailReport(branchId, start, end);
        return ResponseEntity.ok(result);
    }

    @Timed
    @GetMapping(value = "/daily-close", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> dailyClose(@RequestParam(name = "branchId") Long branchId,
                                        @RequestParam(name = "start") Long start,
                                        @RequestParam(name = "end") Long end,
                                        @RequestParam(name = "isSuperAdmin") Boolean isSuperAdmin) {
    	DailyCloseReport result = reportService.getDailyCloseReport(branchId, start, end, isSuperAdmin != null && isSuperAdmin);
        return ResponseEntity.ok(result);
    }

    @Timed
    @GetMapping(value = "/zero-dollar-payments", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> zeroDollarPayments(@RequestParam(name = "branchId") Long branchId,
                                                @RequestParam(name="startDate") @DateTimeFormat(pattern="yyyy-MM-dd") Date startDate,
                                                @RequestParam(name = "endDate") @DateTimeFormat(pattern = "yyyy-MM-dd") Date endDate) {
        List<IPayments> result = reportService.getZeroDollarPaymentsReport(branchId, startDate, endDate);
        return ResponseEntity.ok(result);
    }

    @Timed
    @GetMapping(value = "/accounts-receivable", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> accountsReceivable(@RequestParam(name = "branchId") Long branchId,
                                                @RequestParam(name = "end") Long end) {
        AccountsReceivableReport result = reportService.getAccountsReceivableReport(branchId, end);
        return ResponseEntity.ok(result);
    }

    @Timed
    @GetMapping(value = "/outstanding-balances", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> outstandingBalance(@RequestParam(name = "branchId") Long branchId,
                                                @RequestParam(name = "end") Long end) {
        OutstandingBalanceReport result = reportService.getOutstandingBalancesReport(branchId, end);
        return ResponseEntity.ok(result);
    }

    @Timed
    @GetMapping(value = "/adjustments", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> adjustments(@RequestParam(name = "branchId") Long branchId,
                                        @RequestParam(name = "start") Long start,
                                        @RequestParam(name = "end") Long end) {
        AdjustmentsReport result = reportService.getAdjustmentsReport(branchId, start, end);
        return ResponseEntity.ok(result);
    }

    @Timed
    @GetMapping(value = "/practitioner-appointments", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> practitionerAppointments(@RequestParam(name = "branchId") Long branchId,
                                                      @RequestParam(name = "userId") Long userId,
                                                      @RequestParam(name = "startDateTime", required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) OffsetDateTime startDateTime,
                                                      @RequestParam(name = "endDateTime", required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) OffsetDateTime endDateTime) {
        List<IPractitionerAppointments> result = reportService.getPractitionerAppointmentReport(branchId, userId, startDateTime, endDateTime);
        return ResponseEntity.ok(result);
    }

    @Timed
    @GetMapping(value = "/rx-delivered-not-billed", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> rxDeliveredNotBilled(@RequestParam(name = "branchId") Long branchId,
                                                  @RequestParam(name = "startDate", required = false) String startDate,
                                                  @RequestParam(name = "endDate", required = false) String endDate) {
        if (startDate == null || startDate.equals("null")) startDate = "1970-01-01";
        if (endDate == null || endDate.equals("null")) endDate = LocalDate.now().toString();
        List<IRxDeliveredNotBilled> result = reportService.getRxDeliveredNotBilled(branchId, startDate, endDate);
        return ResponseEntity.ok(result);
    }

    @Timed
    @GetMapping(value = "/rx-timelines", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> rxTimelines(@RequestParam(name = "branchId") Long branchId,
                                         @RequestParam(name="startDate", required=false) @DateTimeFormat(pattern="yyyy-MM-dd") LocalDate startDate,
                                         @RequestParam(name = "endDate", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate endDate,
                                         @RequestParam(name = "dateOption", required = false) String dateOption) {
        /* MySQL BETWEEN is inclusive but interprets a date as YYYY-MM-DD 00:00:00,
           so to include all of a day in the range we push the end date of the range to midnight of the next day
         */
        LocalDate oneDayAfterEndDate = endDate.plusDays(1);
        List<IRxTimeline> result = reportService.getRxTimelines(branchId, startDate, oneDayAfterEndDate, dateOption);
        return ResponseEntity.ok(result);
    }

    @Timed
    @GetMapping(value = "/rx-status-change-tracking", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> getRxStatusChangeTrackingReport(@RequestParam(name = "branchId") Long branchId,
                                                             @RequestParam(name = "startDate", required = false) String startDate,
                                                             @RequestParam(name = "endDate", required = false) String endDate) {
        if (startDate == null || startDate.equals("null")) startDate = "1970-01-01";
        if (endDate == null || endDate.equals("null")) endDate = LocalDate.now().toString();
        List<IStatusChangeTracking> result = reportService.getRxStatusChangeTrackingReport(branchId, startDate, endDate);
        return ResponseEntity.ok(result);
    }

    @Timed
    @GetMapping(value = "/claim-status-change-tracking", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<List<IStatusChangeTracking>> getClaimStatusChangeTrackingReport(@RequestParam(name = "branchId", required = false) Long branchId,
                                                             @RequestParam(name = "startDate", required = false) String startDate,
                                                             @RequestParam(name = "endDate", required = false) String endDate) {
        if (startDate == null || startDate.equals("null")) startDate = "1970-01-01";
        if (endDate == null || endDate.equals("null")) endDate = LocalDate.now().toString();
        List<IStatusChangeTracking> result = reportService.getClaimStatusChangeTrackingReport(branchId, startDate, endDate);
        return ResponseEntity.ok(result);
    }

    @Timed
    @GetMapping(value = "/nymbl-status-history-change-tracking", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> getNymblStatusHistoryChangeTracking(@RequestParam(name = "branchId", required = false) Long branchId,
                                                                 @RequestParam(name = "startDate", required = false) String startDate,
                                                                 @RequestParam(name = "endDate", required = false) String endDate) {
        if (startDate == null || startDate.equals("null")) startDate = "1970-01-01";
        if (endDate == null || endDate.equals("null")) endDate = LocalDate.now().toString();
        List<IPrescriptionStatusHistoryChangeTracking> result = reportService.getNymblStatusHistoryChangeTracking(branchId, startDate, endDate);
        return ResponseEntity.ok(result);
    }

    @Timed
    @GetMapping(value = "/claim-status-history-change-tracking", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> getClaimStatusHistoryChangeTracking(@RequestParam(name = "branchId", required = false) Long branchId,
                                                                 @RequestParam(name = "startDate", required = false) String startDate,
                                                                 @RequestParam(name = "endDate", required = false) String endDate) {
        if (startDate == null || startDate.equals("null")) startDate = "1970-01-01";
        if (endDate == null || endDate.equals("null")) endDate = LocalDate.now().toString();
        List<IClaimStatusHistoryChangeTracking> result = reportService.getClaimStatusHistoryChangeTracking(branchId, startDate, endDate);
        return ResponseEntity.ok(result);
    }

    @Timed
    @GetMapping(value = "/note-completion-timelines", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> getNoteCompletionTimelinesReport(@RequestParam(name = "branchId") Long branchId,
                                                              @RequestParam(name = "startDateTime", required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) OffsetDateTime startDateTime,
                                                              @RequestParam(name = "endDateTime", required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) OffsetDateTime endDateTime) {
        if (startDateTime == null || startDateTime.equals("null")) startDateTime = OffsetDateTime.MIN;
        if (endDateTime == null || endDateTime.equals("null")) endDateTime = OffsetDateTime.now();
        List<INoteCompletionTimeline> results = reportService.getNoteCompletionTimelinesReport(branchId, startDateTime, endDateTime);
        return ResponseEntity.ok(results);
    }

    @Timed
    @GetMapping(value = "/claims-activity", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> getClaimsActivityReport(@RequestParam(name = "branchId", required = false) Long branchId,
                                                     @RequestParam(name = "startPeriodId") Long startPeriod,
                                                     @RequestParam(name = "endPeriodId") Long endPeriod) {
        List<ClaimsActivity> results = reportService.getClaimsActivityReport(branchId, startPeriod, endPeriod);
        return ResponseEntity.ok(results);
    }

    @Timed
    @GetMapping(value = "/rental-prescription", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> getRentalPrescriptionReport(@RequestParam(name = "branchId", required = false) Long branchId,
                                                         @RequestParam(name = "status", required = false) Long prescriptionStatus,
                                                         @RequestParam(name = "paused", required = false) Boolean isPaused,
                                                         @RequestParam(name = "canceled", required = false) Boolean isCanceled,
                                                         @RequestParam(name = "completed", required = false) Boolean isCompleted) {
        List<RentalPrescriptionRow> results = reportService.getRentalPrescriptionReport(branchId, prescriptionStatus,isPaused, isCanceled,isCompleted);
        return ResponseEntity.ok(results);
    }

}
