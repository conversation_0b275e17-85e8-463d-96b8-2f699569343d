package com.nymbl.config.controller;

import com.nymbl.config.Constants;
import com.nymbl.config.clearingHouse.WaystarAPI;
import com.nymbl.config.service.DataDictionaryService;
import com.nymbl.config.utils.DateUtil;
import com.nymbl.config.x12.x837.Factory837;
import com.nymbl.config.x12.x837.Factory837Parameters;
import com.nymbl.tenant.model.Branch;
import com.nymbl.tenant.model.Claim;
import com.nymbl.tenant.service.ClaimService;
import jakarta.servlet.http.HttpServletRequest;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;
import java.util.Map;

/**
 * Created by Bradley Moore on 03/10/2020.
 */
@RestController
@RequestMapping("/api/download")
public class DownloadController {

    private final ClaimService claimService;
    private final Factory837 factory837;
    private final WaystarAPI clearingHouseAPI;
    private final DataDictionaryService dataDictionaryService;

    @Autowired
    public DownloadController(ClaimService claimService,
                              Factory837 factory837,
                              WaystarAPI clearingHouseAPI,
                              DataDictionaryService dataDictionaryService) {
        this.claimService = claimService;
        this.factory837 = factory837;
        this.clearingHouseAPI = clearingHouseAPI;
        this.dataDictionaryService = dataDictionaryService;
    }

    @GetMapping(value = "/x12/837/claim/{claimId}", produces = MediaType.APPLICATION_OCTET_STREAM_VALUE)
    public ResponseEntity<?> download(@PathVariable Long claimId, HttpServletRequest request) {
        Claim claim = claimService.findOne(claimId);
        String timestamp = DateUtil.getStringDate(new Date(), Constants.DF_YYYYMMDDHHmmssSSS);
        String controlNumber = claim.getId().toString().concat("_").concat(timestamp).concat("_").concat(claim.getPatientInsurance().getInsuranceCompanyId().toString());
        String filename = controlNumber.concat(".CLP");
        Factory837Parameters parameters = factory837.build(claimId, timestamp, null, null, null, null);
        String _837 = parameters.getX12Claim().toX12String();
        if (!parameters.getValidationErrors().isEmpty()) {
            String messages = String.join("<br>", parameters.getValidationErrors());
            return ResponseEntity.badRequest().body(messages);
        } else {
            Branch b = claim.getBillingBranch();
            if (BooleanUtils.toBooleanDefaultIfNull(b.getUseRealTimeRulesEngine(), false) && !BooleanUtils.toBooleanDefaultIfNull(claim.getOverrideRTRulesEngine(), false)) {
                String message = "Claim %s returned status: %s";
                Map<String, String> response = clearingHouseAPI.realTimeRulesEngineRun(b.getOutClearingHouse().getRestUser(), b.getOutClearingHouse().getRestPassword(), _837);
                String error = response.get("Error");
                if (error == null) {
                    String cId = response.get("Claim");
                    String status = response.get("Status");
                    String messages = response.get("ErrorMessages");
                    return ResponseEntity
                            .ok()
                            .contentType(MediaType.parseMediaType(MediaType.TEXT_PLAIN_VALUE))
                            .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + filename + "\"")
                            .header("x-claim", cId)
                            .header("x-status", status)
                            .header("x-messages", messages)
                            .header("x-filename", filename)
                            .body(_837);
                } else {
                    return ResponseEntity.badRequest().body(error);
                }
            } else {
                return ResponseEntity
                        .ok()
                        .contentType(MediaType.parseMediaType(MediaType.TEXT_PLAIN_VALUE))
                        .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + filename + "\"")
                        .header("x-filename", filename)
                        .body(_837);
            }
        }
    }

    @GetMapping(value = "/data-dictionary", produces = MediaType.TEXT_HTML_VALUE)
    public ResponseEntity<?> dataDictionary(HttpServletRequest request) throws Exception {
        String html = dataDictionaryService.generate();
        return ResponseEntity
                .ok()
                .contentType(MediaType.parseMediaType(MediaType.TEXT_HTML_VALUE))
                .body(html);
    }
}
