package com.nymbl.config.service;

import com.google.common.base.Strings;
import com.nymbl.config.Constants;
import com.nymbl.config.clearingHouse.Waystar837P;
import com.nymbl.config.model.FullAddress;
import com.nymbl.config.model.FullName;
import com.nymbl.config.utils.DateUtil;
import com.nymbl.config.utils.StringUtil;
import com.nymbl.config.x12.message.MessageFormat;
import com.nymbl.config.x12.message.segment.*;
import com.nymbl.config.x12.x837.*;
import com.nymbl.master.model.Company;
import com.nymbl.master.service.UserService;
import com.nymbl.tenant.model.*;
import com.nymbl.tenant.service.ClaimService;
import com.nymbl.tenant.service.FeatureFlagService;
import com.nymbl.tenant.service.Form1500TemplateService;
import com.nymbl.tenant.service.SystemSettingService;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.pdmodel.interactive.form.PDAcroForm;
import org.apache.pdfbox.pdmodel.interactive.form.PDField;
import org.apache.pdfbox.pdmodel.interactive.form.PDNonTerminalField;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.math.BigDecimal;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.text.DecimalFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

import static com.nymbl.config.enums.form1500.Signature.LEAVE_BLANK;
import static com.nymbl.config.enums.form1500.Signature.SIGNATURE_ON_FILE;

@Service
public class Print1500Service {

    private final ClaimService claimService;
    private final UserService userService;
    private final Factory837 factory837;
    private final Form1500TemplateService form1500TemplateService;
    private final FeatureFlagService featureFlagService;
    private final SystemSettingService systemSettingService;

    private static final Map<Integer, String> diagnosisPointerMap;

    static {
        diagnosisPointerMap = new HashMap<>();
        diagnosisPointerMap.put(1, "A");
        diagnosisPointerMap.put(2, "B");
        diagnosisPointerMap.put(3, "C");
    }

    @Value("${upload.directory}")
    String uploadDirectory;

    @Autowired
    public Print1500Service(ClaimService claimService,
                            UserService userService,
                            Factory837 factory837,
                            Form1500TemplateService form1500TemplateService,
                            FeatureFlagService featureFlagService, SystemSettingService systemSettingService) {
        this.claimService = claimService;
        this.userService = userService;
        this.factory837 = factory837;
        this.form1500TemplateService = form1500TemplateService;
        this.featureFlagService = featureFlagService;
        this.systemSettingService = systemSettingService;
    }

    public Map<String, List<String>> getAllPrintClaimFiles(
            Long claimId, boolean isFull, Long billingBranchId, Long patientInsuranceId, Long otherPatientInsuranceId, Long form1500TemplateId) throws IOException {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS");
        Claim claim = claimService.findOne(claimId);
        PatientInsurance patientInsurance = claim.getResponsiblePatientInsurance();
        Factory837Parameters parameters = factory837.build(claimId, LocalDateTime.now().format(formatter), billingBranchId, patientInsuranceId, otherPatientInsuranceId, form1500TemplateId);
        X12Claim x12Claim = parameters.getX12Claim();

        // NC-491: claim overrides
        if (form1500TemplateId == null) {
            if (featureFlagService.findFeatureFlagByFeature("claim_level_1500_overrides") != null && claim.getForm1500TemplateId() != null) {
                form1500TemplateId = claim.getForm1500TemplateId();
            } else {
                form1500TemplateId = patientInsurance.getInsuranceCompany().getForm1500TemplateId();
            }
        }
        List<String> files = printX12CMS(x12Claim, isFull, new MessageFormat(), form1500TemplateId, patientInsurance);
        Map<String, List<String>> results = new TreeMap<>(new HashMap<>());
        results.put("files", files);
        results.put("validation_errors", parameters.getValidationErrors());
        return results;
    }

    public List<String> printFromClaimFile(String contents, List<ClaimSubmission> submissions) throws Exception {

        List<String> results;
        if (contents.startsWith("ISA")) {
            results = printX12CMS(contents);
        } else {
            String payerType = "Other";
            if (submissions.size() > 0) {
                payerType = getPayerTypeStr(submissions.get(0).getPatientInsurance().getInsuranceCompany().getPayerType());
            }
            results = printPipeCMS(contents, payerType);
        }
        return results;
    }

    private List<String> printX12CMS(String contents) throws IOException {
        MessageFormat mf = new MessageFormat();
        X12Claim x12Claim = new X12Claim(contents, mf);
        x12Claim.getInterchangeEnvelope().getFunctionalGroups().get(0).getTransactions().get(0).getContent();
        X12ClaimTransaction t = (X12ClaimTransaction) x12Claim.getInterchangeEnvelope().getFunctionalGroups().get(0).getTransactions().get(0);
        String pcn = t.getLoop2000CList().get(0).getLoop2300List().get(0).getClaimInformation().getPatientControlNumber();
        PatientInsurance pi = null;
        Long form1500TemplateId = null;
        String claimId = pcn.split("C")[1];
        if (StringUtils.isNotBlank(claimId)) {
            Claim claim = claimService.findOne(Long.parseLong(claimId));
            pi = claim.getResponsiblePatientInsurance();
            form1500TemplateId = pi.getInsuranceCompany().getForm1500TemplateId();
        }
        return printX12CMS(x12Claim, true, mf, form1500TemplateId, pi);
    }

    private List<String> printX12CMS(X12Claim x12Claim, boolean isFull, MessageFormat mf, Long form1500TemplateId, PatientInsurance patientInsurance) throws IOException {
        List<String> results = new ArrayList<>();
        String showBackOf1500 = systemSettingService.findBySectionAndField("claim", "enable_back_of_1500").getValue();
        Form1500Template form1500Template = null;
        if (form1500TemplateId != null) {
            form1500Template = form1500TemplateService.findOne(form1500TemplateId);
        }
        if (x12Claim != null) {
            String formName = isFull ? "/CMS1500.pdf" : "/CMS1500_blank.pdf";
            if (mf == null) mf = new MessageFormat();
            X12ClaimTransaction x12ClaimTransaction = new X12ClaimTransaction(x12Claim.getInterchangeEnvelope().getFunctionalGroups().get(0).getTransactions().get(0).toX12String(), mf);
            Loop1000A submitter = x12ClaimTransaction.getLoop1000A();
            Loop1000B receiver = x12ClaimTransaction.getLoop1000B();
            BHT bht = x12ClaimTransaction.getBeginningOfHierarchicalTransaction();
            Loop2000A billingHL = x12ClaimTransaction.getLoop2000AList().get(0);
            Loop2000B subscriberHL = x12ClaimTransaction.getLoop2000BList().get(0);
            Loop2000C patientHL = x12ClaimTransaction.getLoop2000CList().get(0);
            Loop2300 claim = patientHL.getLoop2300List().get(0);
            List<Loop2400> lines = claim.getLoop2400List();
            Map<Integer, List<Loop2400>> pageLineMap = getPageLineMap(lines);

            java.io.File pdfForm = new java.io.File(uploadDirectory.concat(formName));
            java.io.File backOfPdf = new java.io.File(uploadDirectory.concat("/CMS1500_back.pdf"));
            Path folderPath;
            String folderDir = "";
            for (int page = 1; page <= pageLineMap.size(); page++) {
                int backPageCount = pageLineMap.size() + 1;
                List<Loop2400> pageLines = pageLineMap.get(page);
                PDDocument pdfDocument = PDDocument.load(pdfForm);
                carrierBlock(pdfDocument, subscriberHL.getLoop2010BB());
                box1(pdfDocument, subscriberHL);
                box2(pdfDocument, subscriberHL.getLoop2010BA(), patientHL.getLoop2010CA());
                Loop2010CA loop2010CA = patientHL.getLoop2010CA();
                DMG demographicInformation;
                if (loop2010CA != null && !loop2010CA.isEmpty()) {
                    demographicInformation = loop2010CA.getPatientDemographicInformation();
                } else {
                    demographicInformation = subscriberHL.getLoop2010BA().getSubscriberDemographicInformation();
                }
                box3(pdfDocument, demographicInformation);
                box4(pdfDocument, subscriberHL.getLoop2010BA());
                box5(pdfDocument, subscriberHL.getLoop2010BA(), patientHL.getLoop2010CA());
                if (!patientHL.getPatientInformation().isEmpty()) {
                    box6(pdfDocument, getPatientRelation(patientHL.getPatientInformation().getIndividualRelationshipCode()));
                } else {
                    box6(pdfDocument, getPatientRelation(subscriberHL.getSubscriberInformation().getIndividualRelationshipCode()));
                }
                box7(pdfDocument, subscriberHL.getLoop2010BA(), patientInsurance);
                if (claim.getLoop2320List().size() > 0) {
                    box9(pdfDocument, claim.getLoop2320List().get(0));
                }
                box10(pdfDocument, claim);
                box11(pdfDocument, subscriberHL, claim);
                box12(pdfDocument, claim, form1500Template);
                box13(pdfDocument, form1500Template);
                box14(pdfDocument, claim, form1500Template);
                box15(pdfDocument, claim, form1500Template);
                box16(pdfDocument, claim);
                box17(pdfDocument, claim, form1500Template, patientInsurance.getInsuranceCompany());
                box18(pdfDocument, claim);
                box19(pdfDocument, claim);
                box20(pdfDocument, claim);
                box21(pdfDocument, claim);
                box22(pdfDocument, claim);
                box23(pdfDocument, claim);
                BigDecimal totalCharges = box24(pdfDocument, pageLines, claim, form1500Template);
                box25(pdfDocument, billingHL.getLoop2010AA());
                box26(pdfDocument, claim);
                box27(pdfDocument, claim, form1500Template);
                box28(pdfDocument, totalCharges);
                box29(pdfDocument, claim, form1500Template);
                box31(pdfDocument, claim, bht.getDate());
                box32(pdfDocument, claim);
                box33(pdfDocument, billingHL, submitter.getSubmitterEDIContactInformation().getCommunicationNumber1(), form1500Template);

                Company currentCompany = userService.getCurrentCompany();
                folderDir = uploadDirectory.concat("/company/").concat(currentCompany.getId().toString()).concat("/forms/");
                folderPath = Paths.get(folderDir);
                if (!Files.exists(folderPath)) {
                    Files.createDirectories(folderPath);
                }
                String filePath = folderDir.concat("filled_").concat(claim.getClaimInformation().getPatientControlNumber()).concat("_").concat(String.valueOf(page)).concat(".pdf");
                pdfDocument.save(filePath);
                results.add(filePath);
                pdfDocument.close();
                if("Y".equals(showBackOf1500) && isFull) {
                    String backOf1500FilePath;
                    try (PDDocument backOfPdfDocument = PDDocument.load(backOfPdf)) {
                        backOf1500FilePath = folderDir.concat("filled_").concat(claim.getClaimInformation().getPatientControlNumber()).concat("_").concat(String.valueOf(++backPageCount)).concat(".pdf");
                        backOfPdfDocument.save(backOf1500FilePath);
                    }
                    results.add(backOf1500FilePath);
                }
            }
        }
        return results;
    }

    private List<String> printPipeCMS(String contents, String payerType) throws IOException {
        List<String> results = printRequests("/CMS1500.pdf", "filled_", payerType, userService.getCurrentCompany(), contents.split("\n"));
        return results;
    }

    private Map<Integer, List<Loop2400>> getPageLineMap(List<Loop2400> lines) {
        Map<Integer, List<Loop2400>> pageLineMap = new HashMap<>();
        for (int l = 0; l < lines.size(); l++) {
            Loop2400 line = lines.get(l);
            int currentPage = (int) Math.ceil((double) (l + 1) / 6); //break pages at 6 lines
            List<Loop2400> list = pageLineMap.get(currentPage);
            if (list == null) {
                list = new ArrayList<>();
            }
            list.add(line);
            pageLineMap.put(currentPage, list);
        }
        return pageLineMap;
    }

    private void carrierBlock(PDDocument pdfDocument, String[] entry) throws IOException {
        setFieldValue(pdfDocument, "insurance_name", entry[Waystar837P.PayerName.ordinal()]);
        setFieldValue(pdfDocument, "insurance_address", entry[Waystar837P.PayerAdd1.ordinal()]);
        FullAddress payerAddress = new FullAddress(entry[Waystar837P.PayerCity.ordinal()], entry[Waystar837P.PayerState.ordinal()], entry[Waystar837P.PayerZip.ordinal()]);
        setFieldValue(pdfDocument, "insurance_city_state_zip", StringUtil.formatAddress(payerAddress));
    }

    private void carrierBlock(PDDocument pdfDocument, Loop2010BB insurance) throws IOException {
        setFieldValue(pdfDocument, "insurance_name", insurance.getPayerName().getNameLastOrOrganizationName());
        setFieldValue(pdfDocument, "insurance_address", insurance.getPayerAddress().getAddressInformation1());
        FullAddress payerAddress = new FullAddress(insurance.getPayerCityStateZip().getCityName(), insurance.getPayerCityStateZip().getStateOrProvinceCode(), insurance.getPayerCityStateZip().getPostalCode());
        setFieldValue(pdfDocument, "insurance_city_state_zip", StringUtil.formatAddress(payerAddress));
    }

    private void box1(PDDocument pdfDocument, String[] entry, String payerType) throws IOException {
        setFieldValue(pdfDocument, "medicare", payerType.equals("Medicare") ? "Medicare" : Constants.OFF);
        setFieldValue(pdfDocument, "medicaid", payerType.equals("Medicaid") ? "Medicaid" : Constants.OFF);
        setFieldValue(pdfDocument, "tricare", payerType.equals("Tricare") ? "Tricare" : Constants.OFF);
        setFieldValue(pdfDocument, "champva", payerType.equals("Champva") ? "Champva" : Constants.OFF);
        setFieldValue(pdfDocument, "group", payerType.equals("Group") ? "Group" : Constants.OFF);
        setFieldValue(pdfDocument, "feca", payerType.equals("Feca") ? "Feca" : Constants.OFF);
        setFieldValue(pdfDocument, "other_1", payerType.equals("Other") ? "Other" : Constants.OFF);
        //Box 1a Insured's ID Number/ Policy//
//        if (isPrimary) {
//            setFieldValue(pdfDocument, "insured_id_number", "01".equals(rel_to_ins_1a) ? entry[Zirmed837P.SbrID.ordinal()] : entry[Zirmed837P.SbrID.ordinal()].concat(" ").concat(rel_to_ins_1a));
//        } else {
//            setFieldValue(pdfDocument, "insured_id_number", "01".equals(rel_to_ins_1a) ? entry[Zirmed837P.OtherSbrPolicyGroup.ordinal()] : entry[Zirmed837P.OtherSbrPolicyGroup.ordinal()].concat(" ").concat(rel_to_ins_1a));
//        }
        // 2836: do not put relationship to subscriber on paper
        // setFieldValue(pdfDocument, "insured_id_number", "01".equals(rel_to_ins_1a) ? entry[Zirmed837P.SbrID.ordinal()] : entry[Zirmed837P.SbrID.ordinal()].concat(" ").concat(rel_to_ins_1a));
        setFieldValue(pdfDocument, "insured_id_number", entry[Waystar837P.SbrID.ordinal()]);
    }

    private void box1(PDDocument pdfDocument, Loop2000B subscriber) throws IOException {

        SBR sbr = subscriber.getSubscriberInformation();
        switch (sbr.getClaimFilingIndicatorCode()) {
            case "MA":
                setFieldValue(pdfDocument, "medicare", "Medicare");
                break;
            case "MC":
                setFieldValue(pdfDocument, "medicaid", "Medicaid");
                break;
            case "CH":
                setFieldValue(pdfDocument, "tricare", "Tricare");
                break;
            case "VA":
                setFieldValue(pdfDocument, "champva", "Champva");
                break;
            case "CI":
                setFieldValue(pdfDocument, "group", "Group");
                break;
            case "WC":
                setFieldValue(pdfDocument, "feca", "Feca");
                break;
            default:
                setFieldValue(pdfDocument, "other_1", "Other");
                break;
        }
        setFieldValue(pdfDocument, "insured_id_number", subscriber.getLoop2010BA().getSubscriberName().getIdentificationCode());
    }

    private void box2(PDDocument pdfDocument, String[] entry) throws IOException {
        FullName patient_name = new FullName(entry[Waystar837P.PatFName.ordinal()], entry[Waystar837P.PatMName.ordinal()], entry[Waystar837P.PatLName.ordinal()]);
        setFieldValue(pdfDocument, "patient_name", StringUtil.formatName(patient_name, "LFMi", true));
    }

    private void box2(PDDocument pdfDocument, Loop2010BA subscriber, Loop2010CA patient) throws IOException {
        NM1 nm1 = new NM1();
        if (!patient.isEmpty()) {
            nm1 = patient.getPatientName();
        } else if (!subscriber.isEmpty()) {
            nm1 = subscriber.getSubscriberName();
        }
        FullName patient_name = new FullName(nm1.getNameFirst(), nm1.getNameMiddle(), nm1.getNameLastOrOrganizationName());
        setFieldValue(pdfDocument, "patient_name", StringUtil.formatName(patient_name, "LFMi", true));
    }

    private void box3(PDDocument pdfDocument, String[] entry) throws IOException {
        Date dob = DateUtil.getDate(entry[Waystar837P.PatBirthdate.ordinal()], Constants.WAYSTAR_DATE_FORMAT);
        setFieldValue(pdfDocument, "patient_dob_mm", DateUtil.getStringDate(dob, "MM"));
        setFieldValue(pdfDocument, "patient_dob_dd", DateUtil.getStringDate(dob, "dd"));
        setFieldValue(pdfDocument, "patient_dob_yy", DateUtil.getStringDate(dob, "yy"));

        String gender = entry[Waystar837P.PatSex.ordinal()];
        setFieldValue(pdfDocument, "patient_sex_male", gender.equals("M") ? "Male" : Constants.OFF);
        setFieldValue(pdfDocument, "patient_sex_female", gender.equals("F") ? "Female" : Constants.OFF);

        // SCRUM-3553 ********* There is NO OTHER/NON_BINARY **********
        //setFieldValue(pdfDocument, "patient_sex_other", gender.equals("O") ? "Other" : OFF);
    }

    private void box3(PDDocument pdfDocument, DMG demographicInformation) throws IOException {
        String gender = demographicInformation.getGenderCode();
        Date dob = DateUtil.getDate(demographicInformation.getDateTimePeriod(), Constants.DF_YYYYMMDD);

        setFieldValue(pdfDocument, "patient_dob_mm", DateUtil.getStringDate(dob, "MM"));
        setFieldValue(pdfDocument, "patient_dob_dd", DateUtil.getStringDate(dob, "dd"));
        setFieldValue(pdfDocument, "patient_dob_yy", DateUtil.getStringDate(dob, "yy"));
        setFieldValue(pdfDocument, "patient_sex_male", gender.equals("M") ? "Male" : Constants.OFF);
        setFieldValue(pdfDocument, "patient_sex_female", gender.equals("F") ? "Female" : Constants.OFF);

    }

    private void box4(PDDocument pdfDocument, String[] entry) throws IOException {
        FullName name = new FullName(entry[Waystar837P.SbrFName.ordinal()], entry[Waystar837P.SbrMName.ordinal()], entry[Waystar837P.SbrLName.ordinal()]);
        setFieldValue(pdfDocument, "insured_name", StringUtil.formatName(name, "LFMi", true));
    }

    private void box4(PDDocument pdfDocument, Loop2010BA subscriber) throws IOException {
        NM1 nm1 = subscriber.getSubscriberName();
        FullName name = new FullName(nm1.getNameFirst(), nm1.getNameMiddle(), nm1.getNameLastOrOrganizationName());
        setFieldValue(pdfDocument, "insured_name", StringUtil.formatName(name, "LFMi", true));
    }

    private void box5(PDDocument pdfDocument, String[] entry) throws IOException {
        setFieldValue(pdfDocument, "patient_address", entry[Waystar837P.PatAddr.ordinal()]);
        setFieldValue(pdfDocument, "patient_city", entry[Waystar837P.PatCity.ordinal()]);
        setFieldValue(pdfDocument, "patient_state", entry[Waystar837P.PatState.ordinal()]);
        setFieldValue(pdfDocument, "patient_zip", entry[Waystar837P.PatZip.ordinal()]);

        String patientPhone = entry[Waystar837P.PatPhone.ordinal()];
        if (!Strings.isNullOrEmpty(patientPhone)) {
            setFieldValue(pdfDocument, "patient_area_code", patientPhone.substring(0, 3));
            setFieldValue(pdfDocument, "patient_phone", StringUtil.format1500Phone(patientPhone.substring(3)));
        }
    }

    private void box5(PDDocument pdfDocument, Loop2010BA subscriber, Loop2010CA patient) throws IOException {
        N3 n3 = new N3();
        N4 n4 = new N4();

        if (!patient.isEmpty()) {
            n3 = patient.getPatientAddress();
            n4 = patient.getPatientCityStateZip();
        } else if (!subscriber.isEmpty()) {
            n3 = subscriber.getSubscriberAddress();
            n4 = subscriber.getSubscriberCityStateZip();
        }
        setFieldValue(pdfDocument, "patient_address", n3.getAddressInformation1());
        setFieldValue(pdfDocument, "patient_city", n4.getCityName());
        setFieldValue(pdfDocument, "patient_state", n4.getStateOrProvinceCode());
        setFieldValue(pdfDocument, "patient_zip", n4.getPostalCode());

        String patientPhone = ""; //entry[Zirmed837P.PatPhone.ordinal()];
        if (!Strings.isNullOrEmpty(patientPhone)) {
            setFieldValue(pdfDocument, "patient_area_code", patientPhone.substring(0, 3));
            setFieldValue(pdfDocument, "patient_phone", StringUtil.format1500Phone(patientPhone.substring(3)));
        }
    }

    private void box6(PDDocument pdfDocument, String rel_to_ins) throws IOException {
        setFieldValue(pdfDocument, "patient_relation_self", rel_to_ins.equals("S") ? Constants.YES : Constants.OFF);
        setFieldValue(pdfDocument, "patient_relation_spouse", rel_to_ins.equals("M") ? Constants.YES : Constants.OFF);
        setFieldValue(pdfDocument, "patient_relation_child", rel_to_ins.equals("C") ? Constants.YES : Constants.OFF);
        setFieldValue(pdfDocument, "patient_relation_other", rel_to_ins.equals("O") ? Constants.YES : Constants.OFF);
    }

    private void box7(PDDocument pdfDocument, String[] entry) throws IOException {
        setFieldValue(pdfDocument, "insured_street", entry[Waystar837P.SbrAddr.ordinal()]);
        setFieldValue(pdfDocument, "insured_city", entry[Waystar837P.SbrCity.ordinal()]);
        setFieldValue(pdfDocument, "insured_state", entry[Waystar837P.SbrState.ordinal()]);
        setFieldValue(pdfDocument, "insured_zip", entry[Waystar837P.SbrZip.ordinal()]);
        String insuredPhone = entry[Waystar837P.SbrPhone.ordinal()];
        if (!StringUtil.isBlank(insuredPhone)) {
            setFieldValue(pdfDocument, "insured_area_code", insuredPhone.substring(0, 3));
            setFieldValue(pdfDocument, "insured_phone", StringUtil.format1500Phone(insuredPhone.substring(3)));
        }
    }

    private void box7(PDDocument pdfDocument, Loop2010BA subscriberName, PatientInsurance patientInsurance) throws IOException {
        setFieldValue(pdfDocument, "insured_street", subscriberName.getSubscriberAddress().getAddressInformation1());
        setFieldValue(pdfDocument, "insured_city", subscriberName.getSubscriberCityStateZip().getCityName());
        setFieldValue(pdfDocument, "insured_state", subscriberName.getSubscriberCityStateZip().getStateOrProvinceCode());
        setFieldValue(pdfDocument, "insured_zip", subscriberName.getSubscriberCityStateZip().getPostalCode());
        if (StringUtils.isNotBlank(patientInsurance.getPhoneNumber())) {
            setFieldValue(pdfDocument, "insured_area_code", patientInsurance.getPhoneNumber().substring(0, 3));
            setFieldValue(pdfDocument, "insured_phone", StringUtil.format1500Phone(patientInsurance.getPhoneNumber().substring(3)));
        }
    }

    private void box9(PDDocument pdfDocument, String[] entry) throws IOException {
        FullName otherInsuredName = new FullName(entry[Waystar837P.OtherSbrFName.ordinal()],
                entry[Waystar837P.OtherSbrMName.ordinal()],
                entry[Waystar837P.OtherSbrLName.ordinal()]);
        setFieldValue(pdfDocument, "secondary_insured_name", entry[Waystar837P.OtherSbrFName.ordinal()].equals("") ? "" : StringUtil.formatName(otherInsuredName, "LFM", false));
        setFieldValue(pdfDocument, "secondary_insured_group_number", entry[Waystar837P.OtherSbrPolicyGroup.ordinal()]);
        setFieldValue(pdfDocument, "secondary_insured_plan_name", entry[Waystar837P.OtherInsPlanProgramName.ordinal()]);
    }

    private void box9(PDDocument pdfDocument, Loop2320 otherSubscriber) throws IOException {
        NM1 sub = otherSubscriber.getLoop2330A().getOtherSubscriberName();
        NM1 pay = otherSubscriber.getLoop2330B().getOtherPayerName();
        SBR sbr = otherSubscriber.getOtherSubscriberInformation();

        FullName otherInsuredName = new FullName(sub.getNameFirst(), sub.getNameMiddle(), sub.getNameLastOrOrganizationName());
        setFieldValue(pdfDocument, "secondary_insured_name", sub.getNameFirst().equals("") ? "" : StringUtil.formatName(otherInsuredName, "LFM", false));
        setFieldValue(pdfDocument, "secondary_insured_group_number", sub.getIdentificationCode());
        setFieldValue(pdfDocument, "secondary_insured_plan_name", pay.getNameLastOrOrganizationName());
    }

    private void box10(PDDocument pdfDocument, String[] entry) throws IOException {
        //Box 10  Condition Caused By//
        boolean causedByEmployment = "Y".equals(entry[Waystar837P.EmpRelatedYes.ordinal()]);
        setFieldValue(pdfDocument, "caused_by_employment_yes", causedByEmployment ? Constants.YES : Constants.OFF);
        setFieldValue(pdfDocument, "caused_by_employment_no", !causedByEmployment ? Constants.YES : Constants.OFF);

        boolean causedByAuto = "Y".equals(entry[Waystar837P.AutoAccidYes.ordinal()]);
        setFieldValue(pdfDocument, "caused_by_auto_yes", causedByAuto ? Constants.YES : Constants.OFF);
        setFieldValue(pdfDocument, "caused_by_auto_no", !causedByAuto ? Constants.YES : Constants.OFF);
        setFieldValue(pdfDocument, "caused_by_auto_state", causedByAuto ? entry[Waystar837P.AutoAccidState.ordinal()] : "");

        boolean causedByOther = "Y".equals(entry[Waystar837P.OtherAccidYes.ordinal()]);
        setFieldValue(pdfDocument, "caused_by_other_yes", causedByOther ? Constants.YES : Constants.OFF);
        setFieldValue(pdfDocument, "caused_by_other_no", !causedByOther ? Constants.YES : Constants.OFF);

        //Box 10d Other Claim Id//
        setFieldValue(pdfDocument, "claim_code", entry[Waystar837P.ClaimCodes.ordinal()]);
    }

    private void box10(PDDocument pdfDocument, Loop2300 claim) throws IOException {
        //Box 10  Condition Caused By//
        CLM clm = claim.getClaimInformation();
        String causes = clm.getRelatedCausesInformation();
        boolean causedByEmployment = false;
        boolean causedByAuto = false;
        boolean causedByOther = false;
        String autoState = "";
        if (!StringUtil.isBlank(causes)) {
            String[] t = causes.split(":");
            if ("AA".equals(t[0])) {
                causedByAuto = true;
                if (t.length == 4) {
                    autoState = t[3];
                }
            } else if ("EM".equals(t[0])) {
                causedByEmployment = true;
            } else if ("OA".equals(t[0])) {
                causedByOther = true;
            }
        }
        setFieldValue(pdfDocument, "caused_by_employment_yes", causedByEmployment ? Constants.YES : Constants.OFF);
        setFieldValue(pdfDocument, "caused_by_employment_no", !causedByEmployment ? Constants.YES : Constants.OFF);

        setFieldValue(pdfDocument, "caused_by_auto_yes", causedByAuto ? Constants.YES : Constants.OFF);
        setFieldValue(pdfDocument, "caused_by_auto_no", !causedByAuto ? Constants.YES : Constants.OFF);
        setFieldValue(pdfDocument, "caused_by_auto_state", autoState);

        setFieldValue(pdfDocument, "caused_by_other_yes", causedByOther ? Constants.YES : Constants.OFF);
        setFieldValue(pdfDocument, "caused_by_other_no", !causedByOther ? Constants.YES : Constants.OFF);

        setFieldValue(pdfDocument, "claim_code", "");
    }

    private void box11(PDDocument pdfDocument, String[] entry) throws IOException {
        //Box 11 Insured Group Or FECA Number//
        setFieldValue(pdfDocument, "insured_group_feca", entry[Waystar837P.SbrPolicyGroup.ordinal()]);

        //Box 11a Insured DOB and Gender//
        Date insuredDOB = DateUtil.getDate(entry[Waystar837P.SbrBirthdate.ordinal()], Constants.WAYSTAR_DATE_FORMAT);
        setFieldValue(pdfDocument, "insured_dob_mm", DateUtil.getStringDate(insuredDOB, "MM"));
        setFieldValue(pdfDocument, "insured_dob_dd", DateUtil.getStringDate(insuredDOB, "dd"));
        setFieldValue(pdfDocument, "insured_dob_yy", DateUtil.getStringDate(insuredDOB, "yy"));

        boolean isMale = "M".equals(entry[Waystar837P.SbrSex.ordinal()]);
        boolean isFemale = "F".equals(entry[Waystar837P.SbrSex.ordinal()]);
        setFieldValue(pdfDocument, "insured_male", isMale ? Constants.YES : Constants.OFF);
        setFieldValue(pdfDocument, "insured_female", isFemale ? Constants.YES : Constants.OFF);
        // SCRUM-3553 ********* There is NO OTHER/NON_BINARY **********

        //Box 11b Other Claim Id//
        setFieldValue(pdfDocument, "other_claim_id_prefix", entry[Waystar837P.OtherClaimIDQual.ordinal()]);
        setFieldValue(pdfDocument, "other_claim_id", entry[Waystar837P.OtherClaimID.ordinal()]);

        //Box 11c Insurance Plan Name//
        setFieldValue(pdfDocument, "insurance_plan_name", entry[Waystar837P.InsGroupName.ordinal()]);

        //Box 11d Other Insurance Available//
        boolean hasAnotherPlan = !Strings.isNullOrEmpty(entry[Waystar837P.OtherInsPlanProgramName.ordinal()]);
        setFieldValue(pdfDocument, "another_plan_yes", hasAnotherPlan ? Constants.YES : Constants.OFF);
        setFieldValue(pdfDocument, "another_plan_no", !hasAnotherPlan ? Constants.YES : Constants.OFF);
    }

    private void box11(PDDocument pdfDocument, Loop2000B subscriber, Loop2300 claim) throws IOException {

        SBR sbr = subscriber.getSubscriberInformation();
        Loop2010BA subscriberName = subscriber.getLoop2010BA();
        setFieldValue(pdfDocument, "insured_group_feca",
                Strings.isNullOrEmpty(sbr.getSubscriberGroupOrPolicyNumber())
                        ? "NONE"
                        : sbr.getSubscriberGroupOrPolicyNumber());

        DMG dmg = subscriberName.getSubscriberDemographicInformation();
        if (!dmg.isEmpty()) {
            Date insuredDOB = DateUtil.getDate(dmg.getDateTimePeriod(), Constants.DF_YYYYMMDD);
            setFieldValue(pdfDocument, "insured_dob_mm", DateUtil.getStringDate(insuredDOB, "MM"));
            setFieldValue(pdfDocument, "insured_dob_dd", DateUtil.getStringDate(insuredDOB, "dd"));
            setFieldValue(pdfDocument, "insured_dob_yy", DateUtil.getStringDate(insuredDOB, "yy"));

            String gender = dmg.getGenderCode();
            boolean isMale = "M".equals(gender);
            boolean isFemale = "F".equals(gender);
            setFieldValue(pdfDocument, "insured_male", isMale ? Constants.YES : Constants.OFF);
            setFieldValue(pdfDocument, "insured_female", isFemale ? Constants.YES : Constants.OFF);
        }

        setFieldValue(pdfDocument, "other_claim_id_prefix", "");
        setFieldValue(pdfDocument, "other_claim_id", "");

        setFieldValue(pdfDocument, "insurance_plan_name", sbr.getSubscriberGroupName());

        boolean hasAnotherPlan = claim.getLoop2320List().size() > 0;
        setFieldValue(pdfDocument, "another_plan_yes", hasAnotherPlan ? Constants.YES : Constants.OFF);
        setFieldValue(pdfDocument, "another_plan_no", !hasAnotherPlan ? Constants.YES : Constants.OFF);
    }

    private void box12(PDDocument pdfDocument, String[] entry) throws IOException {
        setFieldValue(pdfDocument, "patient_signature", "Signature on File");
        setFieldValue(pdfDocument, "patient_signature_date", entry[Waystar837P.PatSignatureDate.ordinal()]);
    }

    private void box12(PDDocument pdfDocument, Loop2300 claim, Form1500Template form1500Template)
            throws IOException {
        Loop2400 line1 = claim.getLoop2400List().get(0);
        String[] dateRange = line1.getServiceDate().getDateTimePeriod().split("-");
        String date =
                DateUtil.getStringDate(
                        DateUtil.getDate(dateRange[0], Constants.DF_YYYYMMDD), Constants.WAYSTAR_DATE_FORMAT);
        if (form1500Template != null) {
            if (!LEAVE_BLANK.equals(form1500Template.getBox12PatientSignature())) {
                setFieldValue(
                        pdfDocument,
                        "patient_signature",
                        form1500Template.getBox12PatientSignature().toString());
            }
        } else {
            setFieldValue(pdfDocument, "patient_signature", SIGNATURE_ON_FILE.toString());
        }
        setFieldValue(pdfDocument, "patient_signature_date", date);
    }

    private void box13(PDDocument pdfDocument) throws IOException {
        box13(pdfDocument, null);
    }

    private void box13(PDDocument pdfDocument, Form1500Template form1500Template) throws IOException {
        if (form1500Template != null) {
            if (!LEAVE_BLANK.equals(form1500Template.getBox13InsuredSignature())) {
                setFieldValue(
                        pdfDocument,
                        "insured_signature",
                        form1500Template.getBox13InsuredSignature().toString());
            } else {
                setFieldValue(
                        pdfDocument,
                        "insured_signature",
                        "");
            }
        } else {
            setFieldValue(pdfDocument, "insured_signature", SIGNATURE_ON_FILE.toString());
        }
    }

    private void box14(PDDocument pdfDocument, String[] entry) throws IOException {
        Date illnessDate = DateUtil.getDate(entry[Waystar837P.IllnessDate.ordinal()], Constants.WAYSTAR_DATE_FORMAT);
        setFieldValue(pdfDocument, "injury_date_mm", DateUtil.getStringDate(illnessDate, "MM"));
        setFieldValue(pdfDocument, "injury_date_dd", DateUtil.getStringDate(illnessDate, "dd"));
        setFieldValue(pdfDocument, "injury_date_yy", DateUtil.getStringDate(illnessDate, "yy"));
        setFieldValue(pdfDocument, "injury_qualifier", entry[Waystar837P.IllnessDateQual.ordinal()]);
    }

    private void box14(PDDocument pdfDocument, Loop2300 claim, Form1500Template form1500Template) throws IOException {
        if (form1500Template != null && form1500Template.getBox14And15ShowAccidentDate() != null) {
            switch (form1500Template.getBox14And15ShowAccidentDate()) {
                case BOTH_14_AND_15:
                case BOX_14:
                    DTP dtp = claim.getAccidentDate();

                    Date illnessDate = DateUtil.getDate(dtp.getDateTimePeriod(), Constants.DF_YYYYMMDD);
                    setFieldValue(pdfDocument, "injury_date_mm", DateUtil.getStringDate(illnessDate, "MM"));
                    setFieldValue(pdfDocument, "injury_date_dd", DateUtil.getStringDate(illnessDate, "dd"));
                    setFieldValue(pdfDocument, "injury_date_yy", DateUtil.getStringDate(illnessDate, "yy"));
                    setFieldValue(pdfDocument, "injury_qualifier", dtp.getDateTimeQualifier());
                    break;
            }
        } else {
            DTP dtp = claim.getAccidentDate();

            Date illnessDate = DateUtil.getDate(dtp.getDateTimePeriod(), Constants.DF_YYYYMMDD);
            setFieldValue(pdfDocument, "injury_date_mm", DateUtil.getStringDate(illnessDate, "MM"));
            setFieldValue(pdfDocument, "injury_date_dd", DateUtil.getStringDate(illnessDate, "dd"));
            setFieldValue(pdfDocument, "injury_date_yy", DateUtil.getStringDate(illnessDate, "yy"));
            setFieldValue(pdfDocument, "injury_qualifier", dtp.getDateTimeQualifier());
        }
    }

    private void box15(PDDocument pdfDocument, String[] entry) throws IOException {
        setFieldValue(pdfDocument, "other_date_qualifier", entry[Waystar837P.OtherDateQual.ordinal()]);
        Date otherDate = DateUtil.getDate(entry[Waystar837P.OtherDate.ordinal()], Constants.WAYSTAR_DATE_FORMAT);
        setFieldValue(pdfDocument, "other_date_mm", DateUtil.getStringDate(otherDate, "MM"));
        setFieldValue(pdfDocument, "other_date_dd", DateUtil.getStringDate(otherDate, "dd"));
        setFieldValue(pdfDocument, "other_date_yy", DateUtil.getStringDate(otherDate, "yy"));
    }

    private void box15(PDDocument pdfDocument, Loop2300 claim, Form1500Template form1500Template) throws IOException {
        DTP dtp = new DTP();
        if (form1500Template != null && form1500Template.getBox14And15ShowAccidentDate() != null) {
            switch (form1500Template.getBox14And15ShowAccidentDate()) {
                case BOTH_14_AND_15:
                case BOX_15:
                    dtp = claim.getAccidentDate();
                    break;
                default:
                    String relatedCauses = claim.getClaimInformation().getRelatedCausesInformation();
                    if (!StringUtil.isBlank(relatedCauses)) {
                        if (!claim.getInitialTreatmentDate().isEmpty()) {
                            dtp = claim.getInitialTreatmentDate();
                        } else if (!claim.getLastSeenDate().isEmpty()) {
                            dtp = claim.getLastSeenDate();
                        } else if (!claim.getAcuteManifestationDate().isEmpty()) {
                            dtp = claim.getAcuteManifestationDate();
                        } else if (!claim.getLastXRayDate().isEmpty()) {
                            dtp = claim.getLastXRayDate();
                        } else if (!claim.getHearingVisionRxDate().isEmpty()) {
                            dtp = claim.getHearingVisionRxDate();
                        } else if (!claim.getAssumedCareDate().isEmpty()) {
                            dtp = claim.getAssumedCareDate();
                        } else if (!claim.getPropertyCasualtyFirstContactDate().isEmpty()) {
                            dtp = claim.getPropertyCasualtyFirstContactDate();
                        }
                    }
                    break;
            }
        } else {
            String relatedCauses = claim.getClaimInformation().getRelatedCausesInformation();
            if (!StringUtil.isBlank(relatedCauses)) {
                if (!claim.getInitialTreatmentDate().isEmpty()) {
                    dtp = claim.getInitialTreatmentDate();
                } else if (!claim.getLastSeenDate().isEmpty()) {
                    dtp = claim.getLastSeenDate();
                } else if (!claim.getAcuteManifestationDate().isEmpty()) {
                    dtp = claim.getAcuteManifestationDate();
                } else if (!claim.getAccidentDate().isEmpty()) {
                    dtp = claim.getAccidentDate();
                } else if (!claim.getLastXRayDate().isEmpty()) {
                    dtp = claim.getLastXRayDate();
                } else if (!claim.getHearingVisionRxDate().isEmpty()) {
                    dtp = claim.getHearingVisionRxDate();
                } else if (!claim.getAssumedCareDate().isEmpty()) {
                    dtp = claim.getAssumedCareDate();
                } else if (!claim.getPropertyCasualtyFirstContactDate().isEmpty()) {
                    dtp = claim.getPropertyCasualtyFirstContactDate();
                }
            }
        }

        if (!dtp.isEmpty()) {
            setFieldValue(pdfDocument, "other_date_qualifier", dtp.getDateTimeQualifier());
            Date otherDate = DateUtil.getDate(dtp.getDateTimePeriod(), Constants.DF_YYYYMMDD);
            setFieldValue(pdfDocument, "other_date_mm", DateUtil.getStringDate(otherDate, "MM"));
            setFieldValue(pdfDocument, "other_date_dd", DateUtil.getStringDate(otherDate, "dd"));
            setFieldValue(pdfDocument, "other_date_yy", DateUtil.getStringDate(otherDate, "yy"));
        }
    }

    private void box16(PDDocument pdfDocument, String[] entry) throws IOException {
        Date workFrom = DateUtil.getDate(entry[Waystar837P.UnableToWorkFrom.ordinal()], Constants.WAYSTAR_DATE_FORMAT);
        setFieldValue(pdfDocument, "work_from_mm", DateUtil.getStringDate(workFrom, "MM"));
        setFieldValue(pdfDocument, "work_from_dd", DateUtil.getStringDate(workFrom, "dd"));
        setFieldValue(pdfDocument, "work_from_yy", DateUtil.getStringDate(workFrom, "yy"));

        Date workTo = DateUtil.getDate(entry[Waystar837P.UnableToWorkTo.ordinal()], Constants.WAYSTAR_DATE_FORMAT);
        setFieldValue(pdfDocument, "work_to_mm", DateUtil.getStringDate(workTo, "MM"));
        setFieldValue(pdfDocument, "work_to_dd", DateUtil.getStringDate(workTo, "dd"));
        setFieldValue(pdfDocument, "work_to_yy", DateUtil.getStringDate(workTo, "yy"));
    }

    private void box16(PDDocument pdfDocument, Loop2300 claim) throws IOException {
        DTP disabilityDates = claim.getDisabilityDates();
        DTP returnToWorkDate = claim.getAuthorizedReturnToWorkDate();
        Date workFrom = null;
        Date workTo = null;
        if (!disabilityDates.isEmpty()) {
            String date = disabilityDates.getDateTimePeriod();
            if ("314".equals(disabilityDates.getDateTimeQualifier())) {
                String[] dateRange = date.split("-");
                workFrom = DateUtil.getDate(dateRange[0], Constants.DF_YYYYMMDD);
                workTo = DateUtil.getDate(dateRange[1], Constants.DF_YYYYMMDD);
            } else if ("360".equals(disabilityDates.getDateTimeQualifier())) {
                workFrom = DateUtil.getDate(date, Constants.DF_YYYYMMDD);
            } else if ("361".equals(disabilityDates.getDateTimeQualifier())) {
                workTo = DateUtil.getDate(date, Constants.DF_YYYYMMDD);
            }
        }
        if (!returnToWorkDate.isEmpty()) {
            workTo = DateUtil.getDate(returnToWorkDate.getDateTimePeriod(), Constants.DF_YYYYMMDD);
        }
        if (workFrom != null) {
            setFieldValue(pdfDocument, "work_from_mm", DateUtil.getStringDate(workFrom, "MM"));
            setFieldValue(pdfDocument, "work_from_dd", DateUtil.getStringDate(workFrom, "dd"));
            setFieldValue(pdfDocument, "work_from_yy", DateUtil.getStringDate(workFrom, "yy"));
        }

        if (workTo != null) {
            setFieldValue(pdfDocument, "work_to_mm", DateUtil.getStringDate(workTo, "MM"));
            setFieldValue(pdfDocument, "work_to_dd", DateUtil.getStringDate(workTo, "dd"));
            setFieldValue(pdfDocument, "work_to_yy", DateUtil.getStringDate(workTo, "yy"));
        }
    }

    private void box17(PDDocument pdfDocument, String[] entry) throws IOException {
        //Box 17 Ref Physician //
        setFieldValue(pdfDocument, "ref_physician_number", entry[Waystar837P.RefProvQual.ordinal()]);

        FullName refPhysician = new FullName(entry[Waystar837P.RefProvFName.ordinal()],
                entry[Waystar837P.RefProvMName.ordinal()],
                entry[Waystar837P.RefProvLName.ordinal()],
                entry[Waystar837P.RefProvSuffix.ordinal()]);
        setFieldValue(pdfDocument, "ref_physician_name", StringUtil.formatName(refPhysician, "FMLC", false));

        //Box 17a Ref Physician Info //
        setFieldValue(pdfDocument, "ref_physician_qualifier", entry[Waystar837P.RefOtherIDQual.ordinal()]);
        setFieldValue(pdfDocument, "ref_physician_group", entry[Waystar837P.RefOtherID.ordinal()]);

        //Box 17b Ref Physician NPI //
        setFieldValue(pdfDocument, "ref_physician_npi", entry[Waystar837P.RefNPI.ordinal()]);
    }

    private void box17(PDDocument pdfDocument, Loop2300 claim, Form1500Template form1500Template, InsuranceCompany insuranceCompany) throws IOException {
        Loop2310A referringProvider = claim.getLoop2310A();
        Loop2310D supervisingProvider = claim.getLoop2310D();
        Loop2420E orderingProvider = claim.getLoop2400List().get(0).getLoop2420E();

        NM1 nm1 = null;
        REF ref = null;
        if (!referringProvider.isEmpty()) {
            nm1 = referringProvider.getReferringProviderName();
            ref = referringProvider.getReferringProviderSecondaryIdentification();
        } else if (!supervisingProvider.isEmpty()) {
            nm1 = supervisingProvider.getSupervisingProviderName();
            ref = supervisingProvider.getSupervisingProviderSecondaryIdentification();
        } else if (!orderingProvider.isEmpty()) {
            nm1 = orderingProvider.getOrderingProviderName();
            ref = orderingProvider.getOrderingProviderSecondaryIdentification();
        }
        if (nm1 != null) {
            FullName physician = new FullName(
                    nm1.getNameFirst(),
                    nm1.getNameMiddle(),
                    nm1.getNameLastOrOrganizationName(),
                    nm1.getNameSuffix());

            setFieldValue(pdfDocument, "ref_physician_number", nm1.getEntityIdentifierCode());
            setFieldValue(pdfDocument, "ref_physician_name", StringUtil.formatName(physician, "FMLC", false));
            setFieldValue(pdfDocument, "ref_physician_npi", nm1.getIdentificationCode());
        }
        if (form1500Template != null) {
            if (form1500Template.getBox17AOtherIDQualifier() != null) {
                setFieldValue(pdfDocument, "ref_physician_qualifier", form1500Template.getBox17AOtherIDQualifier());
            }
            if (ref != null && ref.getReferenceIdentification() != null) {
                setFieldValue(pdfDocument, "ref_physician_group", ref.getReferenceIdentification());
            } else if(StringUtils.isNotBlank(form1500Template.getBox17AOtherIDEntry())) {
                setFieldValue(pdfDocument, "ref_physician_group", form1500Template.getBox17AOtherIDEntry());
            }
        } else if (ref != null) {
            setFieldValue(pdfDocument, "ref_physician_qualifier", ref.getReferenceIdentificationQualifier());
            setFieldValue(pdfDocument, "ref_physician_group", ref.getReferenceIdentification());
        }
    }

    private void box18(PDDocument pdfDocument, String[] entry) throws IOException {
        Date admission = DateUtil.getDate(entry[Waystar837P.AdmissionFrom.ordinal()], Constants.WAYSTAR_DATE_FORMAT);
        setFieldValue(pdfDocument, "hosp_from_mm", DateUtil.getStringDate(admission, "MM"));
        setFieldValue(pdfDocument, "hosp_from_dd", DateUtil.getStringDate(admission, "dd"));
        setFieldValue(pdfDocument, "hosp_from_yy", DateUtil.getStringDate(admission, "yy"));

        Date release = DateUtil.getDate(entry[Waystar837P.AdmissionFrom.ordinal()], Constants.WAYSTAR_DATE_FORMAT);
        setFieldValue(pdfDocument, "hosp_to_mm", DateUtil.getStringDate(release, "MM"));
        setFieldValue(pdfDocument, "hosp_to_dd", DateUtil.getStringDate(release, "dd"));
        setFieldValue(pdfDocument, "hosp_to_yy", DateUtil.getStringDate(release, "yy"));
    }

    private void box18(PDDocument pdfDocument, Loop2300 claim) throws IOException {

        Date admission = DateUtil.getDate(claim.getAdmissionDate().getDateTimePeriod(), Constants.DF_YYYYMMDD);
        setFieldValue(pdfDocument, "hosp_from_mm", DateUtil.getStringDate(admission, "MM"));
        setFieldValue(pdfDocument, "hosp_from_dd", DateUtil.getStringDate(admission, "dd"));
        setFieldValue(pdfDocument, "hosp_from_yy", DateUtil.getStringDate(admission, "yy"));

        Date discharge = DateUtil.getDate(claim.getDischargeDate().getDateTimePeriod(), Constants.DF_YYYYMMDD);
        setFieldValue(pdfDocument, "hosp_to_mm", DateUtil.getStringDate(discharge, "MM"));
        setFieldValue(pdfDocument, "hosp_to_dd", DateUtil.getStringDate(discharge, "dd"));
        setFieldValue(pdfDocument, "hosp_to_yy", DateUtil.getStringDate(discharge, "yy"));
    }

    private void box19(PDDocument pdfDocument, String[] entry) throws IOException {
        setFieldValue(pdfDocument, "additional_claim_info", entry[Waystar837P.AdditionalClaimInfo.ordinal()]);
    }

    private void box19(PDDocument pdfDocument, Loop2300 claim) throws IOException {
        setFieldValue(pdfDocument, "additional_claim_info", claim.getClaimNote().getDescription());
    }

    private void box20(PDDocument pdfDocument, String[] entry) throws IOException {
        setFieldValue(pdfDocument, "outside_lab_dollars", StringUtil.formParseAmount(entry[Waystar837P.OutsideLabCharge.ordinal()], true));
        setFieldValue(pdfDocument, "outside_lab_cents", StringUtil.formParseAmount(entry[Waystar837P.OutsideLabCharge.ordinal()], false));
    }

    private void box20(PDDocument pdfDocument, Loop2300 claim) throws IOException {
        PS1 ps1 = claim.getLoop2400List().get(0).getPurchasedServiceInformation();
        setFieldValue(pdfDocument, "outside_lab_dollars", StringUtil.formParseAmount(ps1.getMonetaryAmount(), true));
        setFieldValue(pdfDocument, "outside_lab_cents", StringUtil.formParseAmount(ps1.getMonetaryAmount(), false));
    }

    private void box21(PDDocument pdfDocument, String[] entry) throws IOException {
        //Box 21 Diagnosis Codes
        setFieldValue(pdfDocument, "icd_indicator", entry[Waystar837P.ICDindicator.ordinal()]);
        setFieldValue(pdfDocument, "icd_indicator_misc", "");

        setFieldValue(pdfDocument, "diag_code_a", formatDiagnosisCode(entry[Waystar837P.DiagCodeA.ordinal()]));
        setFieldValue(pdfDocument, "diag_code_b", formatDiagnosisCode(entry[Waystar837P.DiagCodeB.ordinal()]));
        setFieldValue(pdfDocument, "diag_code_c", formatDiagnosisCode(entry[Waystar837P.DiagCodeC.ordinal()]));
        setFieldValue(pdfDocument, "diag_code_d", formatDiagnosisCode(entry[Waystar837P.DiagCodeD.ordinal()]));
        setFieldValue(pdfDocument, "diag_code_e", formatDiagnosisCode(entry[Waystar837P.DiagCodeE.ordinal()]));
        setFieldValue(pdfDocument, "diag_code_f", formatDiagnosisCode(entry[Waystar837P.DiagCodeF.ordinal()]));
        setFieldValue(pdfDocument, "diag_code_g", formatDiagnosisCode(entry[Waystar837P.DiagCodeG.ordinal()]));
        setFieldValue(pdfDocument, "diag_code_h", formatDiagnosisCode(entry[Waystar837P.DiagCodeH.ordinal()]));
        setFieldValue(pdfDocument, "diag_code_i", formatDiagnosisCode(entry[Waystar837P.DiagCodeI.ordinal()]));
        setFieldValue(pdfDocument, "diag_code_j", formatDiagnosisCode(entry[Waystar837P.DiagCodeJ.ordinal()]));
        setFieldValue(pdfDocument, "diag_code_k", formatDiagnosisCode(entry[Waystar837P.DiagCodeK.ordinal()]));
        setFieldValue(pdfDocument, "diag_code_l", formatDiagnosisCode(entry[Waystar837P.DiagCodeL.ordinal()]));
    }

    private void box21(PDDocument pdfDocument, Loop2300 claim) throws IOException {
        //Box 21 Diagnosis Codes
        HI hi = claim.getHealthCareDiagnosisCode();
        setFieldValue(pdfDocument, "icd_indicator", "0");
        setFieldValue(pdfDocument, "icd_indicator_misc", "");

        String[] hci = hi.getHealthCareCodeInformation1().split(":");
        if (hci.length > 1)
            setFieldValue(pdfDocument, "diag_code_a", formatDiagnosisCodeX12(hci[1]));
        hci = hi.getHealthCareCodeInformation2().split(":");
        if (hci.length > 1)
            setFieldValue(pdfDocument, "diag_code_b", formatDiagnosisCodeX12(hci[1]));
        hci = hi.getHealthCareCodeInformation3().split(":");
        if (hci.length > 1)
            setFieldValue(pdfDocument, "diag_code_c", formatDiagnosisCodeX12(hci[1]));
        hci = hi.getHealthCareCodeInformation4().split(":");
        if (hci.length > 1)
            setFieldValue(pdfDocument, "diag_code_d", formatDiagnosisCodeX12(hci[1]));
        hci = hi.getHealthCareCodeInformation5().split(":");
        if (hci.length > 1)
            setFieldValue(pdfDocument, "diag_code_e", formatDiagnosisCodeX12(hci[1]));
        hci = hi.getHealthCareCodeInformation6().split(":");
        if (hci.length > 1)
            setFieldValue(pdfDocument, "diag_code_f", formatDiagnosisCodeX12(hci[1]));
        hci = hi.getHealthCareCodeInformation7().split(":");
        if (hci.length > 1)
            setFieldValue(pdfDocument, "diag_code_g", formatDiagnosisCodeX12(hci[1]));
        hci = hi.getHealthCareCodeInformation8().split(":");
        if (hci.length > 1)
            setFieldValue(pdfDocument, "diag_code_h", formatDiagnosisCodeX12(hci[1]));
        hci = hi.getHealthCareCodeInformation9().split(":");
        if (hci.length > 1)
            setFieldValue(pdfDocument, "diag_code_i", formatDiagnosisCodeX12(hci[1]));
        hci = hi.getHealthCareCodeInformation10().split(":");
        if (hci.length > 1)
            setFieldValue(pdfDocument, "diag_code_j", formatDiagnosisCodeX12(hci[1]));
        hci = hi.getHealthCareCodeInformation11().split(":");
        if (hci.length > 1)
            setFieldValue(pdfDocument, "diag_code_k", formatDiagnosisCodeX12(hci[1]));
        hci = hi.getHealthCareCodeInformation12().split(":");
        if (hci.length > 1)
            setFieldValue(pdfDocument, "diag_code_l", formatDiagnosisCodeX12(hci[1]));
    }

    private void box22(PDDocument pdfDocument, String[] entry) throws IOException {
        setFieldValue(pdfDocument, "medicaid_resub_code", entry[Waystar837P.ResubCode.ordinal()]);
        setFieldValue(pdfDocument, "original_ref_number", entry[Waystar837P.OriginalRefNum.ordinal()]);
    }

    private void box22(PDDocument pdfDocument, Loop2300 claim) throws IOException {
        String[] info = claim.getClaimInformation().getHealthCareServiceLocationInformation().split(":");

        if (!info[2].equals("1")) {
            setFieldValue(pdfDocument, "medicaid_resub_code", info[2]);
        }
        setFieldValue(pdfDocument, "original_ref_number", claim.getPayerClaimControlNumber().getReferenceIdentification());
    }

    private void box23(PDDocument pdfDocument, String[] entry) throws IOException {
        setFieldValue(pdfDocument, "prior_auth_number", entry[Waystar837P.PriorAuth.ordinal()]);
    }

    private void box23(PDDocument pdfDocument, Loop2300 claim) throws IOException {
        REF ref = null;
        if (!claim.getPriorAuthorization().isEmpty()) {
            ref = claim.getPriorAuthorization();
        }
        if (!claim.getReferralNumber().isEmpty()) {
            ref = claim.getReferralNumber();
        }
        if (!claim.getCliaNumber().isEmpty()) {
            ref = claim.getCliaNumber();
        }
        if (!claim.getMammographyCertificationNumber().isEmpty()) {
            ref = claim.getMammographyCertificationNumber();
        }
        if (ref != null) {
            setFieldValue(pdfDocument, "prior_auth_number", ref.getReferenceIdentification());
        }
    }

    private void box24(PDDocument pdfDocument, String[] entry, int page, String[] mod1, String[] mod2, String[] mod3, String[] mod4, String[] mod5, String[] mod6) throws IOException {
        //Box 24-1 Service Charges each 2 rows Row 1 Left to Right then Row 2 Left to Right//
        if (!StringUtil.isBlank(page == 0 ? entry[Waystar837P.ProcCode_1.ordinal()] : entry[Waystar837P.ProcCode_7.ordinal()])) {
            //row 1
            setFieldValue(pdfDocument, "note_1", page == 0 ? entry[Waystar837P.Note_1.ordinal()] : entry[Waystar837P.Note_7.ordinal()]);
            setFieldValue(pdfDocument, "epsdt_1", page == 0 ? entry[Waystar837P.EPSDT_1.ordinal()] : entry[Waystar837P.EPSDT_7.ordinal()]);
            setFieldValue(pdfDocument, "service_prov_qual_1", page == 0 ? entry[Waystar837P.renprovnumqual_1.ordinal()] : entry[Waystar837P.renprovnumqual_7.ordinal()]);
            setFieldValue(pdfDocument, "service_prov_number_1", page == 0 ? entry[Waystar837P.renprovnum_1.ordinal()] : entry[Waystar837P.renprovnum_7.ordinal()]);

            //row 2
            Date svcFrom = DateUtil.getDate(page == 0 ? entry[Waystar837P.ServiceFrom_1.ordinal()] : entry[Waystar837P.ServiceFrom_7.ordinal()], Constants.WAYSTAR_DATE_FORMAT);
            setFieldValue(pdfDocument, "service_date_from_mm_1", DateUtil.getStringDate(svcFrom, "MM"));
            setFieldValue(pdfDocument, "service_date_from_dd_1", DateUtil.getStringDate(svcFrom, "dd"));
            setFieldValue(pdfDocument, "service_date_from_yy_1", DateUtil.getStringDate(svcFrom, "yy"));
            Date svcTo = DateUtil.getDate(page == 0 ? entry[Waystar837P.ServiceTo_1.ordinal()] : entry[Waystar837P.ServiceTo_7.ordinal()], Constants.WAYSTAR_DATE_FORMAT);
            setFieldValue(pdfDocument, "service_date_to_mm_1", DateUtil.getStringDate(svcTo, "MM"));
            setFieldValue(pdfDocument, "service_date_to_dd_1", DateUtil.getStringDate(svcTo, "dd"));
            setFieldValue(pdfDocument, "service_date_to_yy_1", DateUtil.getStringDate(svcTo, "yy"));

            setFieldValue(pdfDocument, "pos_1", page == 0 ? entry[Waystar837P.POS_1.ordinal()] : entry[Waystar837P.POS_7.ordinal()]);
            setFieldValue(pdfDocument, "emg_1", page == 0 ? entry[Waystar837P.Emg_1.ordinal()] : entry[Waystar837P.Emg_7.ordinal()]);
            setFieldValue(pdfDocument, "proc_code_1", page == 0 ? entry[Waystar837P.ProcCode_1.ordinal()] : entry[Waystar837P.ProcCode_7.ordinal()]);
            if (mod1.length > 0) {
                setFieldValue(pdfDocument, "mod_1_a", mod1[0]);
            }
            if (mod1.length > 1) {
                setFieldValue(pdfDocument, "mod_1_b", mod1[1]);
            }
            if (mod1.length > 2) {
                setFieldValue(pdfDocument, "mod_1_c", mod1[2]);
            }
            if (mod1.length > 3) {
                setFieldValue(pdfDocument, "mod_1_d", mod1[3]);
            }
            setFieldValue(pdfDocument, "diag_1", page == 0 ? entry[Waystar837P.DiagPtrs_1.ordinal()] : entry[Waystar837P.DiagPtrs_7.ordinal()]);
            setFieldValue(pdfDocument, "charge_dollars_1", StringUtil.formParseAmount(page == 0 ? entry[Waystar837P.Charge_1.ordinal()] : entry[Waystar837P.Charge_7.ordinal()], true));
            setFieldValue(pdfDocument, "charge_cents_1", StringUtil.formParseAmount(page == 0 ? entry[Waystar837P.Charge_1.ordinal()] : entry[Waystar837P.Charge_7.ordinal()], false));
            setFieldValue(pdfDocument, "units_1", page == 0 ? entry[Waystar837P.Units_1.ordinal()] : entry[Waystar837P.Units_7.ordinal()]);
            setFieldValue(pdfDocument, "plan_1", page == 0 ? entry[Waystar837P.FamilyPlan_1.ordinal()] : entry[Waystar837P.FamilyPlan_7.ordinal()]);
            setFieldValue(pdfDocument, "service_prov_npi_1", page == 0 ? entry[Waystar837P.rennpi_1.ordinal()] : entry[Waystar837P.rennpi_7.ordinal()]);
        }

        //Box 24-2 Service Charges each 2 rows Row 1 Left to Right then Row 2 Left to Right//
        if (!StringUtil.isBlank(page == 0 ? entry[Waystar837P.ProcCode_2.ordinal()] : entry[Waystar837P.ProcCode_8.ordinal()])) {
            //row 1
            setFieldValue(pdfDocument, "note_2", page == 0 ? entry[Waystar837P.Note_2.ordinal()] : entry[Waystar837P.Note_8.ordinal()]);
            setFieldValue(pdfDocument, "epsdt_2", page == 0 ? entry[Waystar837P.EPSDT_2.ordinal()] : entry[Waystar837P.EPSDT_8.ordinal()]);
            setFieldValue(pdfDocument, "service_prov_qual_2", page == 0 ? entry[Waystar837P.renprovnumqual_2.ordinal()] : entry[Waystar837P.renprovnumqual_8.ordinal()]);
            setFieldValue(pdfDocument, "service_prov_number_2", page == 0 ? entry[Waystar837P.renprovnum_2.ordinal()] : entry[Waystar837P.renprovnum_8.ordinal()]);

            //row 2
            Date svcFrom = DateUtil.getDate(page == 0 ? entry[Waystar837P.ServiceFrom_2.ordinal()] : entry[Waystar837P.ServiceFrom_8.ordinal()], Constants.WAYSTAR_DATE_FORMAT);
            setFieldValue(pdfDocument, "service_date_from_mm_2", DateUtil.getStringDate(svcFrom, "MM"));
            setFieldValue(pdfDocument, "service_date_from_dd_2", DateUtil.getStringDate(svcFrom, "dd"));
            setFieldValue(pdfDocument, "service_date_from_yy_2", DateUtil.getStringDate(svcFrom, "yy"));
            Date svcTo = DateUtil.getDate(page == 0 ? entry[Waystar837P.ServiceTo_2.ordinal()] : entry[Waystar837P.ServiceTo_8.ordinal()], Constants.WAYSTAR_DATE_FORMAT);
            setFieldValue(pdfDocument, "service_date_to_mm_2", DateUtil.getStringDate(svcTo, "MM"));
            setFieldValue(pdfDocument, "service_date_to_dd_2", DateUtil.getStringDate(svcTo, "dd"));
            setFieldValue(pdfDocument, "service_date_to_yy_2", DateUtil.getStringDate(svcTo, "yy"));

            setFieldValue(pdfDocument, "pos_2", page == 0 ? entry[Waystar837P.POS_2.ordinal()] : entry[Waystar837P.POS_8.ordinal()]);
            setFieldValue(pdfDocument, "emg_2", page == 0 ? entry[Waystar837P.Emg_2.ordinal()] : entry[Waystar837P.Emg_8.ordinal()]);
            setFieldValue(pdfDocument, "proc_code_2", page == 0 ? entry[Waystar837P.ProcCode_2.ordinal()] : entry[Waystar837P.ProcCode_8.ordinal()]);
            if (mod2.length > 0) {
                setFieldValue(pdfDocument, "mod_2_a", mod2[0]);
            }
            if (mod2.length > 1) {
                setFieldValue(pdfDocument, "mod_2_b", mod2[1]);
            }
            if (mod2.length > 2) {
                setFieldValue(pdfDocument, "mod_2_c", mod2[2]);
            }
            if (mod2.length > 3) {
                setFieldValue(pdfDocument, "mod_2_d", mod2[3]);
            }
            setFieldValue(pdfDocument, "diag_2", page == 0 ? entry[Waystar837P.DiagPtrs_2.ordinal()] : entry[Waystar837P.DiagPtrs_8.ordinal()]);
            setFieldValue(pdfDocument, "charge_dollars_2", StringUtil.formParseAmount(page == 0 ? entry[Waystar837P.Charge_2.ordinal()] : entry[Waystar837P.Charge_8.ordinal()], true));
            setFieldValue(pdfDocument, "charge_cents_2", StringUtil.formParseAmount(page == 0 ? entry[Waystar837P.Charge_2.ordinal()] : entry[Waystar837P.Charge_8.ordinal()], false));
            setFieldValue(pdfDocument, "units_2", page == 0 ? entry[Waystar837P.Units_2.ordinal()] : entry[Waystar837P.Units_8.ordinal()]);
            setFieldValue(pdfDocument, "plan_2", page == 0 ? entry[Waystar837P.FamilyPlan_2.ordinal()] : entry[Waystar837P.FamilyPlan_8.ordinal()]);
            setFieldValue(pdfDocument, "service_prov_npi_2", page == 0 ? entry[Waystar837P.rennpi_2.ordinal()] : entry[Waystar837P.rennpi_8.ordinal()]);
        }

        //Box 24-3 Service Charges each 2 rows Row 1 Left to Right then Row 2 Left to Right//
        if (!StringUtil.isBlank(page == 0 ? entry[Waystar837P.ProcCode_3.ordinal()] : entry[Waystar837P.ProcCode_9.ordinal()])) {
            //row 1
            setFieldValue(pdfDocument, "note_3", page == 0 ? entry[Waystar837P.Note_3.ordinal()] : entry[Waystar837P.Note_9.ordinal()]);
            setFieldValue(pdfDocument, "epsdt_3", page == 0 ? entry[Waystar837P.EPSDT_3.ordinal()] : entry[Waystar837P.EPSDT_9.ordinal()]);
            setFieldValue(pdfDocument, "service_prov_qual_3", page == 0 ? entry[Waystar837P.renprovnumqual_3.ordinal()] : entry[Waystar837P.renprovnumqual_9.ordinal()]);
            setFieldValue(pdfDocument, "service_prov_number_3", page == 0 ? entry[Waystar837P.renprovnum_3.ordinal()] : entry[Waystar837P.renprovnum_9.ordinal()]);

            //row 2
            Date svcFrom = DateUtil.getDate(page == 0 ? entry[Waystar837P.ServiceFrom_3.ordinal()] : entry[Waystar837P.ServiceFrom_9.ordinal()], Constants.WAYSTAR_DATE_FORMAT);
            setFieldValue(pdfDocument, "service_date_from_mm_3", DateUtil.getStringDate(svcFrom, "MM"));
            setFieldValue(pdfDocument, "service_date_from_dd_3", DateUtil.getStringDate(svcFrom, "dd"));
            setFieldValue(pdfDocument, "service_date_from_yy_3", DateUtil.getStringDate(svcFrom, "yy"));
            Date svcTo = DateUtil.getDate(entry[Waystar837P.ServiceTo_3.ordinal()], Constants.WAYSTAR_DATE_FORMAT);
            setFieldValue(pdfDocument, "service_date_to_mm_3", DateUtil.getStringDate(svcTo, "MM"));
            setFieldValue(pdfDocument, "service_date_to_dd_3", DateUtil.getStringDate(svcTo, "dd"));
            setFieldValue(pdfDocument, "service_date_to_yy_3", DateUtil.getStringDate(svcTo, "yy"));

            setFieldValue(pdfDocument, "pos_3", page == 0 ? entry[Waystar837P.POS_3.ordinal()] : entry[Waystar837P.POS_9.ordinal()]);
            setFieldValue(pdfDocument, "emg_3", page == 0 ? entry[Waystar837P.Emg_3.ordinal()] : entry[Waystar837P.Emg_9.ordinal()]);
            setFieldValue(pdfDocument, "proc_code_3", page == 0 ? entry[Waystar837P.ProcCode_3.ordinal()] : entry[Waystar837P.ProcCode_9.ordinal()]);
            if (mod3.length > 0) {
                setFieldValue(pdfDocument, "mod_3_a", mod3[0]);
            }
            if (mod3.length > 1) {
                setFieldValue(pdfDocument, "mod_3_b", mod3[1]);
            }
            if (mod3.length > 2) {
                setFieldValue(pdfDocument, "mod_3_c", mod3[2]);
            }
            if (mod3.length > 3) {
                setFieldValue(pdfDocument, "mod_3_d", mod3[3]);
            }
            setFieldValue(pdfDocument, "diag_3", page == 0 ? entry[Waystar837P.DiagPtrs_3.ordinal()] : entry[Waystar837P.DiagPtrs_9.ordinal()]);
            setFieldValue(pdfDocument, "charge_dollars_3", StringUtil.formParseAmount(page == 0 ? entry[Waystar837P.Charge_3.ordinal()] : entry[Waystar837P.Charge_9.ordinal()], true));
            setFieldValue(pdfDocument, "charge_cents_3", StringUtil.formParseAmount(page == 0 ? entry[Waystar837P.Charge_3.ordinal()] : entry[Waystar837P.Charge_9.ordinal()], false));
            setFieldValue(pdfDocument, "units_3", page == 0 ? entry[Waystar837P.Units_3.ordinal()] : entry[Waystar837P.Units_9.ordinal()]);
            setFieldValue(pdfDocument, "plan_3", page == 0 ? entry[Waystar837P.FamilyPlan_3.ordinal()] : entry[Waystar837P.FamilyPlan_9.ordinal()]);
            setFieldValue(pdfDocument, "service_prov_npi_3", page == 0 ? entry[Waystar837P.rennpi_3.ordinal()] : entry[Waystar837P.rennpi_9.ordinal()]);
        }

        //Box 24-4 Service Charges each 2 rows Row 1 Left to Right then Row 2 Left to Right//
        if (!StringUtil.isBlank(page == 0 ? entry[Waystar837P.ProcCode_4.ordinal()] : entry[Waystar837P.ProcCode_10.ordinal()])) {
            //row 1
            setFieldValue(pdfDocument, "note_4", page == 0 ? entry[Waystar837P.Note_4.ordinal()] : entry[Waystar837P.Note_10.ordinal()]);
            setFieldValue(pdfDocument, "epsdt_4", page == 0 ? entry[Waystar837P.EPSDT_4.ordinal()] : entry[Waystar837P.EPSDT_10.ordinal()]);
            setFieldValue(pdfDocument, "service_prov_qual_4", page == 0 ? entry[Waystar837P.renprovnumqual_4.ordinal()] : entry[Waystar837P.renprovnumqual_10.ordinal()]);
            setFieldValue(pdfDocument, "service_prov_number_4", page == 0 ? entry[Waystar837P.renprovnum_4.ordinal()] : entry[Waystar837P.renprovnum_10.ordinal()]);

            //row 2
            Date svcFrom = DateUtil.getDate(page == 0 ? entry[Waystar837P.ServiceFrom_4.ordinal()] : entry[Waystar837P.ServiceFrom_10.ordinal()], Constants.WAYSTAR_DATE_FORMAT);
            setFieldValue(pdfDocument, "service_date_from_mm_4", DateUtil.getStringDate(svcFrom, "MM"));
            setFieldValue(pdfDocument, "service_date_from_dd_4", DateUtil.getStringDate(svcFrom, "dd"));
            setFieldValue(pdfDocument, "service_date_from_yy_4", DateUtil.getStringDate(svcFrom, "yy"));
            Date svcTo = DateUtil.getDate(page == 0 ? entry[Waystar837P.ServiceTo_4.ordinal()] : entry[Waystar837P.ServiceTo_10.ordinal()], Constants.WAYSTAR_DATE_FORMAT);
            setFieldValue(pdfDocument, "service_date_to_mm_4", DateUtil.getStringDate(svcTo, "MM"));
            setFieldValue(pdfDocument, "service_date_to_dd_4", DateUtil.getStringDate(svcTo, "dd"));
            setFieldValue(pdfDocument, "service_date_to_yy_4", DateUtil.getStringDate(svcTo, "yy"));

            setFieldValue(pdfDocument, "pos_4", page == 0 ? entry[Waystar837P.POS_4.ordinal()] : entry[Waystar837P.POS_10.ordinal()]);
            setFieldValue(pdfDocument, "emg_4", page == 0 ? entry[Waystar837P.Emg_4.ordinal()] : entry[Waystar837P.Emg_10.ordinal()]);
            setFieldValue(pdfDocument, "proc_code_4", page == 0 ? entry[Waystar837P.ProcCode_4.ordinal()] : entry[Waystar837P.ProcCode_10.ordinal()]);
            if (mod4.length > 0) {
                setFieldValue(pdfDocument, "mod_4_a", mod4[0]);
            }
            if (mod4.length > 1) {
                setFieldValue(pdfDocument, "mod_4_b", mod4[1]);
            }
            if (mod4.length > 2) {
                setFieldValue(pdfDocument, "mod_4_c", mod4[2]);
            }
            if (mod4.length > 3) {
                setFieldValue(pdfDocument, "mod_4_d", mod4[3]);
            }
            setFieldValue(pdfDocument, "diag_4", page == 0 ? entry[Waystar837P.DiagPtrs_4.ordinal()] : entry[Waystar837P.DiagPtrs_10.ordinal()]);
            setFieldValue(pdfDocument, "charge_dollars_4", StringUtil.formParseAmount(page == 0 ? entry[Waystar837P.Charge_4.ordinal()] : entry[Waystar837P.Charge_10.ordinal()], true));
            setFieldValue(pdfDocument, "charge_cents_4", StringUtil.formParseAmount(page == 0 ? entry[Waystar837P.Charge_4.ordinal()] : entry[Waystar837P.Charge_10.ordinal()], false));
            setFieldValue(pdfDocument, "units_4", page == 0 ? entry[Waystar837P.Units_4.ordinal()] : entry[Waystar837P.Units_10.ordinal()]);
            setFieldValue(pdfDocument, "plan_4", page == 0 ? entry[Waystar837P.FamilyPlan_4.ordinal()] : entry[Waystar837P.FamilyPlan_10.ordinal()]);
            setFieldValue(pdfDocument, "service_prov_npi_4", page == 0 ? entry[Waystar837P.rennpi_4.ordinal()] : entry[Waystar837P.rennpi_10.ordinal()]);
        }

        //Box 24-5 Service Charges each 2 rows Row 1 Left to Right then Row 2 Left to Right//
        if (!StringUtil.isBlank(page == 0 ? entry[Waystar837P.ProcCode_5.ordinal()] : entry[Waystar837P.ProcCode_11.ordinal()])) {
            //row 1
            setFieldValue(pdfDocument, "note_5", page == 0 ? entry[Waystar837P.Note_5.ordinal()] : entry[Waystar837P.Note_11.ordinal()]);
            setFieldValue(pdfDocument, "epsdt_5", page == 0 ? entry[Waystar837P.EPSDT_5.ordinal()] : entry[Waystar837P.EPSDT_11.ordinal()]);
            setFieldValue(pdfDocument, "service_prov_qual_5", page == 0 ? entry[Waystar837P.renprovnumqual_5.ordinal()] : entry[Waystar837P.renprovnumqual_11.ordinal()]);
            setFieldValue(pdfDocument, "service_prov_number_5", page == 0 ? entry[Waystar837P.renprovnum_5.ordinal()] : entry[Waystar837P.renprovnum_11.ordinal()]);

            //row 2
            Date svcFrom = DateUtil.getDate(page == 0 ? entry[Waystar837P.ServiceFrom_5.ordinal()] : entry[Waystar837P.ServiceFrom_11.ordinal()], Constants.WAYSTAR_DATE_FORMAT);
            setFieldValue(pdfDocument, "service_date_from_mm_5", DateUtil.getStringDate(svcFrom, "MM"));
            setFieldValue(pdfDocument, "service_date_from_dd_5", DateUtil.getStringDate(svcFrom, "dd"));
            setFieldValue(pdfDocument, "service_date_from_yy_5", DateUtil.getStringDate(svcFrom, "yy"));
            Date svcTo = DateUtil.getDate(page == 0 ? entry[Waystar837P.ServiceTo_5.ordinal()] : entry[Waystar837P.ServiceTo_11.ordinal()], Constants.WAYSTAR_DATE_FORMAT);
            setFieldValue(pdfDocument, "service_date_to_mm_5", DateUtil.getStringDate(svcTo, "MM"));
            setFieldValue(pdfDocument, "service_date_to_dd_5", DateUtil.getStringDate(svcTo, "dd"));
            setFieldValue(pdfDocument, "service_date_to_yy_5", DateUtil.getStringDate(svcTo, "yy"));

            setFieldValue(pdfDocument, "pos_5", page == 0 ? entry[Waystar837P.POS_5.ordinal()] : entry[Waystar837P.POS_11.ordinal()]);
            setFieldValue(pdfDocument, "emg_5", page == 0 ? entry[Waystar837P.Emg_5.ordinal()] : entry[Waystar837P.Emg_11.ordinal()]);
            setFieldValue(pdfDocument, "proc_code_5", page == 0 ? entry[Waystar837P.ProcCode_5.ordinal()] : entry[Waystar837P.ProcCode_11.ordinal()]);
            if (mod5.length > 0) {
                setFieldValue(pdfDocument, "mod_5_a", mod5[0]);
            }
            if (mod5.length > 1) {
                setFieldValue(pdfDocument, "mod_5_b", mod5[1]);
            }
            if (mod5.length > 2) {
                setFieldValue(pdfDocument, "mod_5_c", mod5[2]);
            }
            if (mod5.length > 3) {
                setFieldValue(pdfDocument, "mod_5_d", mod5[3]);
            }
            setFieldValue(pdfDocument, "diag_5", page == 0 ? entry[Waystar837P.DiagPtrs_5.ordinal()] : entry[Waystar837P.DiagPtrs_11.ordinal()]);
            setFieldValue(pdfDocument, "charge_dollars_5", StringUtil.formParseAmount(page == 0 ? entry[Waystar837P.Charge_5.ordinal()] : entry[Waystar837P.Charge_11.ordinal()], true));
            setFieldValue(pdfDocument, "charge_cents_5", StringUtil.formParseAmount(page == 0 ? entry[Waystar837P.Charge_5.ordinal()] : entry[Waystar837P.Charge_11.ordinal()], false));
            setFieldValue(pdfDocument, "units_5", page == 0 ? entry[Waystar837P.Units_5.ordinal()] : entry[Waystar837P.Units_11.ordinal()]);
            setFieldValue(pdfDocument, "plan_5", page == 0 ? entry[Waystar837P.FamilyPlan_5.ordinal()] : entry[Waystar837P.FamilyPlan_11.ordinal()]);
            setFieldValue(pdfDocument, "service_prov_npi_5", page == 0 ? entry[Waystar837P.rennpi_5.ordinal()] : entry[Waystar837P.rennpi_11.ordinal()]);
        }

        //Box 24-6 Service Charges each 2 rows Row 1 Left to Right then Row 2 Left to Right//
        if (!StringUtil.isBlank(page == 0 ? entry[Waystar837P.ProcCode_6.ordinal()] : entry[Waystar837P.ProcCode_12.ordinal()])) {
            //row 1
            setFieldValue(pdfDocument, "note_6", page == 0 ? entry[Waystar837P.Note_6.ordinal()] : entry[Waystar837P.Note_12.ordinal()]);
            setFieldValue(pdfDocument, "epsdt_6", page == 0 ? entry[Waystar837P.EPSDT_6.ordinal()] : entry[Waystar837P.EPSDT_12.ordinal()]);
            setFieldValue(pdfDocument, "service_prov_qual_6", page == 0 ? entry[Waystar837P.renprovnumqual_6.ordinal()] : entry[Waystar837P.renprovnumqual_12.ordinal()]);
            setFieldValue(pdfDocument, "service_prov_number_6", page == 0 ? entry[Waystar837P.renprovnum_6.ordinal()] : entry[Waystar837P.renprovnum_12.ordinal()]);

            //row 2
            Date svcFrom = DateUtil.getDate(page == 0 ? entry[Waystar837P.ServiceFrom_6.ordinal()] : entry[Waystar837P.ServiceFrom_12.ordinal()], Constants.WAYSTAR_DATE_FORMAT);
            setFieldValue(pdfDocument, "service_date_from_mm_6", DateUtil.getStringDate(svcFrom, "MM"));
            setFieldValue(pdfDocument, "service_date_from_dd_6", DateUtil.getStringDate(svcFrom, "dd"));
            setFieldValue(pdfDocument, "service_date_from_yy_6", DateUtil.getStringDate(svcFrom, "yy"));
            Date svcTo = DateUtil.getDate(page == 0 ? entry[Waystar837P.ServiceTo_6.ordinal()] : entry[Waystar837P.ServiceTo_12.ordinal()], Constants.WAYSTAR_DATE_FORMAT);
            setFieldValue(pdfDocument, "service_date_to_mm_6", DateUtil.getStringDate(svcTo, "MM"));
            setFieldValue(pdfDocument, "service_date_to_dd_6", DateUtil.getStringDate(svcTo, "dd"));
            setFieldValue(pdfDocument, "service_date_to_yy_6", DateUtil.getStringDate(svcTo, "yy"));

            setFieldValue(pdfDocument, "pos_6", page == 0 ? entry[Waystar837P.POS_6.ordinal()] : entry[Waystar837P.POS_12.ordinal()]);
            setFieldValue(pdfDocument, "emg_6", page == 0 ? entry[Waystar837P.Emg_6.ordinal()] : entry[Waystar837P.Emg_12.ordinal()]);
            setFieldValue(pdfDocument, "proc_code_6", page == 0 ? entry[Waystar837P.ProcCode_6.ordinal()] : entry[Waystar837P.ProcCode_12.ordinal()]);
            if (mod6.length > 0) {
                setFieldValue(pdfDocument, "mod_6_a", mod6[0]);
            }
            if (mod6.length > 1) {
                setFieldValue(pdfDocument, "mod_6_b", mod6[1]);
            }
            if (mod6.length > 2) {
                setFieldValue(pdfDocument, "mod_6_c", mod6[2]);
            }
            if (mod6.length > 3) {
                setFieldValue(pdfDocument, "mod_6_d", mod6[3]);
            }
            setFieldValue(pdfDocument, "diag_6", page == 0 ? entry[Waystar837P.DiagPtrs_6.ordinal()] : entry[Waystar837P.DiagPtrs_12.ordinal()]);
            setFieldValue(pdfDocument, "charge_dollars_6", StringUtil.formParseAmount(page == 0 ? entry[Waystar837P.Charge_6.ordinal()] : entry[Waystar837P.Charge_12.ordinal()], true));
            setFieldValue(pdfDocument, "charge_cents_6", StringUtil.formParseAmount(page == 0 ? entry[Waystar837P.Charge_6.ordinal()] : entry[Waystar837P.Charge_12.ordinal()], false));
            setFieldValue(pdfDocument, "units_6", page == 0 ? entry[Waystar837P.Units_6.ordinal()] : entry[Waystar837P.Units_12.ordinal()]);
            setFieldValue(pdfDocument, "plan_6", page == 0 ? entry[Waystar837P.FamilyPlan_6.ordinal()] : entry[Waystar837P.FamilyPlan_12.ordinal()]);
            setFieldValue(pdfDocument, "service_prov_npi_6", page == 0 ? entry[Waystar837P.rennpi_6.ordinal()] : entry[Waystar837P.rennpi_12.ordinal()]);
        }
    }

    /**
     * <p>
     * This will call box28 to fill the total charges
     * </p>
     *
     * @param pdfDocument
     * @param serviceLines
     * @throws IOException
     */
    private BigDecimal box24(PDDocument pdfDocument, List<Loop2400> serviceLines, Loop2300 claim, Form1500Template form1500Template) throws IOException {
        int lineCount = serviceLines.size();
        if (serviceLines.size() > 6) {
            throw new IOException("Loop2400 contained ".concat(String.valueOf(lineCount)).concat(" lines."));
        }
        BigDecimal totalCharges = BigDecimal.ZERO;
        for (int i = 1; i <= serviceLines.size(); i++) {
            Loop2400 line = serviceLines.get(i - 1);
            SV1 sv1 = line.getProfessionalService();
            Loop2410 drugInfo = line.getLoop2410();

            //row 1
            String[] composite = sv1.getCompositeMedicalProcedureIdentifier().split(":");
            Boolean useBox24UPN = form1500Template != null && form1500Template.getUseBox24UPN() != null && form1500Template.getUseBox24UPN();

            String upnQualifier = drugInfo != null && drugInfo.getDrugIdentification() != null
                    ? drugInfo.getDrugIdentification().getProductServiceIdQualifier1()
                    : "";
            String upn = drugInfo != null && drugInfo.getDrugIdentification() != null
                    ? drugInfo.getDrugIdentification().getProductServiceId1()
                    : "";
            String unitOfMeasureQualifier = drugInfo != null && drugInfo.getDrugQuantity() != null
                    ? drugInfo.getDrugQuantity().getCompositeUnitOfMeasure()
                    : "";
            String unitOfMeasureQuantity = drugInfo != null && drugInfo.getDrugQuantity() != null
                    ? drugInfo.getDrugQuantity().getQuantity()
                    : "";
            String upnValues = upnQualifier + upn
                    + StringUtils.leftPad("", 44)
                    + unitOfMeasureQualifier + unitOfMeasureQuantity;

            String justificationValue = composite.length > SV1.sv1CMPI.justification.ordinal() ? composite[SV1.sv1CMPI.justification.ordinal()] : "";
            Boolean codeHasItemAttached = drugInfo != null &&
                    ((drugInfo.getDrugIdentification() != null && !drugInfo.getDrugIdentification().isEmpty())
                            || (drugInfo.getDrugQuantity() != null && !drugInfo.getDrugQuantity().isEmpty()));

            String noteValue = useBox24UPN && codeHasItemAttached ? upnValues : justificationValue;
            setFieldValue(pdfDocument, String.format("note_%s", i), noteValue);

            String EPSDT = sv1.getEspdtIndicator();
            if (StringUtils.isBlank(EPSDT)) {
                CRC epsdtReferal = claim.getEpsdtReferral();
                EPSDT = epsdtReferal.getCodeCategory();
            }
            Loop2420A renderingProviderLoop = line.getLoop2420A();
            setFieldValue(pdfDocument, String.format("epsdt_%s", i), EPSDT);
            if (!line.getPriorAuthorization().isEmpty()) {
                REF priorAuth = line.getPriorAuthorization();
                setFieldValue(pdfDocument, String.format("service_prov_qual_%s", i), priorAuth.getReferenceIdentificationQualifier());
                setFieldValue(pdfDocument, String.format("service_prov_number_%s", i), priorAuth.getReferenceIdentification());
            } else if (renderingProviderLoop != null && !renderingProviderLoop.isEmpty()) {
                REF renderingProviderLineDetails = line.getLoop2420A().getRenderingProviderSecondaryIdentification();
                setFieldValue(pdfDocument, String.format("service_prov_qual_%s", i), renderingProviderLineDetails.getReferenceIdentificationQualifier());
                setFieldValue(pdfDocument, String.format("service_prov_number_%s", i), renderingProviderLineDetails.getReferenceIdentification());
            }
            //row 2
            DTP serviceDate = line.getServiceDate();
            String[] dateRange = serviceDate.getDateTimePeriod().split("-");

            Date svcFrom = DateUtil.getDate(dateRange[0], Constants.DF_YYYYMMDD);
            setFieldValue(pdfDocument, String.format("service_date_from_mm_%s", i), DateUtil.getStringDate(svcFrom, "MM"));
            setFieldValue(pdfDocument, String.format("service_date_from_dd_%s", i), DateUtil.getStringDate(svcFrom, "dd"));
            setFieldValue(pdfDocument, String.format("service_date_from_yy_%s", i), DateUtil.getStringDate(svcFrom, "yy"));

            if (dateRange.length > 1) {
                Date svcTo = DateUtil.getDate(dateRange[1], Constants.DF_YYYYMMDD);

                setFieldValue(pdfDocument, String.format("service_date_to_mm_%s", i), DateUtil.getStringDate(svcTo, "MM"));
                setFieldValue(pdfDocument, String.format("service_date_to_dd_%s", i), DateUtil.getStringDate(svcTo, "dd"));
                setFieldValue(pdfDocument, String.format("service_date_to_yy_%s", i), DateUtil.getStringDate(svcTo, "yy"));

                setFieldValue(pdfDocument, String.format("pos_%s", i), sv1.getPlaceOfServiceCode());
                setFieldValue(pdfDocument, String.format("emg_%s", i), sv1.getEmergencyIndicator());

                String[] pc = sv1.getCompositeMedicalProcedureIdentifier().split(":");
                setFieldValue(pdfDocument, String.format("proc_code_%s", i), pc[1]);
                if (pc.length >= 3)
                    setFieldValue(pdfDocument, String.format("mod_%s_a", i), pc[2]);
                if (pc.length >= 4)
                    setFieldValue(pdfDocument, String.format("mod_%s_b", i), pc[3]);
                if (pc.length >= 5)
                    setFieldValue(pdfDocument, String.format("mod_%s_c", i), pc[4]);
                if (pc.length >= 6)
                    setFieldValue(pdfDocument, String.format("mod_%s_d", i), pc[5]);

                String[] dc = sv1.getCompositeDiagnosisCodePointer().split(":");
                String[] alph = {"A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L"};
                StringBuilder pointers = new StringBuilder();
                for (String e : dc) {
                    if (StringUtils.isNotBlank(e)) {
                        pointers.append(alph[Integer.parseInt(e) - 1]);
                    }
                }
                setFieldValue(pdfDocument, String.format("diag_%s", i), pointers.toString());
                totalCharges = totalCharges.add(new BigDecimal(sv1.getLineItemChargeAmount()));
                setFieldValue(pdfDocument, String.format("charge_dollars_%s", i), StringUtil.formParseAmount(sv1.getLineItemChargeAmount(), true));
                setFieldValue(pdfDocument, String.format("charge_cents_%s", i), StringUtil.formParseAmount(sv1.getLineItemChargeAmount(), false));
                setFieldValue(pdfDocument, String.format("units_%s", i), sv1.getServiceUnitCount());
                setFieldValue(pdfDocument, String.format("plan_%s", i), sv1.getFamilyPlanningIndicator());

                NM1 rendering = line.getLoop2420A().getRenderingProviderName();
                setFieldValue(pdfDocument, String.format("service_prov_npi_%s", i), rendering.getIdentificationCode());
            }
        }
        return totalCharges;
    }

    private void box25(PDDocument pdfDocument, String[] entry) throws IOException {
        //Box 25 TaxID or EIN Number //
        setFieldValue(pdfDocument, "billing_tin", entry[Waystar837P.BillTin.ordinal()]);
        if (Strings.isNullOrEmpty(entry[Waystar837P.BillTinType.ordinal()])) {
            setFieldValue(pdfDocument, "is_ssn", Constants.OFF);
            setFieldValue(pdfDocument, "is_ein", Constants.OFF);
        } else {
            boolean isEin = "E".equals(entry[Waystar837P.BillTinType.ordinal()]);
            setFieldValue(pdfDocument, "is_ssn", !isEin ? Constants.YES : Constants.OFF);
            setFieldValue(pdfDocument, "is_ein", isEin ? Constants.YES : Constants.OFF);
        }
    }

    private void box25(PDDocument pdfDocument, Loop2010AA billing) throws IOException {
        REF ref = billing.getBillingProviderTaxIdentification();
        if ("SY".equals(ref.getReferenceIdentificationQualifier())) {
            setFieldValue(pdfDocument, "is_ssn", Constants.YES);
            setFieldValue(pdfDocument, "is_ein", Constants.OFF);
        } else if ("EI".equals(ref.getReferenceIdentificationQualifier())) {
            setFieldValue(pdfDocument, "is_ein", Constants.YES);
            setFieldValue(pdfDocument, "is_ssn", Constants.OFF);
        } else {
            setFieldValue(pdfDocument, "is_ein", Constants.OFF);
            setFieldValue(pdfDocument, "is_ssn", Constants.OFF);
        }
        setFieldValue(pdfDocument, "billing_tin", ref.getReferenceIdentification());
    }

    private void box26(PDDocument pdfDocument, String[] entry) throws IOException {
        setFieldValue(pdfDocument, "patient_account_number", entry[Waystar837P.ClaimRef.ordinal()]);
    }

    private void box26(PDDocument pdfDocument, Loop2300 claim) throws IOException {
        setFieldValue(pdfDocument, "patient_account_number", claim.getClaimInformation().getPatientControlNumber());
    }

    private void box27(PDDocument pdfDocument, String[] entry) throws IOException {
        if ("Y".equals(entry[Waystar837P.AcceptAssignmentYesNo.ordinal()])) {
            setFieldValue(pdfDocument, "accept_assign_yes", Constants.YES);
        } else {
            setFieldValue(pdfDocument, "accept_assign_no", Constants.YES);
        }
    }

    private void box27(PDDocument pdfDocument, Loop2300 claim, Form1500Template form1500Template) throws IOException {
        if ("A".equals(claim.getClaimInformation().getAssignmentOrPlanParticipationCode())) {
            setFieldValue(pdfDocument, "accept_assign_yes", Constants.YES);
            setFieldValue(pdfDocument, "accept_assign_no", Constants.OFF);
        } else {
            setFieldValue(pdfDocument, "accept_assign_no", Constants.YES);
            setFieldValue(pdfDocument, "accept_assign_yes", Constants.OFF);
        }
    }

    private void box28(PDDocument pdfDocument, BigDecimal totalCharges) throws IOException {
        if (totalCharges.compareTo(BigDecimal.ZERO) != 0) {
            String t_charge = new DecimalFormat(Constants.DF0).format(totalCharges);
            String dollars = StringUtil.formParseAmount(t_charge, true);
            String cents = StringUtil.formParseAmount(t_charge, false);

            setFieldValue(pdfDocument, "total_charge_dollars", dollars);
            setFieldValue(pdfDocument, "total_charge_cents", cents);
        }
    }

    private void box29(PDDocument pdfDocument) throws IOException {
    /*
        Following a conversation with Teter and Josh Lau on 02/27/2020, it has been decided that Box 29
        should always be blank rather than calculating how much previous insurances have paid. I have commented
        out the previous code, and double commented the code that was there before it, in case this decision
        is ever reversed. -- Clark Murray, 02/27/2020
    */
        setFieldValue(pdfDocument, "amt_paid_dollars", "");
        setFieldValue(pdfDocument, "amt_paid_cents", "");

//        BigDecimal amount_paid = new BigDecimal(getTotalAmountPaid(claim));
//        String tempPaid = new DecimalFormat(DF0).format(total_patient_paid);
//        setFieldValue(pdfDocument, "amt_paid_dollars", total_patient_paid.equals(BigDecimal.ZERO) ? "" : StringUtil.formParseAmount(tempPaid, true));
//        setFieldValue(pdfDocument, "amt_paid_cents", total_patient_paid.equals(BigDecimal.ZERO) ? "" : StringUtil.formParseAmount(tempPaid, false));

//        String tempPaid = getTotalAmountPaid(claim);
////        setFieldValue(pdfDocument, "amt_paid_dollars", Strings.isNullOrEmpty(tempPaid) ? "" : StringUtil.formParseAmount(tempPaid, true));
////        setFieldValue(pdfDocument, "amt_paid_cents", Strings.isNullOrEmpty(tempPaid) ? "" : StringUtil.formParseAmount(tempPaid, false));

    }

    private void box29(PDDocument pdfDocument, Loop2300 claim, Form1500Template form1500Template) throws IOException {
        if (form1500Template != null && BooleanUtils.toBooleanDefaultIfNull(form1500Template.getUseCapitatedPayerSpecialEdits(), false)) {
            String amt = claim.getLoop2320List().get(0).getPayerPaidAmount().getMonetaryAmount();
            setFieldValue(pdfDocument, "amt_paid_dollars", StringUtils.isBlank(amt) ? "" : StringUtil.formParseAmount(amt, true));
            setFieldValue(pdfDocument, "amt_paid_cents", StringUtils.isBlank(amt) ? "" : StringUtil.formParseAmount(amt, false));
        } else {
            setFieldValue(pdfDocument, "amt_paid_dollars", "");
            setFieldValue(pdfDocument, "amt_paid_cents", "");
        }
    }

    private void box31(PDDocument pdfDocument, String[] entry) throws IOException {
        String physName = entry[Waystar837P.RenLName.ordinal()];
        setFieldValue(pdfDocument, "phys_signature", physName.length() > 22 ? physName.substring(0, 22) : physName);
        setFieldValue(pdfDocument, "phys_sig_date", entry[Waystar837P.RenProvSignatureDate.ordinal()]);
    }

    private void box31(PDDocument pdfDocument, Loop2300 claim, String date) throws IOException {
        Loop2420A rendering = claim.getLoop2400List().get(0).getLoop2420A();
        NM1 nm1 = rendering.getRenderingProviderName();
        String name = "";
        if ("1".equals(nm1.getEntityTypeQualifier())) {
            FullName fullName = new FullName(nm1.getNameFirst(), nm1.getNameMiddle(), nm1.getNameLastOrOrganizationName(), nm1.getNameSuffix());
            name = StringUtil.formatName(fullName, "FMLC", false);
        } else if ("2".equals(nm1.getEntityTypeQualifier())) {
            name = nm1.getNameLastOrOrganizationName();
        }
        setFieldValue(pdfDocument, "phys_signature", name.length() > 22 ? name.substring(0, 22) : name);
        Date d = DateUtil.getDate(date, Constants.DF_YYYYMMDD);
        setFieldValue(pdfDocument, "phys_sig_date", DateUtil.getStringDate(d, Constants.WAYSTAR_DATE_FORMAT));
    }

    private void box32(PDDocument pdfDocument, String[] entry) throws IOException {
        //Box 32 Service Facility Info //
        setFieldValue(pdfDocument, "fac_name", entry[Waystar837P.FacilityName.ordinal()]);
        setFieldValue(pdfDocument, "fac_street", entry[Waystar837P.FacilityAdd1.ordinal()]);
        FullAddress facAddress = new FullAddress(entry[Waystar837P.FacilityCity.ordinal()], entry[Waystar837P.FacilityState.ordinal()], entry[Waystar837P.FacilityZip.ordinal()]);
        setFieldValue(pdfDocument, "fac_location", StringUtil.formatAddress(facAddress));

        // Box 32a Facility NPI //
        setFieldValue(pdfDocument, "fac_npi", entry[Waystar837P.FacilityNPI.ordinal()]);

        // Box 32b Facility Provider Number //
        setFieldValue(pdfDocument, "fac_prov_number", entry[Waystar837P.FacilityProvNum.ordinal()]);
    }

    private void box32(PDDocument pdfDocument, Loop2300 claim) throws IOException {
        Loop2310C serviceFacility = claim.getLoop2310C();
        NM1 nm1 = serviceFacility.getServiceFacilityName();
        N3 n3 = serviceFacility.getServiceFacilityAddress();
        N4 n4 = serviceFacility.getServiceFacilityCityStateZip();
        if (!"NA".equals(nm1.getNameLastOrOrganizationName())) {
            setFieldValue(pdfDocument, "fac_name", nm1.getNameLastOrOrganizationName());
        }
        setFieldValue(pdfDocument, "fac_street", n3.getAddressInformation1());
        FullAddress facAddress = new FullAddress(n4.getCityName(), n4.getStateOrProvinceCode(), n4.getPostalCode());
        setFieldValue(pdfDocument, "fac_location", StringUtil.formatAddress(facAddress));

        setFieldValue(pdfDocument, "fac_npi", nm1.getIdentificationCode());
        Loop2420A rendering = claim.getLoop2400List().get(0).getLoop2420A();
        setFieldValue(pdfDocument, "fac_prov_number", rendering.getRenderingProviderSpecialtyInformation().getReferenceIdentification());
    }

    private void box33(PDDocument pdfDocument, String[] entry) throws IOException {
        String phone = entry[Waystar837P.BillPhone.ordinal()];
        if (!Strings.isNullOrEmpty(phone)) {
            setFieldValue(pdfDocument, "billing_phone_area", phone.substring(0, 3));
            setFieldValue(pdfDocument, "billing_phone", StringUtil.format1500Phone(phone.substring(3)));
        }
        setFieldValue(pdfDocument, "billing_name", entry[Waystar837P.BillName.ordinal()]);
        setFieldValue(pdfDocument, "billing_street", entry[Waystar837P.BillAddr1.ordinal()]);
        FullAddress billingAddress = new FullAddress(entry[Waystar837P.BillCity.ordinal()], entry[Waystar837P.BillState.ordinal()], entry[Waystar837P.BillZip.ordinal()]);
        setFieldValue(pdfDocument, "billing_location", StringUtil.formatAddress(billingAddress));
        setFieldValue(pdfDocument, "billing_npi", entry[Waystar837P.BillNPI.ordinal()]);
        setFieldValue(pdfDocument, "billing_prov_number", entry[Waystar837P.BillProvNum.ordinal()]);
    }

    private void box33(PDDocument pdfDocument, Loop2000A billingProvider, String phone, Form1500Template form1500Template) throws IOException {
        PRV prv = billingProvider.getSpecialtyInformation();
        Loop2010AA billingProviderName = billingProvider.getLoop2010AA(); //1500 - Box 33
        NM1 nm1 = billingProviderName.getBillingProviderName();
        N3 n3 = billingProviderName.getBillingProviderAddress();
        N4 n4 = billingProviderName.getBillingProviderCityStateZip();
        PER per = billingProviderName.getBillingProviderContactInformation();
        String areaCode = "";
        String phoneNumber = "";
        if (form1500Template != null && StringUtils.isNotBlank(form1500Template.getBillingPhoneNumber())) {
            areaCode = form1500Template.getBillingPhoneNumber().substring(0, 3);
            phoneNumber = form1500Template.getBillingPhoneNumber().substring(3);
        } else if (!per.isEmpty() && per.getCommunicationNumberQualifier1().equals("TE")) {
            areaCode = per.getCommunicationNumber1().substring(0, 3);
            phoneNumber = StringUtil.format1500Phone(per.getCommunicationNumber1().substring(3));
        } else if (!StringUtil.isBlank(phone)) {
            areaCode = phone.substring(0, 3);
            phoneNumber = StringUtil.format1500Phone(phone.substring(3));
        }
        setFieldValue(pdfDocument, "billing_phone_area", areaCode);
        setFieldValue(pdfDocument, "billing_phone", phoneNumber);
        setFieldValue(pdfDocument, "billing_name", nm1.getNameLastOrOrganizationName());
        setFieldValue(pdfDocument, "billing_street", n3.getAddressInformation1());
        FullAddress fullAddress = new FullAddress(n4.getCityName(), n4.getStateOrProvinceCode(), n4.getPostalCode());
        setFieldValue(pdfDocument, "billing_location", StringUtil.formatAddress(fullAddress));
        setFieldValue(pdfDocument, "billing_npi", nm1.getIdentificationCode());
        setFieldValue(pdfDocument, "billing_prov_number", prv.getReferenceIdentification());
    }

    private String formatDiagnosisCode(String code) {
        if (!Strings.isNullOrEmpty(code)) {
            return code.replace(".", " ");
        } else {
            return "";
        }
    }

    private String formatDiagnosisCodeX12(String code) {
        if (!Strings.isNullOrEmpty(code)) {
            code = code.substring(0, 3).concat(" ").concat(code.substring(3));
            return code;
        } else {
            return "";
        }
    }

    private String[] splitModifier(String modifier) {
        if (!Strings.isNullOrEmpty(modifier)) {
            return modifier.split(" ");
        } else {
            return new String[]{"", "", "", ""};
        }
    }

    public void setFieldValue(PDDocument pdfDocument, String fieldName, String value) throws IOException {
        PDAcroForm acroForm = pdfDocument.getDocumentCatalog().getAcroForm();
        if (acroForm != null) {
            PDField field = acroForm.getField(fieldName);
            if (field instanceof PDNonTerminalField) {
                PDNonTerminalField f = (PDNonTerminalField) field;
                f.getChildren().get(0).setValue(value != null ? value.trim() : Constants.OFF);
            } else {
                field.setValue(value != null ? value.trim() : Constants.OFF);
            }
        }
    }

    private String getPatientRelation(String s) {
        // 2836: do not put relationship to subscriber on paper
        //String rel_to_ins_1a;
        switch (s) {
            case "18":
                return "S";
            case "01":
                return "M";
            case "19":
                return "C";
            case "G8":
                return "O";
            default:
                return Constants.OFF;
        }
    }

    private String getPayerTypeStr(PayerType payerType) {
        return payerType != null ? payerType.getName() : "Other";
    }

    private List<String> printRequests(String file, String fileType, String payerType, Company currentCompany, String[] requests837) throws IOException {
        List<String> results = new ArrayList<>();
        for (String request837 : requests837) {
            String[] entry = request837.split("\\|", -1);
            int files = !Strings.isNullOrEmpty(entry[Waystar837P.Charge_7.ordinal()]) ? 2 : 1;
            for (int page = 0; page < files; page++) {
                java.io.File f = new java.io.File(uploadDirectory.concat(file));
                PDDocument pdfDocument = PDDocument.load(f);
                int[] charge_index =
                        new int[]{
                                page == 0 ? Waystar837P.Charge_1.ordinal() : Waystar837P.Charge_7.ordinal(),
                                page == 0 ? Waystar837P.Charge_2.ordinal() : Waystar837P.Charge_8.ordinal(),
                                page == 0 ? Waystar837P.Charge_3.ordinal() : Waystar837P.Charge_9.ordinal(),
                                page == 0 ? Waystar837P.Charge_4.ordinal() : Waystar837P.Charge_10.ordinal(),
                                page == 0 ? Waystar837P.Charge_5.ordinal() : Waystar837P.Charge_11.ordinal(),
                                page == 0 ? Waystar837P.Charge_6.ordinal() : Waystar837P.Charge_12.ordinal()
                        };
                String[] mod1 = splitModifier(entry[page == 0 ? Waystar837P.Modifiers_1.ordinal() : Waystar837P.Modifiers_7.ordinal()]);
                String[] mod2 = splitModifier(entry[page == 0 ? Waystar837P.Modifiers_2.ordinal() : Waystar837P.Modifiers_8.ordinal()]);
                String[] mod3 = splitModifier(entry[page == 0 ? Waystar837P.Modifiers_3.ordinal() : Waystar837P.Modifiers_9.ordinal()]);
                String[] mod4 = splitModifier(entry[page == 0 ? Waystar837P.Modifiers_4.ordinal() : Waystar837P.Modifiers_10.ordinal()]);
                String[] mod5 = splitModifier(entry[page == 0 ? Waystar837P.Modifiers_5.ordinal() : Waystar837P.Modifiers_11.ordinal()]);
                String[] mod6 = splitModifier(entry[page == 0 ? Waystar837P.Modifiers_6.ordinal() : Waystar837P.Modifiers_12.ordinal()]);

                BigDecimal totalCharges = BigDecimal.ZERO;
                for (int index : charge_index) {
                    if (!StringUtil.isBlank(entry[index])) {
                        totalCharges = totalCharges.add(new BigDecimal(entry[index]));
                    }
                }
                carrierBlock(pdfDocument, entry);
                box1(pdfDocument, entry, payerType);
                box2(pdfDocument, entry);
                box3(pdfDocument, entry);
                box4(pdfDocument, entry);
                box5(pdfDocument, entry);
                box6(pdfDocument, getPatientRelation(entry[Waystar837P.PatRel.ordinal()]));
                box7(pdfDocument, entry);
                box9(pdfDocument, entry);
                box10(pdfDocument, entry);
                box11(pdfDocument, entry);
                box12(pdfDocument, entry);
                box13(pdfDocument);
                box14(pdfDocument, entry);
                box15(pdfDocument, entry);
                box16(pdfDocument, entry);
                box17(pdfDocument, entry);
                box18(pdfDocument, entry);
                box19(pdfDocument, entry);
                box20(pdfDocument, entry);
                box21(pdfDocument, entry);
                box22(pdfDocument, entry);
                box23(pdfDocument, entry);
                box24(pdfDocument, entry, page, mod1, mod2, mod3, mod4, mod5, mod6);
                box25(pdfDocument, entry);
                box26(pdfDocument, entry);
                box27(pdfDocument, entry);
                box28(pdfDocument, totalCharges);
                box29(pdfDocument);
                box31(pdfDocument, entry);
                box32(pdfDocument, entry);
                box33(pdfDocument, entry);
                String key = Integer.toString(results.size() + 1);
                String filePath = uploadDirectory.concat("/company/").concat(currentCompany.getId().toString()).concat("/forms/").concat(fileType).concat(entry[Waystar837P.ClaimRef.ordinal()]).concat("_").concat(key).concat(".pdf");
                pdfDocument.save(filePath);
                pdfDocument.close();
                results.add(filePath);
            }
        }
//        String result = uploadDirectory.concat("/company/").concat(currentCompany.getId().toString().toString()).concat("/forms/").concat(fileType).concat("merged_").concat("P" + claim.getPrescription().getPatientId() + "C" + claim.getId()).concat(".pdf");
//        PDFMergerUtility ut = new PDFMergerUtility();
//        for (String r : results) {
//            ut.addSource(FileUtils.getFile(r));
//        }
//        ut.setDestinationFileName(result);
//        ut.mergeDocuments(MemoryUsageSetting.setupMainMemoryOnly());
//        results.forEach(r -> {
//            java.io.File f = FileUtils.getFile(r.replaceAll("uploads", uploadDirectory));
//            if (f != null && f.exists()) f.delete();
//        });
        return results;
    }
}
