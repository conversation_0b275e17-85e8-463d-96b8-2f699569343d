package com.nymbl.config.security;

import com.nymbl.master.model.User;
import com.nymbl.master.service.UserService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * Utility class for checking user privileges using the database-driven privilege system.
 */
@Component
public class PrivilegeChecker {

    private static final Logger logger = LoggerFactory.getLogger(PrivilegeChecker.class);

    private final UserService userService;

    @Autowired
    public PrivilegeChecker(UserService userService) {
        this.userService = userService;
    }

    /**
     * Check if the current user has the specified privilege.
     *
     * @param privilege the privilege to check
     * @return true if the user has the privilege, false otherwise
     */
    public boolean hasPrivilege(String privilege) {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();

        if (authentication == null) {
            logger.warn("No authentication found in security context");
            return false;
        }

        try {
            // Get the current user from the database
            User user = userService.findByUsername(authentication.getName());
            if (user == null) {
                logger.warn("User not found: {}", authentication.getName());
                return false;
            }

            // Check if user is superadmin (grants all privileges)
            if (user.getId() == 1 || Boolean.TRUE.equals(user.getIsSuperAdmin())) {
                return true;
            }

            // Get user's privileges from the database
            List<String> userPrivileges = userService.getPrivileges(user);
            return userPrivileges.contains(privilege);

        } catch (Exception e) {
            logger.error("Error checking privilege '{}' for user '{}'", privilege, authentication.getName(), e);
            return false;
        }
    }
}
