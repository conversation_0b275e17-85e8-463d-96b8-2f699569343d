app.factory('DownloadFactory', DownloadFactory);
DownloadFactory.$inject = ['$resource'];

function DownloadFactory($resource) {
	return $resource('api/download', {}, {
		download: {
			url: 'api/download/x12/837/claim/:claimId',
			method: 'GET',
			params: {
				claimId: '@_claimId'
			},
			transformResponse: function (data, header, status, config, statusText) {
				return {
					data: data.toString(),
					header: header(),
					status: status,
					config: config,
					statusText: statusText
				}
			}
		}
	});
}
