app.factory('ClaimFileFactory', ClaimFileFactory);
ClaimFileFactory.$inject = ['$resource'];

function ClaimFileFactory($resource) {
  return $resource('api/claim-file/:id', {id: '@_id'}, {
    findByClaimId: {
      url: 'api/claim-file/claim/:claimId',
      method: 'GET',
      isArray: true,
      params: {
        claimId: '@_claimId'
      }
    },
    printSubmittedCMS: {
      url: 'api/claim-file/cms-print/:claimFileId',
      method: 'GET',
      isArray: true,
      params: {
        claimFileId: '@_claimFileId'
      }
    }
  });
}
