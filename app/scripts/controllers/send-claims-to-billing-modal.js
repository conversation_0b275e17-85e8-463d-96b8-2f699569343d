app.controller('SendClaimsToBillingModalCtrl', SendClaimsToBillingModalCtrl);
SendClaimsToBillingModalCtrl.$inject = ['$scope', '$moment', '$uibModalInstance', 'claimIds', 'ClaimFactory', '$sce', 'UtilService'];

function SendClaimsToBillingModalCtrl($scope, $moment, $uibModalInstance, claimIds, ClaimFactory, $sce, UtilService) {

	$scope.moment = $moment;

	$scope.disableButton = claimIds.length > 0;
	$scope.claims = [];
	angular.forEach(claimIds, function (claimId) {
		ClaimFactory.get({id: claimId}).$promise.then(function (claim) {
			$scope.claims.push(claim);
		});
	});

	$scope.sendClaimsToBilling = function () {
		$scope.disableButton = true;
			ClaimFactory.sendClaimFiles({claimIds: claimIds, billingBranchId: null}).$promise.then(function (response) {
				$uibModalInstance.close(response);
			}, function (error) {
				console.log(error);
				UtilService.displayAlert("danger", "<p>Sending claims failed.</p>" + $sce.trustAsHtml(error.data.data), "header-alert-container");
				$('#send-files').button('reset');
			});
	};

	$scope.close = function () {
		$uibModalInstance.close();
	};

}
