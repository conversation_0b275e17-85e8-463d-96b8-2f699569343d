'use strict';
app.controller('UserCtrl', UserCtrl);
UserCtrl.$inject = ['$rootScope', '$scope', '$moment', '$uibModal', '$filter', 'UtilService', 'UserService', 'CompanyFactory', 'RoleFactory', 'UserFactory', 'PermissionService', 'InsuranceCompanyFactory', 'UserInsuranceCompanyFactory', 'UserBranchFactory', 'UserRoleFactory', 'UserPrivilegeFactory', 'SystemSettingFactory', 'BranchService', 'CalendarTemplatesFactory'];

function UserCtrl($rootScope, $scope, _$moment, $uibModal, $filter, UtilService, UserService, CompanyFactory, RoleFactory, UserFactory, PermissionService, InsuranceCompanyFactory, UserInsuranceCompanyFactory, UserBranchFactory, UserRoleFactory, UserPrivilegeFactory, SystemSettingFactory, BranchService, CalendarTemplatesFactory) {
  $rootScope.page = {
    title: 'Maintenance',
    subtitle: 'Users',
    view: 'user'
  };
  $scope.form = {};

  $scope.formatName = UtilService.formatName;
  $scope.hasPermission = UserService.hasPermission;
  $scope.branchService = BranchService;
  $scope.authService = UserService;

  $scope.filter = {
    q: "",
    userGroup: "",
    companyId: UserService.getCompanyId()
  };


  $scope.selected = {
    insuranceCompanies: [],
    userBranches: [],
    userRoles: [],
    userPrivileges: [],
    assignedCompanies: [],
    defaultCalendarUsers: [],
    calenderTemplateDefaultUsers: [],
    calenderTemplateDefaultBranches: [],
    calenderTemplates: [],
    allBranches: false
  };

  $scope.users = [];
  $scope.branches = [];
  $scope.allUsers = [];
  $scope.canHaveAppointmentUsers = [];
  $scope.colorClass = '#000000';
  $scope.exporting = false;
  $scope.userPrivilegesLoaded = false;

  SystemSettingFactory.findBySectionAndField({
    section: "general",
    field: "nylas_enabled"
  }).$promise.then(function (response) {
    $scope.showNylas = response.value === 'Y';
  });

  CompanyFactory.query().$promise.then(function (companies) {
    $scope.companies = companies;
  });
  InsuranceCompanyFactory.search({active: true}).$promise.then(function (insuranceCompanies) {
    $scope.insuranceCompanies = insuranceCompanies;
  });
  RoleFactory.query().$promise.then(function (roles) {
    $scope.roles = roles;
  });
  BranchService.loadUserBranchesPromise().then(function (response) {
    $scope.branches = response;
  });

  function init(first) {
    $scope.submitted = false;
    $scope.activeRecord = undefined;
    $scope.editingRecord = undefined;
    $scope.editing = false;
    $scope.saving = false;

    if (!first) {
      resetFormState();
    }
  }

  function resetFormState() {
    if ($scope.form && $scope.form.user) {
      $scope.form.user.$setUntouched();
      $scope.form.user.$setPristine();
      if ($scope.form.user.username) {
        $scope.form.user.username.$setViewValue("", "clear");
      }
    }
    $scope.submitted = false;
    $scope.saving = false;
  }

  $scope.loadUserData = function() {
    $scope.search();
    resetFormState();
  };

  $scope.$watch('roles', function (newValue, _oldValue) {
    if (newValue === undefined) return;
    if (!UserService.hasInventory()) {
      var index = -1;
      angular.forEach($scope.roles, function (role, i) {
        if (role.name === "Inventory") {
          index = i;
        }
      });
      if (index > -1) {
        $scope.roles.splice(index, 1);
      }
    }
  });


  $scope.setActive = function (record) {
    if (record) {
      $scope.userPrivilegesLoaded = false;
      $scope.activeRecord = angular.copy(record);
      $scope.activeRecord.password = "";
      $scope.passwordWasModified = false;
      $scope.selected.insuranceCompanies = [];
      $scope.selected.userBranches = [];
      $scope.selected.userRoles = [];
      $scope.selected.userPrivileges = [];
      $scope.selected.assignedCompanies = [];
      $scope.selected.defaultCalendarUsers = [];
      $scope.selected.calenderTemplates = [];
      $scope.selected.calenderTemplateDefaultUsers = [];
      $scope.selected.calenderTemplateDefaultBranches = [];
      $scope.selected.allBranches = false;

      UserInsuranceCompanyFactory.findByUserId({userId: $scope.activeRecord.id}).$promise.then(function (response) {
        angular.forEach(response, function (entry) {
          $scope.selected.insuranceCompanies.push(entry.insuranceCompany);
        });
      });
      UserBranchFactory.findUserBranches({userId: $scope.activeRecord.id}).$promise.then(function (response) {
        angular.forEach(response, function (branch) {
          $scope.selected.userBranches.push(branch);
        });
      });
      UserRoleFactory.findByUserId({userId: $scope.activeRecord.id}).$promise.then(function (response) {
        angular.forEach(response, function (entry) {
          $scope.selected.userRoles.push(entry.role);
        });
      });
      UserPrivilegeFactory.findByUserId({userId: $scope.activeRecord.id}).$promise.then(function (response) {
        angular.forEach(response, function (entry) {
          $scope.selected.userPrivileges.push(entry.privilege);
          if (entry.privilege.name === "branches_all") {
            $scope.selected.allBranches = true;
          }
        });
        $scope.userPrivilegesLoaded = true;
      });

      $scope.getCalendarTemplates($scope.activeRecord.id);

      if ($scope.activeRecord.companies) {
        $scope.selected.assignedCompanies = $scope.activeRecord.companies;
      }
      if ($scope.activeRecord.calendarDefaultUsers) {
        UserFactory.getUserCalendarDefaultUser({userId: $scope.activeRecord.id}).$promise.then(function (users) {
          angular.forEach(users, function (user) {
            $scope.selected.defaultCalendarUsers.push(user);
          })
        })
      }
    } else {
      init(false);
    }
    $scope.editing = false;
  };

  $scope.getCalendarTemplates = function(userId) {
    if(userId !== undefined) {
      CalendarTemplatesFactory.findByUserId({userId}).$promise.then(function(response) {
        $scope.selected.calenderTemplates = response;
        angular.forEach(response, function (template) {
          template.calenderTemplateDefaultUsers = [];
          template.calenderTemplateDefaultBranches = [];
          if(template.calendarDefaultUsers && template.calendarDefaultUsers.length > 0) {
            angular.forEach(template.calendarDefaultUsers.split(","), function (id) {
              UserFactory.get({id}).$promise.then(function(user) {
                template.calenderTemplateDefaultUsers.push(user)
              });
            })
          }
          if(template.calendarDefaultBranches && template.calendarDefaultBranches.length > 0) {
            UserBranchFactory.findUserBranches({userId}).$promise.then(function (response) {
              angular.forEach(response, function (branch) {
                angular.forEach(template.calendarDefaultBranches.split(","), function (id) {
                  if(id === branch.id.toString()) {
                    template.calenderTemplateDefaultBranches.push(branch);
                  }
                })
              });
            })
          }
        });
      })
    }
  }

  $scope.edit = function () {
    $scope.editing = true;
    $scope.editingRecord = angular.copy($scope.activeRecord);
    $scope.passwordWasModified = false;

    if(!$scope.activeRecord.sessionExpireMinutes){
      $scope.activeRecord.sessionExpireMinutes = UserService.getSessionExpireMinutes();
    }
  };

	$scope.newRecord = function () {
		$scope.editingRecord = undefined;
		$scope.activeRecord = {
			id: null,
			companyId: UserService.getCompanyId(),
			active: true,
			isMultiUser: false,
			isSuperAdmin: false
		};
		$scope.selected = {
      insuranceCompanies: [],
      userBranches: [],
      userPrivileges: [],
      userRoles: [],
      assignedCompanies: [],
      calenderTemplates: [],
      allBranches: false
    };
    $scope.passwordWasModified = true;
    if(!$scope.activeRecord.sessionExpireMinutes){
      $scope.activeRecord.sessionExpireMinutes = UserService.getSessionExpireMinutes();
    }
		$scope.editing = true;
		$scope.form.user.$setPristine();
	};

  $scope.cancel = function () {
    $scope.editing = false;
    $scope.submitted = false;
    $scope.setActive($scope.editingRecord);
  };

  $scope.trackPasswordChange = function() {
    $scope.passwordWasModified = true;
  };

  $scope.save = function (isValid) {
    $scope.saving = true;
    $scope.submitted = true;
    
    if (!isValid) {
      UtilService.displayAlert('danger', 'Please correct the errors in red.', '#header-alert-container');
      $scope.saving = false;
      return;
    }

    if (!$scope.activeRecord.id && !$scope.activeRecord.password) {
      UtilService.displayAlert('danger', 'Password is required for new users', '#alert-container');
      $scope.saving = false;
      return;
    }

    // Create a clean copy of the user to modify before sending
    var userToSave = angular.copy($scope.activeRecord);
    
    // Handle password for existing users
    if (userToSave.id) {
      // If password wasn't modified or is empty, don't send it
      if (!$scope.passwordWasModified || !userToSave.password) {
        delete userToSave.password;
      }
      // Even if modified, validate it meets requirements
      else if (userToSave.password && userToSave.password.length < 6) {
        $scope.error = "Password must be at least 6 characters long";
        $scope.saving = false;
        return;
      }
    }

    var userInsuranceCompanies = [];
    angular.forEach($scope.selected.insuranceCompanies, function (entry, _index) {
      var ic = {
        userId: userToSave.id,
        insuranceCompanyId: entry.id
      };
      userInsuranceCompanies.push(ic);
    });
    var userBranches = [];
    angular.forEach($scope.selected.userBranches, function (entry, _index) {
      var ub = {
        userId: userToSave.id,
        branchId: entry.id
      };
      userBranches.push(ub);
    });

    var userRoles = [];
    angular.forEach($scope.selected.userRoles, function (entry, _index) {
      var ur = {
        userId: userToSave.id,
        roleId: entry.id
      };
      userRoles.push(ur);
    });

    var tempCompanies = [];
    angular.forEach($scope.selected.assignedCompanies, function (entry, _index) {
      tempCompanies.push(entry.id)
    });
    var calendarDefaultUsers = [];
    angular.forEach($scope.selected.defaultCalendarUsers, function (entry, _index) {
      calendarDefaultUsers.push(entry.id);
    });
    userToSave.calendarDefaultUsers = calendarDefaultUsers.toString();
    var dto = {
      user: userToSave,
      insuranceCompanies: userInsuranceCompanies,
      assignedCompanies: tempCompanies,
      userBranches: userBranches,
      userRoles: userRoles
    };
    
    UserFactory.saveDTO(dto, function (saved) {
      
      // Clear any previous error messages
      $scope.errorMessage = null;
      
      var hasAllBranchesPrivilege = false;
      var privilegeId = null;
      angular.forEach($scope.selected.userPrivileges, function (entry) {
          if (entry.name === "branches_all") {
            hasAllBranchesPrivilege = true;
            privilegeId = entry.id;
          }
        }
      );

      if ($scope.selected.allBranches) {
        if(!hasAllBranchesPrivilege) {
          var privilege = {
            userId: saved.id
          };
          UserPrivilegeFactory.saveUserAllBranchesPrivilege(privilege);
        }
      } else if(hasAllBranchesPrivilege && !$scope.selected.allBranches){
        UserPrivilegeFactory.removeUserPrivilege({privilegeId: privilegeId, userId: saved.id});
      }

      // Update the active record with saved data
      $scope.setActive(saved);
      
      // Show success message in the main alert container
      UtilService.displayAlert('success', 'User saved successfully', '#header-alert-container');
      
      // Reset only the form state and saving flag, but keep the active record
      $scope.saving = false;
      $scope.submitted = false;
      $scope.editing = false;
      if ($scope.form && $scope.form.user) {
        $scope.form.user.$setPristine();
        $scope.form.user.$setUntouched();
      }
      
    }, function (error) {
      console.error('[MaintenanceUserCtrl] Save error:', error);
      $scope.errorMessage = error.data && error.data.message ? error.data.message : 'Unexpected response from server';
      UtilService.displayAlert('danger', $scope.errorMessage, '#header-alert-container');
      
      // Reset saving state on error but keep form state
      $scope.saving = false;
    });
  };

  $scope.cannotEverDelete = function () {
    return true;
  };

  $scope.delete = function () {
    alert("Deleting users from within Nymbl is not allowed at this time.");
    // if (confirm('Are you sure you want to delete the current user?')) {
    // 	$scope.editing = false;
    // 	var id = $scope.activeRecord.id;
    // 	UserFactory.delete({id: id}, function () {
    // 		$scope.activeRecord = undefined;
    // 		$scope.filter = {
    // 			searchTerm: "",
    // 			userGroup: ""
    // 		};
    // 		$scope.search();
    // 		$scope.form.user.$setPristine();
    // 	});
    // }
  };

  $scope.openPrivileges = function () {
    var modalInstance = $uibModal.open({
      animation: false,
      templateUrl: 'permissionModal.html',
      controller: 'PermissionModalCtrl',
      backdrop: 'static',
      keyboard: false,
      resolve: {
        privileges: function () {
          return $scope.selected.userPrivileges;
        }
      }
    });

    modalInstance.result.then(function () {
      var selectedPrivileges = $filter('filter')(PermissionService.getPrivileges(), {selected: true});
      angular.forEach(selectedPrivileges, function (_entry, index) {
        delete selectedPrivileges[index].selected;
      });
      $scope.saving = true;
      UserFactory.savePrivileges({userId: $scope.activeRecord.id}, selectedPrivileges, function (_response) {
        $scope.selected.userPrivileges = selectedPrivileges;
        resetFormState();
        $scope.search();
      }, function(error) {
        console.error('Error saving privileges:', error);
        $scope.saving = false;
        UtilService.displayAlert('danger', 'Failed to save privileges', '#header-alert-container');
      });
    });
  };

  $scope.openNotifications = function () {
    var modalInstance = $uibModal.open({
      animation: false,
      templateUrl: 'notificationPreferencesModal.html',
      controller: 'NotificationPreferencesModalCtrl',
      backdrop: 'static',
      keyboard: false,
      resolve: {
        notificationPreferences: function () {
          return $scope.activeRecord.notificationTypes;
        }
      }
    });

    modalInstance.result.then(function (notificationTypes) {
      var selectedNotificationTypes = $filter('filter')(notificationTypes, {selected: true});
      angular.forEach(selectedNotificationTypes, function (_entry, index) {
        delete selectedNotificationTypes[index].selected;
      });
      $scope.saving = true;
      UserFactory.saveNotificationTypes({userId: $scope.activeRecord.id}, selectedNotificationTypes, function (user) {
        $scope.activeRecord = user;
        resetFormState();
        $scope.search();
      }, function(error) {
        console.error('Error saving notification types:', error);
        $scope.saving = false;
        UtilService.displayAlert('danger', 'Failed to save notification preferences', '#header-alert-container');
      });
    });
  };

  $scope.openCalendarTemplateModal = function (calendarTemplate, activeUserId) {
    var modalInstance = $uibModal.open({
      templateUrl: 'views/tmpl/maintenance/calendar_template/_calendar_template_modal.html',
      controller: 'CalendarTemplateCtrl',
      backdrop: 'static',
      keyboard: false,
      size: 'md',
      resolve: {
        calendarTemplate: function () {
          return calendarTemplate;
        },
        activeUserId: function () {
          return activeUserId;
        }
      }
    });
    modalInstance.result.then(function () {
      $scope.search();
    });
  };

  $scope.canHaveAppointmentUsers = [];
  $scope.search = function (firstLoadAllUsers) {
    UserFactory.search($scope.filter).$promise.then(function (users) {
      //$scope.checkusers = users;
      $scope.users = [];
      if(firstLoadAllUsers){
        $scope.allUsers = []
      }
      angular.forEach(users, function (user, _index) {
        if (!user.isSuperAdmin) {
          $scope.users.push(user);
          if(firstLoadAllUsers && user.active){
            $scope.allUsers.push(user);
          }
        }
        if (!user.isSuperAdmin && user.canHaveAppointments) {
          $scope.canHaveAppointmentUsers.push(user);
        }
      });
      fetchActiveRecord();
    });

  };

  function fetchActiveRecord() {
    if ($scope.activeRecord) {
      UserFactory.get({id: $scope.activeRecord.id}).$promise.then(function (user) {
        $scope.activeRecord = user;
        $scope.activeRecord.password = "";
        $scope.passwordWasModified = false;
      });
      $scope.getCalendarTemplates($scope.activeRecord.id);
    }
  }

  $scope.resetAuthCode = function(user) {
    UserFactory.sendAuthCode(user).$promise.then(function (authUser) {
      $scope.activeRecord = authUser;
      alert('Success! A new code has been sent to the user\'s device. Ask user to refresh login page before entering new code');
    }).catch(function (error) {
      console.log(error);
      alert('Problem with sending alert');
    });
  };

  $scope.exportUsers = function() {
    $scope.exporting = true;
    UserFactory.exportUsers({companyId: [UserService.getCompanyId()]}).$promise.then(function (response) {
      $scope.exporting = false;
      var element = document.createElement('a');
      element.setAttribute('href', 'data:text/plain;charset=utf-8,' + encodeURIComponent(response.data));
      element.setAttribute('download', 'users.csv');

      element.style.display = 'none';
      document.body.appendChild(element);
      element.click();
      document.body.removeChild(element);

    }, function (error) {
      console.log(error);
    });
  };

  $scope.search(true);
}
