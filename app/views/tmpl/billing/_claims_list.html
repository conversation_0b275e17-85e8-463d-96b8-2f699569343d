<tr class="text-sm" data-id="{{dto.claim.id}}" ng-show="claims.length" ng-repeat="dto in claims">
    <td>
        <div class="input-group">
            <!--<input type="checkbox" class="generate-claim-file mt-2" data-claim-id="{{dto.claim.id}}"-->
            <!--ng-disabled="claimStatus[dto.claim.status]['disable']" ng-click="toggleChecked()">-->
            <input type="checkbox" class="generate-claim-file mt-2" data-claim-id="{{dto.claim.id}}"
                   ng-click="toggleChecked()">
        </div>
    </td>
    <td class="claim_id">
        <a role="button" class="text-primary text-md text-strong m-0 mr-3"
           ui-sref="app.billing.claim({ claimId: dto.claim.id })" target="_blank">{{dto.claim.id}}
            <i class="fa fa-bell" ng-show="dto.followUpTasks"></i>
        </a>
    </td>
    <td class="patient_name text-nowrap">
        <a ui-sref="app.patient.profile({patientId: dto.claim.prescription.patientId})" target="_blank">
            # {{dto.claim.prescription.patientId}} {{utilService.formatName(dto.claim.prescription.patient, "LF")}}
            ({{dto.claim.prescription.deviceType.name}})
        </a>
    </td>
    <td ng-if="!billerCodeEnabled" class="company_name text-nowrap"
        ng-init="pi = dto.claim.responsiblePatientInsurance"
        ng-bind-html="(pi ? pi.insuranceCompany.name + (!!dto.payerId ? ' - ' + dto.payerId: '') : 'Patient Responsibility')">
    </td>
    <td ng-if="billerCodeEnabled" class="company_name text-nowrap"
        ng-init="pi = dto.claim.responsiblePatientInsurance"
        ng-bind-html="(pi ? pi.insuranceCompany.name + (!!dto.payerId ? ' - ' + dto.payerId: '') + ' (' + pi.insuranceCompany.billerCode + ')' : 'Patient Responsibility')">
    </td>
    <td class="date_of_service text-nowrap">{{ moment(dto.claim.dateOfService).format('MM/DD/YYYY') }}</td>
    <td class="branch_name" ng-bind-html="dto.claim.billingBranch.name"></td>
    <td class="status" ng-bind-html="dto.claim.nymblStatus.name"></td>
    <td class="total">{{ dto.claim.totalClaimAmount | currency:'$':2 }}</td>
    <td class="balance">{{ (dto.claim.totalClaimBalance + dto.claim.totalPtResponsibilityBalance) | currency:'$':2 }}</td>
    <td class="submission_age"
        ng-bind-html="dto.claimSubmissionDate ? moment().diff(dto.claimSubmissionDate, 'days') + '-' + dto.submissionType.charAt(0) : ''">
        /td>
    <td class="applied_age">{{ dto.appliedPaymentDate ? moment().diff(dto.appliedPaymentDate, 'days') : '' }}</td>
    <td class="claim_status_age">{{ dto.daysInClaimStatus }}</td>
    <td class="user" ng-bind-html="utilService.formatName(dto.claim.user, 'LF')"></td>
    <td>
        <input class="print-claim-file" type="checkbox" ng-model="checked[dto.claim.id]"/>
    </td>
    <td>
        <a href role="button"
           class="action-link text-warning text-strong text-uppercase text-xs ml-5"
           ng-if="userService.hasPermission('claim_delete') && !exporting"
           ng-click="deleteClaim(dto.claim)">
            <i class="fa fa-trash fa-2x"></i>
        </a>
    </td>
    <td>
        <audit-button entity="claim" ng-model="dto.claim.id" type="icon" showlabel="{{false}}"></audit-button>
    </td>
</tr>
<tr ng-hide="claims.length">
    <td colspan="14" class="bg-warning" ng-show="!loading">No claims found.</td>
</tr>
