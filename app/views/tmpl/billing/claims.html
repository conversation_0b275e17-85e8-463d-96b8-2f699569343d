<div class="page page-forms-common">
    <!-- page header -->
    <div class="pageheader">
        <h2>{{page.title}} <span ng-bind-html="page.subtitle"></span></h2>
        <div id="header-alert-container" class="alert" style="display: none;"></div>
        <div id="write-off-alert-container" class="alert" style="display: none;"></div>
        <div class="page-bar">
            <ul class="page-breadcrumb">
                <li>
                    <a branch-dropdown></a>
                </li>
                <li>
                    <a ui-sref="app.billing.claims">Billing</a>
                </li>
                <li>
                    <a ui-sref="app.billing.claims">{{page.subtitle}}</a>
                </li>
            </ul>
            <div ng-show="followUpChecked" class="page-toolbar">
                <a href="javascript:" class="btn btn-lightred no-border" daterangepicker="rangeOptions"
                   date-begin="startDate" date-end="endDate">
                    <i class="fa fa-calendar"></i>&nbsp;&nbsp;
                    <span>{{startDate}} - {{endDate}}</span>&nbsp;&nbsp;
                    <i class="fa fa-angle-down"></i>
                </a>
            </div>
        </div>
    </div>
    <div id="filter-form" class="panel panel-primary mb-10">
        <div class="panel-body pt-10 pb-10">
            <div class="row">
                <div class="form-group col-sm-3">
                    <div id="patient-class-handler" class="select">
                        <label class="input-label">Patient</label>
                        <input id="filter_patient" name="patient" type="text" size="20"
                               class="form-control chosen-select text-uppercase"
                               placeholder="Search Patient by Name, Id, SSN, or DOB (MMDDYYYY)"
                               uib-typeahead="selected as utilService.formatNameWithDOB(patient) for patient in patientService.getPatients($viewValue)"
                               ng-model="filter.patient"
                               typeahead-on-select="selectPatient($item)" typeahead-min-length="2"
                               typeahead-template-url="patientWithDOBSearchTemplate.html"
                               typeahead-wait-ms="500" autocomplete="off" ng-change="deselectPatient()"
                               ng-disabled="hasClaimId"/>
                    </div>
                </div>
                <div class="col-sm-3 form-group">
                    <div id="filter-branch-id-class-handler" class="select">
                        <label>Billing Branch</label>
                        <select id="filter_branch" chosen class="form-control input-sm chosen-select"
                                ng-model="filter.branchId"
                                ng-options="b.id as b.name for b in branchService.userBranches | orderBy : 'name'"
                                ng-disabled="hasClaimId">
                            <option value="" ng-disabled="!branchService.enableFilterAll()">All Branches</option>
                        </select>
                    </div>
                </div>
                <div class="col-sm-2 form-group">
                    <div id="filter-user-id-class-handler" class="select">
                        <label>User</label>
                        <select name="filter_user_id"
                                id="filter_user_id"
                                chosen
                                class="form-control input-sm chosen-select"
                                ng-model="filter.userId"
                                ng-cloak
                                ng-options="user.id as utilService.formatName(user, 'LFMiC') for user in users | orderBy:'lastName'"
                                ng-disabled="hasClaimId">
                            <option value="">All Users</option>
                        </select>
                    </div>
                </div>
                <div class="col-sm-2 form-group mb-0">
                    <label for="startDate">Date of Service Between</label>
                    <p class="input-group">
                        <input type="text"
                               id="startDate"
                               name="startDate"
                               uib-datepicker-popup="MM/dd/yyyy"
                               class="form-control"
                               ng-model="filter.dosStartDate"
                               is-open="calendar.opened.dosStartDate"
                               datepicker-options="calendar.dateOptions"
                               close-text="Close" readonly
                               as-date ng-cloak>
                        <span class="input-group-btn">
                            <button type="button" class="btn btn-default" ng-click="calendar.open($event, 'dosStartDate')"><i class="fa fa-calendar"></i>
                            </button>
                        </span>
                    </p>
                </div>
                <div class="col-sm-2 form-group mb-0">
                    <label for="endDate">And</label>
                    <p class="input-group">
                        <input type="text"
                               id="endDate"
                               name="endDate"
                               uib-datepicker-popup="MM/dd/yyyy"
                               class="form-control"
                               ng-model="filter.dosEndDate"
                               is-open="calendar.opened.dosEndDate"
                               datepicker-options="calendar.dateOptions"
                               close-text="Close"
                               readonly
                               as-date
                               ng-cloak>
                        <span class="input-group-btn">
                            <button type="button" class="btn btn-default" ng-click="calendar.open($event, 'dosEndDate')"><i class="fa fa-calendar"></i></button>
                        </span>
                    </p>
                </div>
            </div>
            <div class="row">
                <div class="col-sm-4 form-group">
                    <div id="filter-insurance-company-id-class-handler" class="select">
                        <label>Insurance Company</label>
                        <select name="filter_insurance_company_id"
                                id="filter_insurance_company_id"
                                chosen
                                class="form-control input-sm chosen-select"
                                ng-model="filter.insuranceCompanyId"
                                ng-cloak
                                ng-options="insuranceCompany.id as insuranceCompany.name for insuranceCompany in insuranceCompanies"
                                ng-disabled="hasClaimId">
                            <option value="">All Insurance Companies</option>
                        </select>
                    </div>
                </div>
                <!--<div class="col-sm-4 form-group">-->
                <!--<div id="filter-status-id-class-handler" class="select">-->
                <!--<label>Status</label>-->
                <!--<select name="filter_claim_status"-->
                <!--id="filter_claim_status"-->
                <!--chosen-->
                <!--class="form-control input-sm chosen-select"-->
                <!--ng-model="filter.status"-->
                <!--ng-cloak-->
                <!--ng-options="key as data.text for (key, data) in claimStatus">-->
                <!--<option value="">All Status</option>-->
                <!--</select>-->
                <!--</div>-->
                <!--</div>-->
                <div class="col-sm-4 form-group">
                    <div id="filter-status-id-class-handler" class="select">
                        <label>Status</label>
                        <select name="filter_claim_status"
                                id="filter_claim_status"
                                chosen
                                class="form-control input-sm chosen-select"
                                ng-model="filter.nymblStatusId"
                                ng-cloak
                                ng-options="status.id as status.name for status in claimStatuses"
                                ng-disabled="hasClaimId">
                            <option value="">All Status</option>
                        </select>
                    </div>
                </div>
                <div class="col-sm-1">
                    <div class="form-group mb-0">
                        <div align="center">
                            <label>Unresolved</label>
                            <div class="col-sm-12 pl-0">
                                <label class="checkbox checkbox-custom">
                                    <input id="filter_unresolved" type="checkbox"
                                           ng-model="showUnresolved"
                                           ng-disabled="hasClaimId">
                                    <i></i>
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-sm-1">
                    <div class="form-group mb-0">
                        <div align="center">
                            <label>Follow Up</label>
                            <div class="col-sm-12 pl-0">
                                <label class="checkbox checkbox-custom">
                                    <input id="filter_follow_up" type="checkbox"
                                           ng-model="followUpChecked"
                                           ng-disabled="hasClaimId">
                                    <i></i>
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-sm-2 form-group m-0">
                    <label>Matches Found</label>
                    <input type="text" id="matches" class="form-control input-sm" value="{{ totalClaims }}" disabled>
                </div>
            </div>
            <div class="row">
                <div class="col-sm-1 form-group">
                    <label>Claim ID</label>
                    <input id="filter_claim_id" type="number"
                           class="form-control input-sm"
                           ng-model="filter.claimId">
                </div>
                <div class="col-sm-1 form-group">
                    <label>{{utilService.prescriptionAbbreviation}} ID</label>
                    <input id="filter_prescription_id" type="number"
                           class="form-control input-sm"
                           ng-model="filter.prescriptionId">
                </div>
                <div class="col-sm-10 form-group text-right">
                    <br>
                    <button class="btn btn-rounded btn-sm btn-danger mr-10"
                            style="visibility: hidden"
                            id="clear"
                            ng-click="clearFilters('all')"
                            ng-disabled="loading">
                        <i class="fa fa-search"></i>Clear
                    </button>
                    <button class="btn btn-rounded btn-sm btn-default"
                            id="search"
                            ng-click="search()"
                            ng-disabled="loading || exporting">
                        <i class="fa fa-search"></i>Search
                    </button>
                </div>
            </div>
        </div>
    </div>
    <div ng-if="!loading">
        <div id="alert-container" class="alert" style="display: none;"></div>
        <div class="row mt-15 mb-15">
            <div class="col-sm-12">
                <div class="form-group text-right mb-0">
                    <button type="button" id="export-claims" class="btn btn-rounded btn-sm btn-success"
                            data-loading-text="<i class='fa fa-spinner fa-spin fa-pulse'></i> Exporting..."
                            ng-click="exportClaims()">Export to Excel
                    </button>
                    <button type="button" id="bulk-update-status" class="btn btn-rounded btn-sm btn-warning"
                            data-loading-text="<i class='fa fa-spinner fa-spin fa-pulse'></i> Sending..." disabled
                            ng-click="bulkUpdateStatus()">Update Status
                    </button>
                    <button type="button" id="reassign-claim" class="btn btn-rounded btn-sm btn-warning"
                            data-loading-text="<i class='fa fa-spinner fa-spin fa-pulse'></i> Sending..." disabled
                            ng-click="reassignClaim()">Reassign Claim
                    </button>
                    <button type="button" id="print-payment-invoice" class="btn btn-rounded btn-sm btn-info"
                            ng-disabled="loading || exporting"
                            ng-click="printPaymentInvoice()">Print Invoice
                    </button>
                    <button type="button" id="send-files" class="btn btn-rounded btn-sm btn-primary"
                            data-loading-text="<i class='fa fa-spinner fa-spin fa-pulse'></i> Sending..." disabled
                            ng-click="sendClaimFile()">Send Claim File
                    </button>
                    <button type="button" id="write-off" class="btn btn-rounded btn-sm btn-primary"
                            data-loading-text="<i class='fa fa-spinner fa-spin fa-pulse'></i> Processing..." disabled
                            ng-click="writeOff()">Write Off Claims
                    </button>
                </div>
            </div>
        </div>
        <div ng-if="!loading && !exporting"
             ng-include src="'views/tmpl/_pagination_list.html'"></div>
        <div class="tile">
            <div class="tile-body p-0">
                <div class="claims-table-responsive" style="overflow: scroll;">
                    <table id="claim-list" class="table table-condensed"
                           style="table-layout: fixed; min-width: 1550px;">
                        <thead>
                        <tr class="bg-primary">
                            <th class="w-40">
                                <div class="input-group" style="line-height: 16px;">
                                    <input type="checkbox"
                                           id="check-all"
                                           ng-disabled="exporting"
                                           ng-model="checkAll.value"
                                           ng-click="toggleAllChecked()">
                                </div>
                            </th>
                            <th class="w-60" ng-click="sortBy('c.id')">Claim ID</th>
                            <th class="w-250" ng-click="sortBy('p.last_name')">
                                Patient/{{utilService.prescriptionNoun}}
                            </th>
                            <th ng-if="!billerCodeEnabled" class="w-200" ng-click="sortBy('ic.name')">Resp Payer-ID</th>
                            <th ng-if="billerCodeEnabled" class="w-200" ng-click="sortBy('ic.name')">Resp Payer-ID (Biller
                                Code)
                            </th>
                            <th class="w-100" ng-click="sortBy('c.date_of_service')">Date of Service</th>
                            <th class="w-90" ng-click="sortBy('b.name')">Claim Billing Branch</th>
                            <th class="w-100" ng-click="sortBy('c.nymbl_status_id')">Status</th>
                            <th class="w-100" ng-click="sortBy('c.total_claim_amount')">Total</th>
                            <th class="w-100" ng-click="sortBy('c.total_claim_balance')">Balance</th>
                            <th class="w-100" ng-click="sortBy('cs.submission_date')">Submission Aging<br>(days-type)
                            </th>
                            <th class="w-100" ng-click="sortBy('ap.applied_date')">Last Payment Aging (days)</th>
                            <th class="w-100">Claim Status Aging (days)</th>
                            <th class="w-120" ng-click="sortBy('n_user.last_name')">User</th>
                            <th class="">Print?</th>
                            <th></th>
                            <th></th>
                        </tr>
                        </thead>
                        <tbody ng-include="'views/tmpl/billing/_claims_list.html'"></tbody>
                    </table>
                </div>
            </div>
        </div>
        <div ng-if="!loading && !exporting"
             ng-include src="'views/tmpl/_pagination_list.html'"></div>
    </div>
    <div ng-if="loading" class="row text-center">
        <div class="col-sm-12">
            <i class="fa fa-spinner fa-spin fa-5x text-greensea pt-20"></i>
        </div>
    </div>
</div>


<ng-include src="'views/tmpl/patient/_reassign_user_modal.html'"></ng-include>
<ng-include src="'views/tmpl/patient/_bulk_update_status_modal.html'"></ng-include>
<ng-include src="'views/tmpl/claim/_send_claims_to_billing.html'"></ng-include>
