<ng-include src="'views/tmpl/maintenance/_view_denied.html'"></ng-include>
<div class="page page-forms-common" ng-if="hasPermission(page.view + '_view')">
    <!-- page header -->
    <div class="pageheader">
        <h2>{{page.title}} <span ng-bind-html="page.subtitle"></span></h2>
        <div id="header-alert-container" class="alert" style="display: none;"></div>
        <div class="page-bar">
            <ul class="page-breadcrumb">
                <li>
                    <a branch-dropdown></a>
                </li>
                <li>
                    <a href="javascript:">{{page.title}}</a>
                </li>
                <li>
                    <a ui-sref="app.maintenance.user">{{page.subtitle}}</a>
                </li>
            </ul>
        </div>
    </div>
    <div id="body-container">
        <div class="panel panel-greensea mb-10">
            <div class="panel-body pt-10 pb-10">
                <div class="row">
                    <div class="col-sm-4">
                        <div class="form-group m-0">
                            <label>Search by keywords</label>
                            <input id="search"
                                   type="text"
                                   class="form-control input-sm"
                                   ng-change="search()" ng-model-options='{ debounce: 500 }'
                                   ng-model="filter.q"
                                   ng-disabled="editing">
                        </div>
                    </div>
                    <div class="col-sm-2">
                        <div class="form-group m-0">
                            <label>User Group</label>
                            <select chosen disable-search="{{isMobile}}"
                                    class="form-control input-sm chosen-select"
                                    ng-change="search()"
                                    ng-model="filter.userGroup"
                                    ng-disabled="editing"
                                    ng-options="role.id as role.name for role in roles"
                            >
                                <option value="">All</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-sm-2">
                        <div class="form-group m-0">
                            <label>Matches displayed</label>
                            <input type="text"
                                   id="matches"
                                   class="form-control input-sm"
                                   ng-model="users.length"
                                   disabled>
                        </div>
                    </div>
<!--                    <div class="col-sm-1 col-lg-offset-2 mt-25 pr-3 pull-right">-->
<!--                        <button type="button" id="export-users" class="btn btn-rounded btn-sm btn-success"-->
<!--                                data-loading-text="<i class='fa fa-spinner fa-spin fa-pulse'></i> Exporting..."-->
<!--                                ng-click="exportUsers()">Export to Excel-->
<!--                        </button>-->
<!--                    </div>-->
                </div>
            </div>
        </div>
        <div class="row pl-15">
            <div class="col-sm-2 mb-20 custom-table">
                <div class="col-sm-12 header-fixed">
                    <div class="col-sm-12">Name (id)</div>
                </div>
                <div class="col-sm-12 custom-table-body">
                    <div ng-class="{active : activeRecord.id === user.id}" class="col-sm-12" ng-repeat="user in users"
                         ng-click="setActive(user)">
                        <div id="maintenance-user-active-{{user.id}}" class="col-sm-12 text-nowrap" ng-if="user.active">
                            {{formatName(user, 'LFMC') + ' (' + user.id + ')'}}
                        </div>
                        <div id="maintenance-user-inactive-{{user.id}}" class="col-sm-12 text-nowrap text-red"
                             ng-if="!user.active">
                            <del>{{formatName(user, 'LFMC')}} - INACTIVE</del>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-sm-10 mb-20">
                <form name="form.user"
                      ng-submit="save(form.user.$valid)"
                      class="col-sm-12 form-primary ajax-crud-form"
                      novalidate>
                    <div class="form-group text-right">
                        <audit-button entity="user" ng-model="activeRecord.id" showlabel="{{true}}"></audit-button>
                        <ng-include src="'views/tmpl/maintenance/_basic_action_bar.html'"></ng-include>
                        <button type="button"
                                class="btn btn-success btn-rounded"
                                ng-disabled="activeRecord === undefined || !userPrivilegesLoaded"
                                ng-click="openPrivileges()">Permissions
                        </button>
                        <button type="button"
                                class="btn btn-warning btn-rounded"
                                ng-disabled="activeRecord === undefined"
                                ng-click="openNotifications()">Notifications
                        </button>
                    </div>
                    <div id="alert-container" class="alert" style="display: none;"></div>
                    <legend class="form-section-header">User Information {{ activeRecord.id ? '(id: ' + activeRecord.id + ')' : "" }}</legend>
                    <div class="row">
                        <div class="col-sm-12 form-group">
                            <label>User Group</label><br>
                            <ui-select multiple ng-model="selected.userRoles" ng-disabled="!editing"
                                       close-on-select="false" style="height: auto; min-height: 30px; padding: .3em;">
                                <ui-select-match placeholder="Select roles...">{{''+$item.name}}</ui-select-match>
                                <ui-select-choices repeat="role in roles | filter:$select.search">
                                    <div ng-bind-html="''+role.name | highlight: $select.search"></div>
                                </ui-select-choices>
                            </ui-select>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-sm-2 form-group">
                            <label>Manager 1</label>
                            <select class="form-control input-sm chosen-select"
                                    ng-cloak
                                    ng-model="activeRecord.managerId"
                                    ng-options="c.id as utilService.formatName(c, 'LMFC') disable when c.id == activeRecord.id for c in allUsers"
                                    ng-disabled="!editing">
                                <option value=""></option>
                            </select>
                        </div>
                        <div class="col-sm-2 form-group">
                            <label>Manager 2</label>
                            <select class="form-control input-sm chosen-select"
                                    ng-cloak
                                    ng-model="activeRecord.managerTwoId"
                                    ng-options="c.id as utilService.formatName(c, 'LMFC') disable when c.id == activeRecord.id for c in allUsers"
                                    ng-disabled="!editing">
                                <option value=""></option>
                            </select>
                        </div>
                        <div class="col-sm-2 form-group">
                            <label>Manager 3</label>
                            <select class="form-control input-sm chosen-select"
                                    ng-cloak
                                    ng-model="activeRecord.managerThreeId"
                                    ng-options="c.id as utilService.formatName(c, 'LMFC') disable when c.id == activeRecord.id for c in allUsers"
                                    ng-disabled="!editing">
                                <option value=""></option>
                            </select>
                        </div>
                        <div class="col-sm-2 form-group"
                             ng-class="{ 'has-error' : submitted && form.user.npi.$invalid, 'has-success' : submitted && form.user.npi.$valid}">
                            <label>NPI</label>
                            <input type="text"
                                   class="form-control input-sm"
                                   ng-pattern="/^\d{10}$/"
                                   ng-disabled="!editing"
                                   ng-model="activeRecord.npi">
                            <div ng-messages="form.user.npi.$error" role="alert" ng-show="submitted">
                                <div class="help-block" ng-message="pattern">Invalid format.</div>
                            </div>
                        </div>
                        <div class="col-sm-2 form-group">
                            <label>&nbsp;</label><br>
                            <label class="checkbox-inline checkbox-custom">
                                <input type="checkbox"
                                       value="1"
                                       ng-disabled="!editing || !hasPermission('user_delete')"
                                       ng-model="activeRecord.active"><i></i> Active
                            </label>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-sm-3 form-group"
                             ng-class="{
                                'has-error' : (form.user.$error.usernameExists && !form.user.username.$pristine) || (submitted && form.user.username.$invalid),
                                'has-success' : submitted && form.user.username.$valid
                            }">
                            <label for="username">
                                Username
                            </label>
                            <i class="tooltips text-strong pull-right pt-5"
                               ng-class="{'icon-check text-greensea': submitted && form.user.username.$valid, 'icon-close text-red': (form.user.$error.usernameExists && !form.user.username.$pristine) || (submitted && form.user.username.$invalid)}"
                               data-toggle="tooltip" data-placement="top"
                               title="{{(form.user.$error.usernameExists && !form.user.username.$pristine) || (submitted && form.user.username.$invalid) ? 'Username is not available, already taken' : ''}}"></i>
                            <input type="text"
                                   id="username"
                                   name="username"
                                   class="form-control input-sm"
                                   required
                                   ng-disabled="!editing"
                                   username-available
                                   ng-model-options="{ allowInvalid: true, updateOn: 'default blur clear', debounce: {'default': 500, 'blur': 0, clear: '0'} }"
                                   ng-model="activeRecord.username"
                                   data-lpignore="true">
                            <div ng-show="form.user.$pending.usernameExists"><i class="fa fa-spinner fa-spin"></i>Checking...
                            </div>
                            <div ng-messages="form.user.username.$error" role="alert" ng-show="submitted">
                                <div class="help-block" ng-message="required">Username is required.</div>
                                <div class="help-block" ng-message="usernameExists">This username is already taken</div>
                            </div>
                        </div>
                        <div class="col-sm-3 form-group"
                             ng-class="{ 'has-error' : submitted && form.user.password.$invalid && (!activeRecord.id || activeRecord.password !== '12345678'), 'has-success' : submitted && form.user.password.$valid && activeRecord.password !== '12345678'}">
                            <label>Password</label>
                            <div id="maintenance-user-alert-container" class="alert" style="display: none;"></div>
                            <input type="password" 
                                   name="password" 
                                   class="form-control input-sm" 
                                   ng-required="!activeRecord.id"
                                   ng-disabled="!editing" 
                                   ng-model="activeRecord.password" 
                                   ng-change="trackPasswordChange()"
                                   data-lpignore="true"
                                   minlength="6"
                                   maxlength="20"
                                   placeholder="{{activeRecord.id ? '••••••••' : 'Enter password'}}">
                            <div ng-messages="form.user.password.$error" role="alert" ng-show="submitted || (activeRecord.password && form.user.password.$invalid)">
                                <div class="help-block" ng-message="required">Password is required for new users.</div>
                                <div class="help-block" ng-message="maxlength">Password has a maximum length of 20 characters.</div>
                                <div class="help-block" ng-message="minlength">Password has a minimum length of 6 characters.</div>
                            </div>
                        </div>
                        <div class="col-sm-4 form-group"
                             ng-class="{ 'has-error' : submitted && form.user.email.$invalid, 'has-success' : submitted && form.user.email.$valid}">
                            <label>Email</label>
                            <input type="email" name="email" class="form-control input-sm" ng-disabled="!editing"
                                   required
                                   ng-model="activeRecord.email"
                                   data-lpignore="true">
                            <div ng-messages="form.user.email.$error" role="alert" ng-show="submitted">
                                <div class="help-block" ng-message="email">Invalid format.</div>
                                <div class="help-block" ng-message="required">Email is required.</div>
                            </div>
                        </div>
                        <div class="col-sm-2 form-group"
                             ng-class="{ 'has-error' : submitted && form.user.phone_number.$invalid, 'has-success' : submitted && form.user.phone_number.$valid}">
                            <label>Cell Phone Number</label>
                            <input type="text"
                                   name="phone_number"
                                   class="form-control input-sm"
                                   ui-mask-placeholder
                                   ui-mask-placeholder-char="_"
                                   ui-mask="(************* Ext. ?9?9?9?9?9"
                                   ui-options="{clearOnBlur: false}"
                                   ng-model="activeRecord.phoneNumber"
                                   ng-disabled="!editing"
                                   ng-cloak>
                            <div ng-messages="form.user.phoneNumber.$error" role="alert" ng-show="submitted">
                                <div class="help-block" ng-message="pattern">Invalid format.</div>
                            </div>
                        </div>
                        <div class="form-group col-sm-3">
                            <label>SMS Compliant Cell Number</label>
                            <input type="text" class="form-control" ng-model="activeRecord.smsCompliantCell" readonly>
                        </div>
                        <div class="form-group col-sm-3"
                             ng-hide="activeRecord.smsCompliantCell==true || (activeRecord.smsCompliantCell==false && (activeRecord.phoneNumber == null || (activeRecord.phoneNumber != null && activeRecord.phoneNumber.length == 0)))">
                            <label>Error Code for Non-SMS Compliant Cell</label>
                            <input type="text" class="form-control" ng-model="activeRecord.smsCellErrorCode" readonly>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-sm-3 form-group"
                             ng-class="{ 'has-error' : submitted && form.user.last_name.$invalid , 'has-success' : submitted && form.user.last_name.$valid}">
                            <label>Last Name</label>
                            <input type="text"
                                   name="last_name"
                                   class="form-control input-sm"
                                   required
                                   ng-disabled="!editing"
                                   ng-model="activeRecord.lastName"
                                   data-lpignore="true">
                            <div ng-messages="form.user.last_name.$error" role="alert" ng-show="submitted">
                                <div class="help-block" ng-message="required">Last name is required.</div>
                            </div>
                        </div>
                        <div class="col-sm-3 form-group"
                             ng-class="{ 'has-error' : submitted && form.user.first_name.$invalid , 'has-success' : submitted && form.user.first_name.$valid}">
                            <label>First Name</label>
                            <input type="text"
                                   name="first_name"
                                   class="form-control input-sm"
                                   required
                                   ng-disabled="!editing"
                                   ng-model="activeRecord.firstName"
                                   data-lpignore="true">
                            <div ng-messages="form.user.first_name.$error" role="alert" ng-show="submitted">
                                <div class="help-block" ng-message="required">First name is required.</div>
                            </div>
                        </div>
                        <div class="col-sm-3 form-group">
                            <label>Middle Name</label>
                            <input type="text"
                                   name="middle_name"
                                   class="form-control input-sm"
                                   ng-disabled="!editing"
                                   ng-model="activeRecord.middleName">
                        </div>
                        <div class="col-sm-3 form-group">
                            <label>Credentials</label>
                            <input type="text"
                                   name="credentials"
                                   class="form-control input-sm"
                                   ng-disabled="!editing"
                                   ng-model="activeRecord.credentials">
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-sm-3 form-group"
                             ng-class="{ 'has-error' : submitted && form.user.ssn.$invalid, 'has-success' : submitted && form.user.ssn.$valid}">
                            <label>Social Security Number</label>
                            <input type="text"
                                   name="ssn"
                                   class="form-control input-sm"
                                   ng-pattern="/^\d{3}-\d{2}-\d{4}$/"
                                   ng-disabled="!editing"
                                   ng-model="activeRecord.ssn"
                                   ui-mask-placeholder
                                   ui-mask-placeholder-char="_"
                                   ui-mask="***********">
                            <div ng-messages="form.user.ssn.$error" role="alert" ng-show="submitted">
                                <div class="help-block" ng-message="pattern">Invalid format.</div>
                            </div>
                        </div>
                        <div class="col-sm-9 form-group">
                            <label>Assigned Insurances for WIP Alerts</label><br>
                            <ui-select multiple
                                       ng-model="selected.insuranceCompanies"
                                       ng-disabled="!editing"
                                       close-on-select="true" style="height: auto; min-height: 30px; padding: .3em;">
                                <ui-select-match placeholder="Select insurance companies...">
                                    {{''+$item.name}}
                                </ui-select-match>
                                <ui-select-choices
                                        repeat="insurance in insuranceCompanies | filter:$select.search | orderBy:'name'">
                                    <div ng-bind-html="''+insurance.name | highlight: $select.search"></div>
                                </ui-select-choices>
                            </ui-select>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-sm-3 form-group"
                             ng-class="{ 'has-error' : submitted && form.user.sessionExpireMinutes.$invalid, 'has-success' : submitted && form.user.sessionExpireMinutes.$valid}">
                            <label for="sessionExpireMinutes">Session Timeout (minutes)</label>
                            <input type="number" name="sessionExpireMinutes" id="sessionExpireMinutes" class="form-control input-sm" required min="{{5}}" max="{{authService.getSessionMaxExpireMinutes()}}"
                                   ng-disabled="!editing" ng-model="activeRecord.sessionExpireMinutes">
                            <div ng-messages="form.user.sessionExpireMinutes.$error" role="alert" ng-show="submitted">
                                <div class="help-block" ng-message="required">Timeout duration is required.</div>
                                <div class="help-block" ng-message="min">The timeout cannot be shorter than 5 minutes.</div>
                                <div class="help-block" ng-message="max">The timeout exceeds the maximum in system settings (or is over 120 minutes).</div>
                            </div>
                        </div>
                    </div>
                    <div class="row" ng-show="activeRecord && authService.getCurrentCompany().twoFactorAuth && authService.getCurrentUser().isSuperAdmin">
                        <div class="col-sm-3 form-group">
                            <label>Authentication Code</label>
                            <input type="text"
                                   name="authCode"
                                   class="form-control input-sm"
                                   disabled
                                   ng-model="activeRecord.authCode">
                        </div>
                        <div class="col-sm-3 form-group">
                            <label>Auth Expiration</label>
                            <input type="text"
                                   name="credentials"
                                   class="form-control input-sm"
                                   disabled
                                   ng-model="activeRecord.authCodeExpirationTime">
                        </div>
                        <div class="col-sm-2 form-group">
                            <button style="display: block" class="btn btn-rounded btn-warning" ng-click="resetAuthCode(activeRecord)">Reset Auth Code</button>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-sm-3 form-group">
                            <label>&nbsp;</label><br>
                            <label class="checkbox-inline checkbox-custom">
                                <input type="checkbox"
                                       value="1"
                                       ng-disabled="!editing"
                                       ng-model="activeRecord.canHaveAppointments"><i></i> Can Have Appointments?
                            </label>
                        </div>
                        <div class="col-sm-3 form-group">
                            <label>&nbsp;</label><br>
                            <label class="checkbox-inline checkbox-custom">
                                <input type="checkbox"
                                       value="1"
                                       ng-disabled="!editing"
                                       ng-click="selected.calendarDefaultUsers = ''"
                                       ng-model="activeRecord.viewAllUsers"><i></i> View All Users in Calendar
                            </label>
                        </div>
                        <div class="col-sm-3 form-group">
                            <label>&nbsp;</label><br>
                            <label class="checkbox-inline checkbox-custom">
                                <input type="checkbox"
                                       value="1"
                                       ng-disabled="!editing"
                                       ng-model="activeRecord.calendarAllBranches"><i></i> View All Branches in Calendar
                            </label>
                        </div>
                        <div class="row col-sm-3" ng-if="showNylas">
                            <label>&nbsp;</label><br>
                            <label class="checkbox-inline checkbox-custom">
                                <input type="checkbox"
                                       value="1"
                                       ng-disabled="!editing || !authService.isSuperAdmin()"
                                       ng-model="activeRecord.nylasActive"><i></i> Enable Calendar Integration
                            </label>
                        </div>
                    </div>
                    <div>
                        <div class="row">
                            <div class="col-lg-4 form-group form-section-header">
                                Template Name
                                <i ng-show="editing" class="fa fa-plus" class="pull-right" style="color:green"
                                   ng-click="openCalendarTemplateModal(null, activeRecord.id)"></i>
                                <i ng-show="!editing" class="fa fa-plus" class="pull-right"></i>
                            </div>
                            <div class="col-lg-4 form-group form-section-header">Branch(es)</div>
                            <div class="col-lg-4 form-group form-section-header">Default Users in Calendar</div>
                        </div>
                        <div class="row" ng-repeat="template in selected.calenderTemplates">
                            <div class="col-lg-4 form-group">
                                <input type="text"
                                       name="name"
                                       class="form-control input-sm"
                                       ng-disabled=true
                                       ng-model="template.name">
                            </div>
                            <div class="col-lg-4 form-group">
                                <ui-select multiple ng-model="template.calenderTemplateDefaultBranches" ng-disabled=true
                                           close-on-select="false" style="height: auto; min-height: 30px; padding: .3em;">
                                    <ui-select-match placeholder="Select branches...">{{''+$item.name}}</ui-select-match>
                                    <ui-select-choices repeat="branch in branchService.userBranches | filter:$select.search track by $index">
                                        <div ng-bind-html="''+branch.name | highlight: $select.search"></div>
                                    </ui-select-choices>
                                </ui-select>
                            </div>
                            <div class="col-lg-3 form-group">
                                <ui-select multiple ng-model="template.calenderTemplateDefaultUsers" ng-disabled=true
                                           close-on-select="false" style="height: auto; min-height: 30px; padding: .3em;">
                                    <ui-select-match placeholder="Select users...">{{''+$item.firstAndLastName}}</ui-select-match>
                                    <ui-select-choices repeat="user in selected.calenderDefaultUsers | filter:$select.search track by $index">
                                        <div ng-bind-html="''+user.firstAndLastName | highlight: $select.search"></div>
                                    </ui-select-choices>
                                </ui-select>
                            </div>
                            <i class="fa fa-pencil pull-right" ng-show="editing" style="color:green"
                               ng-click="openCalendarTemplateModal(template, activeRecord.id)">
                            </i>
                            <i class="fa fa-pencil pull-right" ng-show="!editing">
                            </i>
                        </div>
                    </div>
<!--                    <div class="row" ng-show="!activeRecord.viewAllUsers">-->
<!--                        <div class="col-sm-12 form-group">-->
<!--                            <div id="default-users-calendars-selector" class="select">-->
<!--                                <label>Default Users in Calendar</label>-->
<!--                                <select chosen disable-search="{{isMobile}}"-->
<!--                                        class="form-control"-->
<!--                                        multiple="multiple"-->
<!--                                        ng-model="selected.defaultCalendarUsers"-->
<!--                                        ng-options="user as user.firstName + ' ' + user.lastName for user in canHaveAppointmentUsers track by user.id">-->
<!--                                </select>-->
<!--                            </div>-->
<!--                        </div>-->
<!--                    </div>-->
                    <legend class="form-section-header">Branch Level Security</legend>
                    <div class="row">
                        <div class="col-sm-12 form-group">
                            <label>Branch Level Access</label>
                            &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                            <label class="checkbox-inline checkbox-custom">
                                <input type="checkbox"
                                       value="1"
                                       ng-disabled="!editing"
                                       ng-model="selected.allBranches"><i></i> All Branches
                            </label>
                            <br>
                            <ui-select multiple
                                       ng-model="selected.userBranches"
                                       ng-disabled="!editing"
                                       ng-show="!selected.allBranches"
                                       close-on-select="true"
                                       style="height: auto; min-height: 30px; padding: .3em;">
                                <ui-select-match placeholder="Select Branches...">{{''+$item.name}}</ui-select-match>
                                <ui-select-choices repeat="branch in branchService.branches | filter:$select.search">
                                    <div ng-bind-html="''+ branch.name| highlight: $select.search"></div>
                                </ui-select-choices>
                            </ui-select>
                        </div>
                    </div>
                    <legend class="form-section-header" ng-if="authService.isSuperAdmin()">
                        Company Level Security
                    </legend>
                    <div class="row" ng-if="authService.isSuperAdmin()">
                        <div class="col-sm-12 form-group">
                            <label>Company Level Access</label><br>
                            <ui-select multiple
                                       ng-model="selected.assignedCompanies"
                                       ng-disabled="!editing"
                                       close-on-select="false"
                                       style="height: auto; min-height: 30px; padding: .3em;">
                                <ui-select-match placeholder="Select Companies...">{{''+$item.name}}</ui-select-match>
                                <ui-select-choices repeat="company in companies | filter:$select.search">
                                    <div ng-bind-html="''+ company.name| highlight: $select.search"></div>
                                </ui-select-choices>
                            </ui-select>
                        </div>
                    </div>
                    <legend class="form-section-header">
                        Cascade Orthopedic Supply Punchout
                    </legend>
                    <div class="row">
                        <div class="col-sm-4 form-group">
                            <label>Cascade Account Id</label>
                            <input type="text"
                                   class="form-control input-sm"
                                   name="cascade_account_id"
                                   ng-readonly="!authService.isSuperAdmin()"
                                   ng-disabled="!editing"
                                   ng-model="activeRecord.cascadeAccountId">
                        </div>
                        <div class="col-sm-4 form-group">
                            <label>Cascade Password</label>
                            <input type="password"
                                   class="form-control input-sm"
                                   autocomplete="new-password"
                                   name="cascade_password"
                                   ng-readonly="!authService.isSuperAdmin()"
                                   ng-disabled="!editing"
                                   ng-model="activeRecord.cascadePassword">
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
<ng-include src="'views/tmpl/maintenance/_permission_modal.html'"></ng-include>
<ng-include src="'views/tmpl/maintenance/_notification_preferences_modal.html'"></ng-include>
