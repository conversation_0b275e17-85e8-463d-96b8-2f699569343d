<div class="page page-forms-common">
    <!-- page header -->
    <div class="pageheader">
        <h2>Billing
            <span>Claim</span>
        </h2>
        <div id="header-alert-container" class="alert" style="display: none;"></div>
        <div id="x12-alert-container" class="alert" style="display: none;"></div>
        <div class="page-bar">
            <ul class="page-breadcrumb">
                <li>
                    <a branch-dropdown>
                        <i class="fa fa-home"></i>
                        <span ng-bind-html="branch.name"></span>
                    </a>
                </li>
                <li>
                    <a ui-sref="app.billing.claims">Billing</a>
                </li>
                <li>
                    <a ui-sref="app.billing.claim">Claim #{{claim.id}}</a>
                </li>
                <li>
                    <a ui-sref="app.patient.profile({patientId: claim.prescription.patientId})">{{utilService.prescriptionAbbreviation}}
                        #{{claim.prescription.id}}</a>
                </li>
                <li>
                    <a ui-sref="app.patient.profile({patientId: claim.prescription.patientId})">Patient
                        #{{claim.prescription.patientId}} {{utilService.formatName(claim.prescription.patient,
                                "LFMi")}}</a>
                </li>
            </ul>
        </div>
    </div>
    <div class="body-container">
        <div class="row mr-0" ng-if="validationErrors.length">
            <div class="col-sm-12 alert alert-danger ml-15">
                <div class="form-group">
                    <ul>
                        <li ng-repeat="error in validationErrors" ng-bind-html="error"></li>
                    </ul>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-sm-12">
                <div class="col-sm-3 form-group" ng-if="userService.isSuperAdmin()">
                    <label class="checkbox-inline checkbox-custom pt-30">
                        <input id="nymbl_rcm" type="checkbox"
                               value="1"
                               ng-model="claim.nymblRcm"
                               ng-cloak>
                        <i></i><strong style="color: red; font-size: 16px"> Uses Nymbl RCM</strong>
                    </label>
                </div>
                <div class="form-group text-right mb-0">
                    <button ng-if="claimDefaults['format_837'] === 'x12'"
                            type="button"
                            id="download-837"
                            class="btn btn-rounded btn-sm btn-success"
                            ng-disabled="validationErrors.length"
                            ng-click="download()">Download x12 837
                    </button>
                    <button ng-show="billingCycleLockdown() &&
                                   (claim.totalPtResponsibilityBalance < 0 || claim.totalClaimBalance < 0) &&
                                   ((claim.totalClaimBalance === (claim.totalPtResponsibilityBalance * -1)) || (claim.totalPtResponsibilityBalance === (claim.totalClaimBalance * -1)))"
                            type="button"
                            id="zeroOutInsuranceAndPatientBalance"
                            class="btn btn-rounded btn-sm btn-success"
                            ng-click="zeroOutInsuranceAndPatientBalance()">Zero Ins/Patient Balance
                    </button>
                    <h5 ng-if="claim && !claim.prescription.patient.primaryBranchId"
                        style="color:red">Missing Patient Primary Branch Information</h5>
                    <button class="btn btn-sm btn-rounded btn-info" id="edit-prescription"
                            ng-click="openPickPatientPrimaryBranch(claim.prescription.patient.id)"
                            ng-if="!claim.prescription.patient.primaryBranchId">
                        <i class="fa fa-edit"></i>
                        Pick Patient Primary Branch
                    </button>
                    <button ng-if="userService.hasPermission('claim_balance_swap')"
                            type="button"
                            id="transfer"
                            class="btn btn-rounded btn-sm btn-danger"
                            ng-click="swapBalance()">Swap Balance
                    </button>
                    <button type="button"
                            id="print-payment-invoice"
                            class="btn btn-rounded btn-sm btn-warning"
                            ng-click="printPaymentInvoice()">Print
                    </button>
                    <button type="button"
                            id="save-claim"
                            class="btn btn-info btn-rounded btn-sm"
                            data-loading-text="<i class='fa fa-spinner fa-spin fa-pulse'></i> Saving..."
                            ng-click="updateClaim(false)">
                        <i class="fa fa-refresh"></i>
                        {{nextPayer.selected ? 'Update & Create Claim' : 'Update Claim'}}
                    </button>
                    <button type="button"
                            id="send-files"
                            class="btn btn-rounded btn-sm btn-primary"
                            data-loading-text="<i class='fa fa-spinner fa-spin fa-pulse'></i> Sending..."
                            ng-disabled="validationErrors.length"
                            ng-click="sendSingleClaimFile()">Send Claim File
                    </button>
                    <audit-button entity="claim" ng-model="claim.id" showlabel="{{true}}"></audit-button>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-sm-2 form-group">
                <label>Next payer</label>
                <select name="nextPayer"
                        id="nextPayer"
                        class="col-sm-10 form-control input-sm chosen-select"
                        ng-model="nextPayer.selected"
                        ng-options="nextPayer.patientInsuranceId as nextPayer.patientInsurance.insuranceCompany.name+' '+ (nextPayer.carrierType  ? ' (' + insuranceService.insuranceTypes[nextPayer.carrierType] + ')' : '') disable when nextPayer.disabled for nextPayer in nextPayer.list"
                        ng-cloak>
                    <!--ng-disabled="!claimHasPrimaryPayment()" - COMMENTED OUT FOR SCRUM--3425; deprecated due to GL-->
                </select>
            </div>
            <div class="col-sm-2 form-group">
                <label>Status</label>
                <select name="status"
                        id="status"
                        chosen
                        class="col-sm-10 form-control input-sm chosen-select"
                        ng-model="claim.nymblStatusId"
                        ng-cloak
                        ng-options="status.id as status.name disable when (status.id === 19 && submissions.length < 1) for status in claimStatuses">
                </select>
            </div>
            <div class="col-sm-2 form-group">
                <label>User</label>
                <select name="user_id"
                        id="user_id"
                        chosen
                        class="col-sm-10 form-control input-sm chosen-select"
                        ng-model="claim.userId"
                        ng-cloak
                        ng-change="userReassigned = true;"
                        ng-options="user.id as utilService.formatName(user, 'LFMiC') for user in users | orderBy:'lastName'">
                    <!--ng-disabled="claimStatus[claim.status]['disable'] > 1"-->
                </select>
            </div>
            <div class="col-sm-2 form-group">
                <label>
                    <span>Date of Service </span>
                    <i ng-if="(billingCycleLockdown() && !userService.isSuperAdmin()) || !dateOfServiceEdit"
                       class="fa fa-lock" uib-popover-template="'dosLockedPopover.html'"
                       popover-trigger="mouseenter"></i>
                </label>
                <div class="input-group">
                    <input type="text"
                           id="claim_dos"
                           name="claim_dos"
                           uib-datepicker-popup="MM/dd/yyyy"
                           class="form-control input-sm"
                           ng-model="claim.dateOfService"
                           is-open="dosCalendar.opened['dos']"
                           datepicker-options="dosCalendar.dateOptions"
                           close-text="Close" required
                           ng-required="true" ng-cloak
                           ng-disabled="(billingCycleLockdown() && !userService.isSuperAdmin()) || !dateOfServiceEdit"
                           as-date>
                    <span class="input-group-btn">
                        <button id="button_claim_dos" type="button"
                                class="btn btn-default btn-sm"
                                ng-disabled="(billingCycleLockdown() && !userService.isSuperAdmin()) || !dateOfServiceEdit"
                                ng-click="dosCalendar.open($event, 'dos')">
                                <i class="fa fa-calendar"></i>
                        </button>
                    </span>
                </div>
                <div class="help-block text-danger" ng-show="claim && dosPODWarning">Date of Service does not match the
                    signed date on the Proof of Delivery
                </div>
            </div>
            <div class="col-sm-2 form-group">
                <label>Resolved</label>
                <div class="input-group">
                    <input type="text"
                           id="date_resolved"
                           name="date_resolved"
                           uib-datepicker-popup="MM/dd/yyyy"
                           class="form-control input-sm"
                           ng-model="claim.dateResolved"
                           is-open="dateResolvedCalendar.opened['dateResolved']"
                           datepicker-options="dateResolvedCalendar.dateOptions"
                           close-text="Close"
                           ng-cloak
                           as-date>
                    <span class="input-group-btn" style="padding:0px !important">
                        <button id="button_resolved" type="button"
                                class="btn btn-default btn-sm"
                                ng-click="dateResolvedCalendar.open($event, 'dateResolved')">
                            <i class="fa fa-calendar"></i>
                        </button>
                    </span>
                </div>
            </div>
            <div class="col-sm-2 form-group">
                <label class="checkbox-inline checkbox-custom pt-25">
                    <input type="checkbox" name="printSalesTax" id="printSalesTax" value="1"
                           ng-model="printSalesTax"><i></i>
                    Include Sales Tax on Printed Invoice
                </label>
            </div>
        </div>
        <div class="row">
            <div class="col-sm-9">
                <div class="col-sm-3 form-group">
                    <label>Total Claim Amount</label>
                    <input id="total_claim_amount" type="number"
                           step="0.01"
                           class="form-control input-sm w-100"
                           ng-model="claimTotals.totalCharge"
                           ng-disabled="true" ng-readonly="true"/>
                </div>
                <div class="col-sm-3 form-group">
                    <label>Insurance Paid</label>
                    <input id="insurance_paid" type="number"
                           step="0.01"
                           class="form-control input-sm w-100"
                           ng-model="claimTotals.insurancePaid"
                           ng-disabled="true" ng-readonly="true"/>

                    <label>Patient Paid</label>
                    <input id="patient_paid" type="number"
                           step="0.01"
                           class="form-control input-sm w-100"
                           ng-model="claimTotals.patientPaid"
                           ng-disabled="true" ng-readonly="true"/>
                </div>
                <div class="col-sm-3 form-group">
                    <label>Insurance Adjustments</label>
                    <input id="insurance_adjustment" type="number"
                           step="0.01"
                           class="form-control input-sm w-100"
                           ng-model="claimTotals.insuranceAdjustments"
                           ng-disabled="true" ng-readonly="true"/>

                    <label>Patient Adjustments</label>
                    <input id="patient_adjustments" type="number"
                           step="0.01"
                           class="form-control input-sm w-100"
                           ng-model="claimTotals.patientAdjustments"
                           ng-disabled="true" ng-readonly="true"/>
                </div>
                <div class="col-sm-3 form-group">
                    <label>Insurance Balance</label>
                    <input id="insurance_balance" type="number"
                           step="0.01"
                           class="form-control input-sm w-100"
                           ng-model="claim.totalClaimBalance"
                           ng-change="updatePatientBalance()"
                           ng-disabled="accountingLocked && !userService.isSuperAdmin()"/>

                    <label>Patient Balance</label>
                    <input id="patient_balance" type="number"
                           step="0.01"
                           class="form-control input-sm w-100"
                           ng-model="claim.totalPtResponsibilityBalance"
                           ng-change="updateInsuranceBalance()"
                           ng-disabled="accountingLocked && !userService.isSuperAdmin()"/>
                </div>
            </div>
            <div class="col-sm-3">
                <div class="col-sm-10 form-group" ng-if="claimDefaults.use_single_claim === 'Y' && !billerCodeEnabled">
                    <label>Responsible For Insurance Balance</label>
                    <input id="responsiblePatientInsuranceName"
                           type="text"
                           ng-model="responsiblePatientInsuranceCompanyName"
                           name="responsiblePatientInsuranceCompanyName"
                           class="form-control input-sm" ng-disabled="true" ng-readonly="true"/>
                </div>
                <div class="col-sm-10 form-group" ng-if="claimDefaults.use_single_claim === 'Y' && billerCodeEnabled">
                    <label>Responsible For Insurance Balance</label>
                    <input id="responsiblePatientInsuranceNameBC"
                           type="text"
                           ng-model="responsiblePayerWithBillerCode"
                           name="responsiblePatientInsuranceNameBC"
                           class="form-control input-sm" ng-disabled="true" ng-readonly="true"/>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-sm-3 form-group">
                <label>Billing Branch Company Override</label>
                <input id="claim_billing_branch_name" type="text" value="{{claim.billingBranch.name}}"
                       name="claimBillingBranchName"
                       class="form-control input-sm" ng-disabled="true"
                       ng-if="!canOverrideClaimValues && billingCycleLockdown()"/>
                <select chosen disable-search="{{isMobile}}"
                        class="form-control input-sm chosen-select"
                        ng-model="claim.billingBranchId"
                        ng-cloak
                        ng-change="reloadPDFs(false, claim.patientInsuranceId, claim.otherPatientInsuranceId)"
                        ng-options="b.id as b.name for b in branchService.userBranches"
                        ng-if="canOverrideClaimValues || !billingCycleLockdown()">
                    <option value=""></option>
                </select>
            </div>
            <div class="col-sm-3 form-group">
                <label class="pull-left pl-0 pt-5 text-sm">Resubmission Code (Box 22)</label>
                <select name="resubmissionCode"
                        id="resubmissionCode"
                        chosen="{allow_single_deselect: true}"
                        class="form-control input-sm chosen-select"
                        ng-model="claim.resubmissionCode"
                        ng-cloak
                        ng-change="reloadPDFs(false, claim.patientInsuranceId, claim.otherPatientInsuranceId)"
                        ng-options="key as data.text for (key, data) in claimResubmissionCode">
                    <option value="">Select an Option</option>
                </select>
            </div>
            <div class="col-sm-2 form-group">
                <label>Original Ref Num (Box 22)</label>
                <input id="box22" class="form-control input-sm"
                       ng-model="claim.originalRefNum"
                       ng-cloak>
            </div>
            <div class="col-sm-2 form-group">
                <label>Additional Info. (Box 19)</label>
                <input id="box19" class="form-control input-sm"
                       ng-model="claim.additionalInfo"
                       ng-cloak>
            </div>
            <div class="col-sm-2 form-group" ng-show="!nextPayer.selected">
                <label>Create Manual Submission</label>
                <div class="input-group">
                    <label class="input-group-addon">
                        <input id="create_manual_submission" type="checkbox" value="1"
                               ng-disabled="moment(claim.dateOfService).isAfter(moment().format('YYYY-MM-DD'))"
                               ng-model="manualSubmission" ng-cloak>
                    </label>
                    <input type="text"
                           id="manualSubmissionDate"
                           name="manualSubmissionDate"
                           uib-datepicker-popup="MM/dd/yyyy"
                           class="form-control input-sm"
                           ng-model="manualClaimSubmission.submissionDate"
                           is-open="calendar.opened['manualSubmission']"
                           datepicker-options="calendar.dateOptions"
                           ng-disabled="!manualSubmission"
                           close-text="Close"
                           ng-cloak
                           as-date>
                    <span class="input-group-btn" style="padding:0px !important">
                        <button id="button_manual_submission_date" type="button"
                                class="btn btn-default btn-sm"
                                ng-disabled="!manualSubmission && !canOverrideDates"
                                ng-click="calendar.open($event, 'manualSubmission')">
                            <i class="fa fa-calendar"></i>
                        </button>
                    </span>
                </div>
                <div class="help-block text-danger"
                     ng-show="claim && moment(claim.dateOfService).isAfter(moment().format('YYYY-MM-DD'))">The Date of
                    Service is set to a future date.
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-sm-3 form-group" ng-hide="box27AcceptsAssignmentDisabled">
                <label class="checkbox-inline checkbox-custom pt-30">
                    <input id="box27" type="checkbox"
                           value="1"
                           ng-model="claim.acceptAssignment"
                           ng-cloak>
                    <i></i> Accept Assignment (Box 27)
                </label>
            </div>
            <div class="col-sm-3 form-group">
                <label>Overflow Narrative (80 characters)</label>
                <input id="box19a" class="form-control input-sm"
                       ng-model="claim.overflowNarrative"
                       ng-cloak>
            </div>
            <div class="col-sm-3 form-group" ng-if="claim_level_1500_overrides">
                <label for="form1500Claim">1500 Form Template Override (Electronic Claim File)</label>
                <div class="select">
                    <select chosen class="form-control input-sm chosen-select" id="form1500Claim"
                            name="form1500Claim"
                            disable-search="{{isMobile}}"
                            ng-model="claim.form1500TemplateId"
                            ng-change="updateFormTemplateId()"
                            ng-options="template.id as template.name for template in form1500Templates"
                            required>
                        <option value="">Default for Insurance</option>
                    </select>
                </div>
            </div>
            <div class="col-sm-4 form-group"
                 ng-if="claim.billingBranch.useRealTimeRulesEngine && !claim_level_1500_overrides">
            </div>
            <div class="col-sm-1 form-group"
                 ng-if="claim.billingBranch.useRealTimeRulesEngine && claim_level_1500_overrides">
            </div>
            <div class="col-sm-2 form-group" ng-if="claim.billingBranch.useRealTimeRulesEngine">
                <label class="checkbox-inline checkbox-custom pt-30">
                    <input id="override" type="checkbox"
                           value="1"
                           ng-model="claim.overrideRTRulesEngine"
                           ng-cloak>
                    <i></i> Override Claims Edit Rule Engine & Send Claim to Waystar
                </label>
            </div>
        </div>
        <div class="row">
            <div class="col-sm-12">
                <ul class="nav nav-tabs tabs-dark" id="claim-details-tabs">
                    <li role="presentation" id="patient-tab">
                        <a data-target="#patient" data-tab="patient" role="tab"
                           data-toggle="tab">{{utilService.prescriptionAbbreviation}} Details</a>
                    </li>
                    <li role="presentation" id="insurance-tab">
                        <a data-target="#insurance" data-tab="insurance" role="tab" data-toggle="tab">Insurances</a>
                    </li>
                    <li role="presentation" id="physician-tab">
                        <a data-target="#physician" data-tab="physician" role="tab" data-toggle="tab">Physicians</a>
                    </li>
                    <li role="presentation" id="codes-tab" class="active">
                        <a data-target="#codes" data-tab="codes" role="tab" data-toggle="tab">Codes</a>
                    </li>
                    <li role="presentation" id="submissions-tab" ng-if="!claimDefaults.use_single_claim">
                        <a data-target="#submissions" data-tab="submissions" role="tab"
                           data-toggle="tab">Submissions</a>
                    </li>
                    <li role="presentation" id="submissions-single-claim-tab" ng-if="claimDefaults.use_single_claim">
                        <a data-target="#submissions-single-claim" data-tab="submissions-single-claim" role="tab"
                           data-toggle="tab">Submissions</a>
                    </li>
                    <li role="presentation" id="followup-tab">
                        <a data-target="#followup" data-tab="followup" role="tab" data-toggle="tab">Follow Up Tasks</a>
                    </li>
                    <li role="presentation" id="print-view-tab" ng-if="showPrintView()">
                        <a data-target="#print-view" data-tab="print-view" role="tab" data-toggle="tab"
                           ng-click="display1500pdf('#file_1500_pdf')">Print View</a>
                    </li>
                    <li role="presentation" id="files-tab">
                        <a data-target="#files" data-tab="files" role="tab" data-toggle="tab"
                           ng-click="fileService.profileLoad(claim.prescription.patientId)">Files and Documents</a>
                    </li>
                    <li role="presentation" id="notes-tab">
                        <a data-target="#notes" data-tab="notes" role="tab" data-toggle="tab"
                           ng-click="noteService.profileLoad(claim.prescription.patient.id)">Notes</a>
                    </li>
                    <li role="presentation" id="th-tab">
                        <a data-target="#th" data-tab="th" role="tab" data-toggle="tab">Transaction History</a>
                    </li>
                    <li role="presentation" id="cr-tab">
                        <a data-target="#ccr" data-tab="th" role="tab" data-toggle="tab">Claim Response</a>
                    </li>
                </ul>
            </div>
        </div>
        <div class="row">
            <div class="col-sm-12">
                <div class="tab-content">
                    <div role="tabpanel" class="tab-pane" id="patient">
                        <div class="wrap-reset">
                            <ng-include src="'views/tmpl/claim/_patient.html'"></ng-include>
                        </div>
                    </div>
                    <div role="tabpanel" class="tab-pane" id="insurance">
                        <div class="wrap-reset">
                            <ng-include src="'views/tmpl/claim/_insurance.html'"></ng-include>
                        </div>
                    </div>
                    <div role="tabpanel" class="tab-pane" id="physician">
                        <div class="wrap-reset">
                            <ng-include src="'views/tmpl/claim/_physicians.html'"></ng-include>
                        </div>
                    </div>
                    <div role="tabpanel" class="tab-pane active" id="codes">
                        <div class="wrap-reset">
                            <ng-include src="'views/tmpl/claim/_codes.html'"></ng-include>
                        </div>
                    </div>
                    <div role="tabpanel" class="tab-pane" id="submissions">
                        <div class="wrap-reset">
                            <ng-include src="'views/tmpl/claim/_submissions.html'"></ng-include>
                        </div>
                    </div>
                    <div role="tabpanel" class="tab-pane" id="submissions-single-claim">
                        <div class="wrap-reset">
                            <ng-include src="'views/tmpl/claim/_submissions_single_claim.html'"></ng-include>
                        </div>
                    </div>
                    <div role="tabpanel" class="tab-pane" id="followup">
                        <div class="wrap-reset">
                            <ng-include src="'views/tmpl/claim/_follow_up.html'"></ng-include>
                        </div>
                    </div>
                    <div role="tabpanel" class="tab-pane" id="print-view">
                        <div class="wrap-reset">
                            <ng-include src="'views/tmpl/claim/_view_print_claim_index.html'"></ng-include>
                        </div>
                    </div>
                    <div role="tabpanel" class="tab-pane" id="files">
                        <div class="wrap-reset">
                            <ng-include src="'views/tmpl/claim/_files.html'"></ng-include>
                        </div>
                    </div>
                    <div role="tabpanel" class="tab-pane" id="notes">
                        <div class="wrap-reset">
                            <ng-include src="'views/tmpl/claim/_notes.html'"></ng-include>
                        </div>
                    </div>
                    <div role="tabpanel" class="tab-pane" id="th">
                        <div class="wrap-reset">
                            <ng-include src="'views/tmpl/claim/_transaction_history.html'"></ng-include>
                        </div>
                    </div>
                    <div role="tabpanel" class="tab-pane" id="ccr">
                        <div class="wrap-reset">
                            <ng-include src="'views/tmpl/claim/_claim_clearinghouse_response.html'"></ng-include>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<ng-include src="'views/tmpl/claim/_send_claims_to_billing.html'"></ng-include>

<script type="text/ng-template" id="dosLockedPopover.html">
    <div style="width: 250px; word-wrap: break-word;">
        <p ng-if="billingCycleLockdown()">- The Date of Service is in a locked period and cannot be changed.</p>
        <p ng-if="!billingCycleLockdown() && !dateOfServiceEdit">- The "Date of Service Edit" permission required.</p>
    </div>
</script>
