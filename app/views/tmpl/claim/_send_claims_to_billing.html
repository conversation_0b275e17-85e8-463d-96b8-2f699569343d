<script type="text/ng-template" id="_send_claims_to_billing.html" xmlns="http://www.w3.org/1999/html">
    <form name="sendClaimsToBillingForm" id="sendClaimsToBillingForm" class="ajax-crud-form" novalidate modal-movable>
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" ng-click="close()">
                    <i class="fa fa-times"></i>
                </button>
                <h4 class="modal-title">Submit Claims</h4>
            </div>
            <div class="modal-body bg-body">
                <div class="row mt-20 pl-15">
                    <h5>"Are you sure this Claim is ready to be submitted?" Please click “Submit” to submit Claim File.
                        Click “Cancel” to go back.</h5>
                    <div class="alt-table-responsive">
                        <table class="table table-condensed panel fs-12 b-solid b-primary b-2x">
                            <thead class="bg-primary">
                            <tr>
                                <th class="w-90">Claim ID</th>
                                <th class="w-90">Prescription ID</th>
                                <th class="w-100">Date of Service</th>
                            </tr>
                            </thead>
                            <tbody id="send-claims-to-billing-table-body">
                            <tr ng-show="claims.length" ng-repeat="claim in claims">
                                <td>{{claim.id}}</td>
                                <td>{{claim.prescriptionId}}</td>
                                <td>{{moment(claim.dateOfService).format('MM/DD/YYYY') }}</td>
                            </tr>
                            <tr ng-show="!claims.length">
                                <td colspan="3">No Claims Selected to Send.</td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            <footer ng-if="responseMessage !== undefined">{{responseMessage}} </footer>
            <div class="modal-footer">
                <div class="col-sm-6" style="padding-left: 0 !important;"></div>
                <div class="col-sm-3">
                    <button class="btn btn-rounded btn-sm btn-danger" ng-click="close()">
                        Cancel
                    </button>
                </div>
                <div class="col-sm-3">
                    <button type="submit"
                            class="btn btn-success btn-sm btn-rounded col-sm-12 pr-10"
                            id="send-to-billing-button"
                            name="send-to-billing-button"
                            ng-disabled = "!disableButton"
                            ng-click="sendClaimsToBilling()"
                            data-loading-text="<i class='fa fa-spinner fa-spin fa-pulse'></i> Processing...">
                        <span>Submit</span>
                    </button>
                </div>
            </div>
        </div>
    </form>
</script>
